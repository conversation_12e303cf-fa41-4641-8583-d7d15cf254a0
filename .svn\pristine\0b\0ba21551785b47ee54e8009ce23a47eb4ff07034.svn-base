.change-page {
  width: 50px;
  height: 235px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .page-size {
    font-size: 32px;
    color: #333;
    margin: 10px 0;

    span {
      display: block;
      text-align: center;
    }
  }

  .up-btn {
    text-align: center;
    cursor: pointer;

    .arrow-up {
      display: inline-block;
      width: 0;
      height: 0;
      border-left: 24px solid transparent;
      border-right: 24px solid transparent;
      border-bottom: 33px solid #9D9A9A;

      &.blue {
        border-bottom-color: #1677FF;
      }

      &.gray {
        border-bottom-color: #DDDDDD;
      }
    }
  }

  .next-btn {
    text-align: center;
    cursor: pointer;

    .arrow-down {
      display: inline-block;
      width: 0;
      height: 0;
      border-top: 33px solid #9D9A9A;
      border-left: 24px solid transparent;
      border-right: 24px solid transparent;

      &.blue {
        border-top-color: #1677FF;
      }

      &.gray {
        border-top-color: #DDDDDD;
      }
    }
  }
}
