import { Row, Col } from 'antd'
import { useState, useEffect } from 'react'
import dayjs from 'dayjs'
import ModuleHeader from '@/components/ModuleHeader'
import PayMode from '@/components/PayMode'
import BlueBgTitle from '@/components/BlueBgTitle'
import { useSelector } from 'react-redux'
import { getSpeak } from '@/services/hardware'
import { RootState } from '@/store'
import { IdNoPrivate } from '@/utils/utils'
import loading from '@/utils/loading'
import styles from './index.module.scss'

const Discharge = () => {
  const { currentUser, readCardData } = useSelector((state: RootState) => state.userInfo)
  const [preSettlementData, setPreSettlementData] = useState(null)
  const [money, setMoney] = useState<any>(0)

  useEffect(() => {
    loading.start('正在费用预结算中，请稍候...')
    setTimeout(() => {
      loading.end()
      window.config.isSpeak && getSpeak({ content: '请确认结算信息并支付' })
    }, 3000)
    const medicalPrivateAmount = readCardData.mdtrt_cert_type === '1' ? '0' : '1300.00'
    const privateAmount = readCardData.mdtrt_cert_type === '1' ? '2098.76' : '298.76'
    const medicalFound = readCardData.mdtrt_cert_type === '1' ? '0' : '500.00'
    setPreSettlementData({
      medicalFound,
      medicalPrivateAmount,
      privateAmount
    })
    setMoney(privateAmount)
  }, [])

  return (
    <>
      <ModuleHeader title={'家庭病床结算'} />
      <div className={styles['confirm-page-wrapper']}>
        <Row gutter={[0, 6]}>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
              <div className={styles['info-box']}>
                <BlueBgTitle title={'病床信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>姓名：</span>
                  {currentUser.patientName}
                </div>
                <div className={styles['item']}>
                  <span>病案号：</span>
                  {currentUser.inHospitalID}
                </div>
                <div className={styles['item']}>
                  <span>家庭地址：</span>
                  {currentUser.permanentAddress}
                </div>
                <div className={styles['item']}>
                  <span>开始日期：</span>
                  {dayjs().subtract(10, 'day').format('YYYY-MM-DD')}
                </div>
              </div>
              <div className={styles['detail-list']}>
                <BlueBgTitle title={'结算信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>费用总额：</span>2098.76元
                </div>
                <div className={styles['item']}>
                  <span>医保统筹：</span>
                  {preSettlementData?.medicalFound ?? '0'}元
                </div>
                <div className={styles['item']}>
                  <span>医保个账：</span>
                  {preSettlementData?.medicalPrivateAmount ?? '0'}元
                </div>
                <div className={styles['item']}>
                  <span>起付标准：</span>1000.00
                </div>
                <div className={styles['pay-money']}>
                  需补缴金额：<span>{money}</span>元
                </div>
              </div>
            </div>
          </Col>
          <Col span={24}>
            <PayMode
              PAYMENT_TYPE={'discharge'}
              SUCCESS_MODAL_TITLE={'家庭病床结算成功，请取走凭条'}
              money={money}
              payParams={{
                patientName: currentUser.patientName
              }}
            />
          </Col>
        </Row>
      </div>
    </>
  )
}

export default Discharge
