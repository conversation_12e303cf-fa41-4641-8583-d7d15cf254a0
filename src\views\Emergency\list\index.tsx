import { useEffect, useState } from 'react'
import { PlusCircleFilled } from '@ant-design/icons'
import { Button } from 'antd-mobile'
import { useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import EditTable from '@/components/EditTable'
import { ConfigProvider, Table } from 'antd'
import { RootState } from '@/store'
import dayjs from 'dayjs'
import ModuleHeader from '@/components/ModuleHeader'
import { IdNoPrivate } from '@/utils/utils'
import { getDrugsDiagnosis } from '@/services/pay'
import { getSpeak } from '@/services/hardware'
import styles from './index.module.scss'
import ETipModal from '@/components/ETipModal'
import gotoMenu from '@/utils/gotoMenu'

type EditableTableProps = Parameters<typeof Table>[0]

interface DataType {
  key: React.Key;
  name: string;
  price: string;
  age?: string;
  address?: string;
  amount: string;
  unit: string;
  myusages: any;
  ypmc?: string, //药品名称
  sccj?: string, //厂家
  ypgg?: string, //规格
  num: any,
  isSelected?: boolean,
}

type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>

/**
 * 查询物价
 * @param fields
 */
const handleQueryDrugsDiagnosis = async fields => {
  try {
    let response
    if (window.config.isDebug) {
      response = {
        msg: '请求成功！',
        code: '0',
        data: [
          {
            code: '34414',
            factureInfo: '',
            name: '一般院前急救费',
            price: '156.00',
            pyCode: 'YBYQJJ',
            specifications: '',
            unit: '人次'
          },
          {
            code: '34413',
            factureInfo: '',
            name: '院前急救费',
            price: '234.00',
            pyCode: 'YQJJF',
            specifications: '',
            unit: '人次'
          },
          {
            code: '35908',
            factureInfo: '',
            name: '救护车费（3公里以上）',
            price: '2.00',
            pyCode: 'JHCF（3GLYS）',
            specifications: '',
            unit: '公里'
          },
          {
            code: '35909',
            factureInfo: '',
            name: '救护车起步价（3公里内）',
            price: '10.00',
            pyCode: 'JHCQBJ（3GLN）',
            specifications: '',
            unit: '次'
          }
        ]
      }
    } else {
      response = await getDrugsDiagnosis({
        ...fields
      })
    }

    if (response.code == '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

const List = () => {
  useEffect(() => {
    queryData()
    window.config.isSpeak && getSpeak({ content: '请选择诊断信息和收费项目' })
  }, [])
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { currentUser, readCardData } = useSelector((state: RootState) => state.userInfo)
  const { otherData } = useSelector((state: RootState) => state.list)
  const [dataSource, setDataSource] = useState<DataType[]>([])

  const tableColumns: (ColumnTypes[number] & { editable?: boolean; dataIndex: string })[] = [
    {
      title: '项目',
      dataIndex: 'name',
      render: (name, record) => {
        return <div className='table-item-name'>{name}</div>
      },
      align: 'center',
      width: 100
    },
    {
      title: '单价(元)',
      dataIndex: 'price',
      align: 'center'
    },
    {
      title: '单位',
      dataIndex: 'quantity',
      render: (_, record: any) => {
        return `${record.quantity}${record.unit}`
      },
      align: 'center',
      editable: true
    }
  ]
  // 跳转选择诊断
  const handleToSelectDiag = () => {
    navigate('/emergency/query')
  }

  // 跳转确认页面
  const handleConfirm = () => {
    console.log(dataSource)
    const filter = dataSource.filter(item => item.isSelected)
    if (!otherData.name) {
      ETipModal('请选择诊断信息', 'error')
      return
    }
    if (filter.length < 1) {
      ETipModal('请选择费用项目', 'error')
      return
    }
    console.log(filter)
    dispatch({
      type: 'list/setList',
      payload: filter
    })
    navigate('/emergency/confirm')
  }

  // 查询数据
  const queryData = async () => {
    const res = await handleQueryDrugsDiagnosis({
      queryType: '3',
      patientID: currentUser.patientID
    })
    if (res && res.data) {
      const drugArr = res.data.map(item => {
        return { ...item, key: item.code, quantity: '1', isSelected: false }
      })
      setDataSource(drugArr)
    } else {
      // 未查询到项目 返回首页
      gotoMenu(navigate)
    }
  }

  return (
    <>
      <ModuleHeader title={'费用选择'} />
      <div className={[styles['emergency-list-wrapper'], styles['scroll-style']].join(' ')}>
        {/* <Form layout='horizontal' className={styles['my-form']}>
          <Form.Item label='医生代码' name='username' rules={[{ required: true }]}>
            <Input placeholder='必填，请输入' clearable className={styles['my-input']} />
          </Form.Item>
        </Form> */}
        <div className={styles['info-box']}>
          <div className={styles['item']}>姓名：{currentUser.patientName}</div>
          <div className={styles['item']}>证件号：{IdNoPrivate(currentUser.patientIDCard)}</div>
          <div className={styles['item']}>手机号：{currentUser.phone}</div>
        </div>
        {/* <div className={styles['diagnosis-box']}>
          <div className={styles['label']}>
            <img src={require('@/assets/images/others/diagnosis.png')}></img>
            <span>主诊断</span>
          </div>
          <div className={styles['content']}>
            <PlusCircleFilled
              style={{ fontSize: `${35 / window.config.minPixelValue}`, color: '#1677FF' }}
            />
            <span>添加</span>
          </div>
        </div> */}
        <div className={styles['diagnosis-box']}>
          <div className={styles['label']}>
            <img src={require('@/assets/images/others/diagnosis.png')}></img>
            <span>诊断</span>
          </div>
          <div className={styles['content']} onClick={handleToSelectDiag}>
            {otherData.name ? (
              `${otherData.name}`
            ) : (
              <>
                <PlusCircleFilled
                  style={{ fontSize: `${35 / window.config.minPixelValue}`, color: '#1677FF' }}
                />
                <span>添加</span>
              </>
            )}
          </div>
        </div>
        <div className={styles['item-table']}>
          <div className={styles['title-wrap']}>费用日期：{dayjs().format('YYYY-MM-DD')}</div>
          <ConfigProvider
            theme={{
              token: {
                colorText: '#333333',
                fontSize: 15,
                lineWidthBold: 1
              },
              components: {
                Table: {
                  cellFontSize: 15,
                  headerBg: '#FFFFFF',
                  borderColor: '#FFFFFF',
                  headerSplitColor: '#FFFFFF'
                }
              }
            }}
          >
            <EditTable
              dataSource={dataSource}
              tableColumns={tableColumns}
              setDataSource={setDataSource}
            />
          </ConfigProvider>
        </div>
        <div className={styles['button-wrapper']}>
          <Button color='primary' size='middle' block={true} onClick={handleConfirm}>
            确认
          </Button>
        </div>
      </div>
    </>
  )
}

export default List
