$pixel: 2; //屏幕缩小变量
$width660: calc(660px / $pixel);

.scroll-style {
  overflow-y: auto;
  overflow-x: hidden;
}

/* 滚动条整体样式(高宽分别对应横竖滚动条的尺寸) */
.scroll-style::-webkit-scrollbar {
  width: calc(12px / $pixel);
  height: 0;
}

/* 滚动条里面小方块 */
.scroll-style::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #D0E4FF;
  -webkit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.25);
}

/* 滚动条里面轨道 */
.scroll-style::-webkit-scrollbar-track {
  border-radius: 3px;
  background-color: #F7F7F7;
}
