import { Row, Col, Flex } from 'antd'
import { NavLink } from 'react-router-dom'
import MiniButtonItem from './MiniButtonItem'
import styles from '../index.module.scss'
import { group } from '@/utils/utils'

const styleName = ['bggreen', 'bgblue', 'bgorange']
const MenuNew = ({ data, findUrl }) => {
  const arr = group(data, 'title_type')
  return (
    <div className={styles['menu-new']}>
      <div className={styles['top-menu']}>
        <Flex justify='space-between'>
          {arr[0].data.map((item, index) => {
            return (
              <MiniButtonItem
                key={index}
                item={item}
                class_name={styleName[index]}
                findUrl={findUrl}
              ></MiniButtonItem>
            )
          })}
        </Flex>
      </div>
      <div className={styles['other-menu']}>
        <div className={`${styles['menu-title']} ${styles['blue']}`}>院外服务</div>
        <Flex justify='flex-start'>
          {arr[1].data.map((item, index) => {
            return (
              <div className={styles['menu-item']} key={`${item.title_type}-${index}`}>
                <NavLink to={findUrl(item.name)}>
                  <div className={styles['menu-icon']}>
                    <img
                      className={styles['icon-image']}
                      src={require(`../../../assets/images/iconsfilled/${item.icon_filled}.png`)}
                      alt='图标'
                    />
                  </div>
                  <div className={styles['menu-name']}>{item.name}</div>
                </NavLink>
              </div>
            )
          })}
        </Flex>
      </div>
      <div className={styles['other-menu']}>
        <div className={`${styles['menu-title']} ${styles['green']}`}>门诊服务</div>
        <Flex justify='flex-start'>
          {arr[2].data.map((item, index) => {
            return (
              <div className={styles['menu-item']} key={`${item.title_type}-${index}`}>
                <NavLink to={findUrl(item.name)}>
                  <div className={styles['menu-icon']}>
                    <img
                      className={styles['icon-image']}
                      src={require(`../../../assets/images/iconsfilled/${item.icon_filled}.png`)}
                      alt='图标'
                    />
                  </div>
                  <div className={styles['menu-name']}>{item.name}</div>
                </NavLink>
              </div>
            )
          })}
        </Flex>
      </div>
      <div className={styles['other-menu']}>
        <div className={`${styles['menu-title']} ${styles['orange']}`}>住院服务</div>
        <Flex justify='flex-start'>
          {arr[3].data.map((item, index) => {
            return (
              <div className={styles['menu-item']} key={`${item.title_type}-${index}`}>
                <NavLink to={findUrl(item.name)}>
                  <div className={styles['menu-icon']}>
                    <img
                      className={styles['icon-image']}
                      src={require(`../../../assets/images/iconsfilled/${item.icon_filled}.png`)}
                      alt='图标'
                    />
                  </div>
                  <div className={styles['menu-name']}>{item.name}</div>
                </NavLink>
              </div>
            )
          })}
        </Flex>
      </div>
    </div>
  )
}

export default MenuNew
