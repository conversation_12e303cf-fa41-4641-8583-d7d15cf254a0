{"name": "react-manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite serve --mode development", "build": "tsc && vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --port 8080"}, "dependencies": {"@reduxjs/toolkit": "^2.2.6", "@rollup/plugin-commonjs": "^26.0.1", "ahooks": "^3.8.0", "antd": "^5.19.1", "antd-mobile": "^5.37.1", "antd-style": "^3.6.2", "axios": "^1.5.1", "dayjs": "^1.11.11", "js-base64": "^3.7.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.1.2", "react-router-dom": "^6.16.0", "sass": "^1.77.6", "vconsole": "^3.15.1", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20.5.7", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.39", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.0.2", "typescript": "^5.0.2", "vite": "^4.5.3", "vite-plugin-require-transform": "^1.0.21"}}