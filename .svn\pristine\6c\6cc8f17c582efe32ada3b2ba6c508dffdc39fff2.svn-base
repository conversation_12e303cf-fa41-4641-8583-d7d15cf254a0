import { Row, Col } from 'antd'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import { IdNoPrivate } from '@/utils/utils'
import ModuleHeader from '@/components/ModuleHeader'
import BlueBgTitle from '@/components/BlueBgTitle'
import styles from './index.module.scss'
import { useEffect } from 'react'
import { getSpeak } from '@/services/hardware'

const List = () => {
  const { currentUser } = useSelector((state: RootState) => state.userInfo)

  useEffect(() => {
    window.config.isSpeak && getSpeak({ content: '请确认缴费明细' })
  }, [])

  return (
    <>
      <ModuleHeader title={'缴费列表'} />
      <div className={styles['confirm-page-wrapper']}>
        <Row gutter={[0, 6]}>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
              <BlueBgTitle title={'病人信息'} />
              <div className={styles['info-box']}>
                <div className={styles['item']}>
                  <span>姓名：</span>
                  {currentUser.patientName}
                </div>
                <div className={styles['item']}>
                  <span>证件号：</span>
                  {IdNoPrivate(currentUser.patientIDCard)}
                </div>
                <div className={styles['item']}>
                  <span>手机号：</span>
                  {currentUser.phone}
                </div>
              </div>
            </div>
          </Col>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['pay']].join(' ')}>
              <BlueBgTitle title={'费用明细'} />
              <div className={styles['pay-info-wrapper']}>
                <div className={styles['pay-item']}>
                  <div className={styles['pay-summary']}>西药费：200元</div>
                  <Row>
                    <Col span={8}>
                      <div className={styles.title}>药品名称</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.title}>单价（元）</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.title}>数量/单位</div>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={8}>
                      <div className={styles.content}>贝那普利</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>200</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>1/瓶</div>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={8}>
                      <div className={styles.content}>替米沙坦</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>100</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>2/只</div>
                    </Col>
                  </Row>
                </div>
                <div className={styles['pay-item']}>
                  <div className={styles['pay-summary']}>治疗费：100元</div>
                  <Row>
                    <Col span={8}>
                      <div className={styles.title}>项目名称</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.title}>单价（元）</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.title}>数量/单位</div>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={8}>
                      <div className={styles.content}>针灸</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>50</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>1/次</div>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={8}>
                      <div className={styles.content}>注射费</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>50</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>1/次</div>
                    </Col>
                  </Row>
                </div>
                <div className={styles['pay-item']}>
                  <div className={styles['pay-summary']}>检查费：145元</div>
                  <Row>
                    <Col span={8}>
                      <div className={styles.title}>项目名称</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.title}>单价（元）</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.title}>数量/单位</div>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={8}>
                      <div className={styles.content}>血常规</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>65</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>1/次</div>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={8}>
                      <div className={styles.content}>DR费</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>80</div>
                    </Col>
                    <Col span={8}>
                      <div className={styles.content}>1/次</div>
                    </Col>
                  </Row>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </div>
    </>
  )
}

export default List
