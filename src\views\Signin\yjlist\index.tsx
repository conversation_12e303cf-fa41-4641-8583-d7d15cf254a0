import { useState, useEffect } from 'react'
import { Button } from 'antd-mobile'
import dayjs from 'dayjs'
import styles from './index.module.scss'
import { NavBar, JumboTabs } from 'antd-mobile'
import { useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/store'
import { IdNoPrivate } from '@/utils/utils'
import { getSpeak } from '@/services/hardware'
import BlueBgTitle from '@/components/BlueBgTitle'
import ETipModal from '@/components/ETipModal'
import gotoMenu from '@/utils/gotoMenu'

const DoctorList = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { currentUser } = useSelector((state: RootState) => state.userInfo)

  useEffect(() => {
    window.config.isSpeak && getSpeak({ content: '请选择要签到的项目' })
  }, [])

  const handleSignIn = () => {
    ETipModal('医技签到成功，请耐心等候叫号...', 'success', () => {
      gotoMenu(navigate)
    })
  }

  return (
    <div className={styles['doctor-wrapper']}>
      <div className={styles['header']}>
        <NavBar back='返回' onBack={() => navigate(-1)} style={{ color: '#fff' }}>
          {'医技签到'}
        </NavBar>
      </div>
      <div className={styles['info-wrapper']}>
        <BlueBgTitle title={'就诊人信息'}></BlueBgTitle>
        <div className={styles['info-box']}>
          <div className={styles['item']}>
            <span>姓名：</span>
            {currentUser.patientName}
          </div>
          <div className={styles['item']}>
            <span>证件号：</span>
            {IdNoPrivate(currentUser.patientIDCard)}
          </div>
          <div className={styles['item']}>
            <span>手机号：</span>
            {currentUser.phone}
          </div>
        </div>
      </div>
      <div className={styles['sign-list-wrapper']}>
        <div className={styles['doctor-schdule']} onClick={() => handleSignIn()}>
          <div className={styles['doctor-info']}>
            <img
              src={require('@/assets/images/others/default-doctor.png')}
              alt='医生照片'
              className={styles['doctor-image']}
            />
            <div className={styles['right']}>
              <div className={styles['name-box']}>
                <span className={styles['name']}>血常规</span>
              </div>
              <div className={styles['content']}>
                <span className=''>检验中心</span>
              </div>
            </div>
          </div>
          <div className={styles['button-wrapper']}>
            <Button color='primary' shape='rounded' block size='small'>
              签到
            </Button>
          </div>
        </div>
        <div className={styles['doctor-schdule']} onClick={() => handleSignIn()}>
          <div className={styles['doctor-info']}>
            <img
              src={require('@/assets/images/others/default-doctor.png')}
              alt='医生照片'
              className={styles['doctor-image']}
            />
            <div className={styles['right']}>
              <div className={styles['name-box']}>
                <span className={styles['name']}>B超</span>
              </div>
              <div className={styles['content']}>
                <span className=''>B超室</span>
              </div>
            </div>
          </div>
          <div className={styles['button-wrapper']}>
            <Button color='primary' shape='rounded' block size='small'>
              签到
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DoctorList
