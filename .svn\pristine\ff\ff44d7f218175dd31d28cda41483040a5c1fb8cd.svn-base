import { Row, Col } from 'antd'
import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/store'
import ModuleHeader from '@/components/ModuleHeader'
import PayMode from '@/components/PayMode'
import BlueBgTitle from '@/components/BlueBgTitle'
import styles from './index.module.scss'
import { IdNoPrivate } from '@/utils/utils'
import { getSpeak } from '@/services/hardware'
import loading from '@/utils/loading'

const Confirm = () => {
  const { currentUser, readCardData } = useSelector((state: RootState) => state.userInfo)
  const [preSettlementData, setPreSettlementData] = useState(null)
  const [money, setMoney] = useState<any>(0)
  useEffect(() => {
    loading.start('正在预结算中，请稍候...')
    setTimeout(() => {
      loading.end()
      window.config.isSpeak && getSpeak({ content: '请确认取号信息并支付' })
    }, 3000)
    const medicalPrivateAmount = readCardData.mdtrt_cert_type === '1' ? '0' : '15'
    const privateAmount = readCardData.mdtrt_cert_type === '1' ? 80 : 65

    setPreSettlementData({
      medicalFound: '0',
      medicalPrivateAmount,
      privateAmount
    })
    setMoney(privateAmount)
  }, [])
  return (
    <>
      <ModuleHeader title={'取号支付'} />
      <div className={styles['confirm-page-wrapper']}>
        <Row gutter={[0, 6]}>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
              <div className={styles['info-box']}>
                <BlueBgTitle title={'就诊人信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>姓名：</span>
                  {currentUser.patientName}
                </div>
                <div className={styles['item']}>
                  <span>证件号：</span>
                  {IdNoPrivate(currentUser.patientIDCard)}
                </div>
                <div className={styles['item']}>
                  <span>手机号：</span>
                  {currentUser.phone}
                </div>
              </div>
              <div className={styles['detail-list']}>
                <BlueBgTitle title={'号源信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>就诊医院：</span>
                  {window.config.HOS_NAME}
                </div>
                <div className={styles['item']}>
                  <span>就诊科室：</span>消化内科&nbsp;&nbsp;杜鹃
                </div>
                <div className={styles['item']}>
                  <span>号源类型：</span>专家门诊<b className={styles['money']}>80元</b>
                </div>
                <div className={styles['item']}>
                  <span>就诊日期：</span>
                  {dayjs().format('YYYY-MM-DD')}
                </div>
                <div className={styles['item']}>
                  <span>&nbsp;</span>13:30-13:45<b className={styles['number']}>2号</b>
                </div>
              </div>
            </div>
          </Col>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['pay']].join(' ')}>
              <BlueBgTitle title={'支付信息'}></BlueBgTitle>
              <div className={styles['pay-info']}>
                <div className={styles['item']}>
                  <span>总金额：</span>80元
                </div>
                <div className={styles['item']}>
                  <span>医保金额：</span>
                  {preSettlementData?.medicalPrivateAmount ?? '0'}元
                </div>
              </div>
              <div className={styles['pay-money']}>
                需支付金额：<span>{preSettlementData?.privateAmount ?? '0'}</span>元
              </div>
            </div>
          </Col>
          <Col span={24}>
            <PayMode
              PAYMENT_TYPE={'register'}
              SUCCESS_MODAL_TITLE={'取号支付成功，请取走凭条'}
              money={preSettlementData?.privateAmount ?? '0'}
              payParams={{
                patientName: currentUser.patientName
              }}
            />
          </Col>
        </Row>
      </div>
    </>
  )
}

export default Confirm
