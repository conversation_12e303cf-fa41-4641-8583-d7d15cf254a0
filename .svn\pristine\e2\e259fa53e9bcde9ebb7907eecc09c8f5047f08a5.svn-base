import { Row, Col, Space, Input, Button } from 'antd'
import { Button as MobileButton } from 'antd-mobile'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { useState, useRef, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { IdCard, rand } from '../../../utils/utils'
import { RootState } from '@/store'
import ModuleHeader from '@/components/ModuleHeader'
import NumKeyboardDrawer from '@/components/NumKeyboardDrawer'
import BlueBgTitle from '@/components/BlueBgTitle'
import styles from './index.module.scss'
import ETipModal from '@/components/ETipModal'
import { getSpeak } from '@/services/hardware'
import { getUserInfo, createUser } from '@/services/user'

interface UserInfoType {
  patientName?: string
  patientGender?: string
  patientIDCard?: string
  birthday?: string
  cardTypeName?: string
  phone?: string
  permanentAddress?: string
}

/**
 * 请求用户信息
 * @param fields
 */
const handleUserInfo = async fields => {
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        data: {
          birthday: IdCard(fields.readCardData.certNo, 1),
          patientName: fields.readCardData.certName,
          patientID: `00${rand(6)}`,
          patientGender: IdCard(fields.readCardData.certNo, 2),
          inHospitalID: `${rand(8)}`,
          phone: `159${rand(8)}`,
          patientIDCard: fields.readCardData.certNo,
          patientCard: `202400${rand(6)}`,
          patientNumber: `100${rand(7)}`,
          permanentAddress: '浙江省杭州市余杭区余杭街道华一路',
          mdtrt_cert_type: '02'
        },
        msg: ''
      }
    } else {
      response = await getUserInfo({
        ...fields
      }, false)
    }
    if (response.code == '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * 请求用户信息
 * @param fields
 */
const handleCreateUser = async fields => {
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        data: {
          birthday: IdCard(fields.readCardData.certNo, 1),
          patientName: fields.readCardData.certName,
          patientID: `00${rand(6)}`,
          patientGender: IdCard(fields.readCardData.certNo, 2),
          inHospitalID: `${rand(8)}`,
          phone: `159${rand(8)}`,
          patientIDCard: fields.readCardData.certNo,
          patientCard: `202400${rand(6)}`,
          patientNumber: `100${rand(7)}`,
          permanentAddress: '浙江省杭州市余杭区余杭街道华一路',
          mdtrt_cert_type: '02'
        },
        msg: ''
      }
    } else {
      response = await createUser({
        ...fields
      })
    }
    if (response.code == '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

const Checkin = () => {
  const [search] = useSearchParams()
  const nextpage = search.get('nextpage')
  const navigate = useNavigate()
  const [numPickerVisible, setNumPickerVisible] = useState<boolean>(false)
  const [phone, setPhone] = useState(null)
  const [address, setAddress] = useState(null)
  const { readCardData } = useSelector((state: RootState) => state.userInfo)
  const [userData, setUserData] = useState<UserInfoType>()
  const dispatch = useDispatch()
  const inputRef = useRef(null)

  const handleCloseNumPickerDrawer = () => {
    setNumPickerVisible(false)
  }
  const handleConfirmNumPicker = data => {
    const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
    if (data.length === 0 || !reg.test(data)) {
      ETipModal('请输入正确的手机号码', 'error')
      return
    }
    inputRef.current.focus()
    setPhone(data)
    setNumPickerVisible(false)
  }

  useEffect(() => {
    getReadData(readCardData)
  }, [readCardData])

  useEffect(() => {
    window.config.isSpeak && getSpeak({ content: '请输入手机号和联系地址完成建档' })
  }, [])

  const getReadData = value => {
    console.log(value)
    if (!value) return
    const sexStr = value.patientGender ?? (value.certNo ? IdCard(value.certNo, 2) : '0')
    const birthday = value.birthday ?? (value.certNo ? IdCard(value.certNo, 1) : '')
    const data = {
      patientName: value?.certName,
      patientGender: sexStr,
      birthday,
      patientIDCard: value?.certNo,
      permanentAddress: value?.permanentAddress,
      cardTypeName: value.mdtrt_type_name,
      cardNo: value.mdtrt_cert_data
    }
    setAddress(data.permanentAddress ?? '')
    setPhone('')
    setUserData(data)
  }

  // 确认建档
  const handleConfirm = async () => {
    if (!phone) {
      ETipModal('请输入正确的手机号码', 'error')
      return
    }
    const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
    if (phone.length === 0 || !reg.test(phone)) {
      ETipModal('请输入正确的手机号码', 'error')
      return
    }
    if (!address) {
      ETipModal('请输入户籍地址', 'error')
      return
    }
    const createRes = await handleCreateUser({
      ...userData,
      readCardData,
      phone: phone,
      permanentAddress: address
    })
    if (!createRes) return
    const res = await handleUserInfo({
      readCardData,
      cardType: readCardData.mdtrt_cert_type,
      cardNo: readCardData.mdtrt_cert_no
    })
    if (res?.code === '0' && res?.data) {
      dispatch({
        type: `userInfo/setQueryUser`,
        payload: { ...res.data }
      })
      ETipModal('建档成功', 'success', () => {
        navigate(nextpage, { replace: true })
      })
    } else {
      ETipModal('建档后查询档案失败，请重新登陆或联系医院工作人员处理！', 'error', () => {
        navigate('-1')
      })
    }
  }

  return (
    <>
      <ModuleHeader title={'建档'} />
      <div className={styles['patient-page-wrapper']}>
        <Row gutter={[0, 36]}>
          <Col span={24}>
            <div className={[styles['patient-info-list-wrapper'], styles['info']].join(' ')}>
              <div className={styles['info-box']}>
                <BlueBgTitle title={'患者信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>姓名：</span>
                  {userData?.patientName}
                </div>
                <div className={styles['item']}>
                  <span>性别：</span>
                  {userData?.patientGender === '1'
                    ? '男'
                    : userData?.patientGender === '2'
                    ? '女'
                    : '未知'}
                </div>
                <div className={styles['item']}>
                  <span>身份证号：</span>
                  {userData?.patientIDCard}
                </div>
                <div className={styles['item']}>
                  <span>生日：</span>
                  {userData?.birthday}
                </div>
                <div className={styles['item']}>
                  <span>登陆类型：</span>
                  {userData?.cardTypeName}
                </div>
                <div className={styles['item']}>
                  <div className={styles['input-money']}>
                    <span>联系方式：</span>
                    <Space.Compact>
                      <Input
                        readOnly
                        value={phone}
                        ref={inputRef}
                        onClick={() => setNumPickerVisible(true)}
                      ></Input>
                      <Button type='primary' onClick={() => setNumPickerVisible(true)}>
                        输入
                      </Button>
                    </Space.Compact>
                  </div>
                </div>
                <div className={styles['item']}>
                  <div className={styles['input-money']}>
                    <span>联系地址：</span>
                    <Input
                      value={address}
                      ref={inputRef}
                      onChange={e => setAddress(e.target.value)}
                    ></Input>
                  </div>
                </div>
              </div>
            </div>
          </Col>
          <Col span={24}>
            <div className={styles['button-wrapper']}>
              <MobileButton color='primary' size='middle' block={true} onClick={handleConfirm}>
                确 认
              </MobileButton>
            </div>
          </Col>
        </Row>
      </div>
      {numPickerVisible && (
        <NumKeyboardDrawer
          visible={numPickerVisible}
          title={'手机号'}
          onCancel={handleCloseNumPickerDrawer}
          onConfirm={handleConfirmNumPicker}
          type='number'
          value={phone}
          length={11}
        />
      )}
    </>
  )
}

export default Checkin
