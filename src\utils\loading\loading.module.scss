.loading-container {
  position: absolute;
  display: flex;
  top: calc(100px/$pixel);
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999999;
  text-align: center;
  background: rgba(0, 0, 0, 0.8);
  align-items: center;
  border-radius: calc(30px/$pixel) calc(30px/$pixel) 0 0;

  .loading-modal-init {
    width: calc(660px/$pixel);
    height: calc(450px/$pixel);
    background: #FFFFFF;
    padding: calc(15px/$pixel);
    margin: 0 auto;
    border-radius: calc(15px/$pixel);
    animation: zoom 0.05s forwards ease-out;

    .text {
      font-size: calc(36px/$pixel);
      font-weight: 400;
    }

    .logo {
      width: calc(160px/$pixel);
      height: calc(160px/$pixel);
      margin-top: calc(80px/$pixel);
      margin-bottom: calc(86px/$pixel);
    }

  }

  .show {
    display: block;
  }

  .hide {
    display: none;
  }
}
