.confirm-page-wrapper {
  width: calc(660px / $pixel);
  margin: calc(20px / $pixel) auto 0;
  overflow-x: hidden;
}

.confirm-info-list-wrapper {
  position: relative;
  width: 100%;
  padding: calc(40px / $pixel) calc(40px / $pixel) calc(20px / $pixel);
  background-color: #FFF;
  border-radius: calc(22px / $pixel);
  border: 1px solid #99C4FF;

  &::before {
    position: absolute;
    content: '';
    top: calc(40px / $pixel);
    left: calc(-15px / $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &::after {
    position: absolute;
    content: '';
    top: calc(40px/ $pixel);
    right: calc(-15px/ $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &.info {

    &::before,
    &::after {
      top: calc(40px/ $pixel);
    }
  }

  &.pay {
    padding: calc(50px / $pixel) calc(40px / $pixel) calc(20px / $pixel);

    &::before,
    &::after {
      top: calc(35px/ $pixel);
    }
  }

  .info-box {
    padding: calc(20px / $pixel) 0;
    font-size: calc(28px / $pixel);
    line-height: calc(56px / $pixel);
    color: #383838;
    font-weight: 500;

    .item {
      span {
        display: inline-block;
        min-width: calc(100px / $pixel);
        margin-right: calc(30px / $pixel);
        color: #A6A6A6;
      }
    }
  }

  .pay-info-wrapper {
    height: calc(768px / $pixel);
    overflow-y: scroll;

    .pay-item {
      padding: calc(20px / $pixel) 0;
      border-bottom: 1px solid #CFCFCF;

      &:last-child {
        border-bottom: none;
      }

      .pay-summary {
        font-size: calc(35px / $pixel);
      }

      .title {
        text-align: center;
        font-size: calc(25px / $pixel);
        line-height: calc(35px / $pixel);
        margin-bottom: calc(10px / $pixel);
        margin-top: calc(25px / $pixel);
      }

      .content {
        text-align: center;
        font-size: calc(20px / $pixel);
        line-height: calc(35px / $pixel);
        margin-bottom: calc(10px / $pixel);
      }
    }
  }
}
