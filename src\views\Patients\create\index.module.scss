.patient-page-wrapper {
  width: calc(660px / $pixel);
  margin: calc(20px / $pixel) auto 0;
  overflow-x: hidden;
}

.patient-info-list-wrapper {
  position: relative;
  width: 100%;
  padding: calc(0px / $pixel) calc(40px / $pixel);
  background-color: #FFF;
  border-radius: calc(22px / $pixel);
  border: 1px solid #99C4FF;

  &::before {
    position: absolute;
    content: '';
    left: calc(-15px / $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &::after {
    position: absolute;
    content: '';
    right: calc(-15px/ $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &.info {

    &::before,
    &::after {
      top: calc(40px/ $pixel);
    }
  }

  .info-box {
    padding: calc(70px / $pixel) 0 calc(30px / $pixel);
    font-size: calc(28px / $pixel);
    line-height: calc(60px / $pixel);
    color: #383838;
    font-weight: medium;
    border-bottom: 1px solid #CFCFCF;
    position: relative;

    .item {
      >span {
        display: inline-block;
        min-width: calc(150px / $pixel);
        margin-right: calc(30px / $pixel);
        font-weight: medium;

        &.number {
          color: #383838;
          font-weight: 600;
          font-size: calc(28px / $pixel);
        }
      }

      .input-money {
        margin-top: calc(20px / $pixel);
        display: flex;
        align-items: center;

        >span {
          min-width: calc(150px / $pixel);
        }
      }
    }
  }
}
