import request from '@/utils/request'

// 创建订单缴费二维码
export async function createPay(params) {
  return request({
    url: `/pay/createPay.dp`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION }
  })
}

// B扫C 收单
export async function acquiringPay(params) {
  return request({
    url: `/pay/bScanCpay.dp`,
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    },
    loadingText: '正在支付中，请稍候...'
  })
}

// 关闭订单
export async function closedPay(params) {
  return request({
    url: `/pay/closedPay.dp`,
    method: 'GET',
    params: { ...params, apiVersion: window.config.API_VERSION },
    isHideLoading: true
  })
}

// 支付信息查询
export async function queryOrderPay(params) {
  return request({
    url: `/pay/bScanCquery.dp`,
    method: 'GET',
    params: { ...params, apiVersion: window.config.API_VERSION },
    isHideLoading: true,
    isHideError: true
  })
}

// 退款
export async function refundPay(params) {
  return request({
    url: `/pay/refundPay.dp`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION }
  })
}

// 添加支付信息
export async function addPayInfo(params) {
  return request({
    url: `/pay/addPayInfo.dp`,
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE,
      apiVersion: window.config.API_VERSION
    },
    isHideLoading: true,
    isHideError: true
  })
}

//获取患者待缴费信息
export async function queryPaymentList(params) {
  return request({
    url: `/pay/paymentList`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION }
  })
}

//获取缴费明细
export async function queryPaymentDetailList(params) {
  return request({
    url: `/pay/paymentDetailList`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION }
  })
}

//HIS预结算
export async function hisPreSettlement(params) {
  return request({
    url: `/qzPayment/paymentPreSettlement`,
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    },
    loadingText: '正在预结算中，请稍侯...'
  })
}

//HIS结算
export async function paymentSettlement(params) {
  return request({
    url: `/qzPayment/paymentSettlement`,
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    },
    loadingText: '正在结算中，请稍侯...'
  })
}

//急救收费记录
export async function getPaymentList(params) {
  return request({
    url: `/qzPayment/getPaymentList`,
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    },
    loadingText: '急救收费记录查询中，请稍侯...'
  })
}

//撤销HIS预结算
export async function revocationHisPreSettlement(params) {
  return request({
    url: `/pay/revocationHisPreSettlement`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION },
    isHideLoading: true
  })
}

// 物价查询
export async function getDrugsDiagnosis(params) {
  return request({
    url: `/qzBasic/getDrugsDiagnosis`,
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    }
  })
}

// 门诊处方保存
export async function savePrescription(params) {
  return request({
    url: `/qzPayment/savePrescription`,
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    },
    loadingText: '正在生成处方中，请稍侯...'
  })
}

// 门诊处方查询
export async function getPrescriptionInfo(params) {
  return request({
    url: `/qzPayment/getPrescriptionInfo`,
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    },
    loadingText: '正在查询处方中，请稍侯...'
  })
}
