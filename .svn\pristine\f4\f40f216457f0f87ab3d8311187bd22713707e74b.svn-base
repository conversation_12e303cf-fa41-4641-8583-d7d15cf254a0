/*
 * @Description: 弹窗组件关闭按钮 + 倒计时按钮
 */
import { useState } from 'react'
import { useCountDown } from 'ahooks'
import { CloseOutlined } from '@ant-design/icons'
import styles from './index.module.scss'

type ModalCloseItemProps = {
  onCancel: () => void
  num: number
}

const ModalCloseItem: React.FC<ModalCloseItemProps> = ({ onCancel, num }) => {
  const [targetDate, setTargetDate] = useState<number>(0)
  const [countdown] = useCountDown({
    leftTime: num * 1000,
    targetDate: targetDate,
    onEnd: onCancel
  })

  return (
    <div
      className={styles['modal-close']}
      onClick={() => {
        setTargetDate(0)
        onCancel()
      }}
    >
      <span className={styles['close-btn']}>
        <CloseOutlined />
      </span>
      <span className={styles['modal-count-down']}>{Math.round(countdown / 1000)}秒</span>
    </div>
  )
}

export default ModalCloseItem
