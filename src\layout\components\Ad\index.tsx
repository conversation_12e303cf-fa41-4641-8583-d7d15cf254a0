import { Swiper } from 'antd-mobile'
import styles from './index.module.scss'

const Ad = () => {
  return (
    <div className={styles['ad-wrapper']}>
      {/* <img
        className={styles['ad-img']}
        src={require('@/assets/images/ad/hos-ad.png')}
        alt='宣教图片'
      /> */}
      <Swiper autoplay loop>
        <Swiper.Item key={0}>
          <div>
            <img
              className={styles['ad-img']}
              src={require('@/assets/images/ad/hos-ad.png')}
              alt='宣教图片'
            />
          </div>
        </Swiper.Item>
        <Swiper.Item key={1}>
          <div>
            <img
              className={styles['ad-img']}
              src={require('@/assets/images/ad/hos-ad1.png')}
              alt='宣教图片'
            />
          </div>
        </Swiper.Item>
        <Swiper.Item key={2}>
          <div>
            <img
              className={styles['ad-img']}
              src={require('@/assets/images/ad/hos-ad2.png')}
              alt='宣教图片'
            />
          </div>
        </Swiper.Item>
        <Swiper.Item key={3}>
          <div>
            <img
              className={styles['ad-img']}
              src={require('@/assets/images/ad/hos-ad3.png')}
              alt='宣教图片'
            />
          </div>
        </Swiper.Item>
      </Swiper>
    </div>
  )
}

export default Ad
