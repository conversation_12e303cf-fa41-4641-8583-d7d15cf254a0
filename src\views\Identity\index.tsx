/*
 * @Description: 核身界面
 */
import { useState, useEffect, useRef } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/store'
import { Row, Col } from 'antd'
import { getCardInfo, getSpeak, jwsGetGps } from '@/services/hardware'
import { getUserInfo, getUserMedInfo, getInHospitalList} from '@/services/user'
import { IdCard, rand } from '@/utils/utils'
import dayjs from 'dayjs'
import CardBox from './components/CardBox'
import TypeModal from './components/TypeModal'
import ModuleHeader from '@/components/ModuleHeader'
import WarmTipsWrap from '@/components/WarmTipsWrap'
import ETipModal from '@/components/ETipModal'
import NumKeyboardDrawer from '@/components/NumKeyboardDrawer'
import loading from '@/utils/loading'
import styles from './index.module.scss'

/**
 * 读卡请求
 * @param fields
 */
const fetchReadCard = async fields => {
  try {
    const response = await getCardInfo({
      ...fields
    })
    if (response.code == '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * GPS请求
 * @param fields
 */
const fetchjwsGetGps = async fields => {
  try {
    const response = await jwsGetGps({
      ...fields
    })
    if (response.code == '0') {
      return response
    }
    return response
  } catch (error) {
    return false
  }
}

/**
 * 请求用户信息
 * @param fields
 */
const handleUserInfo = async (fields, isHideError) => {
  try {
    const response = await getUserInfo(
      {
        ...fields
      },
      isHideError
    )
    if (response.code == '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * 请求用户信息
 * @param fields
 */
const handleHospitalList = async (fields) => {
  try {
    const response = await getInHospitalList(
      {
        ...fields
      },
    )
    if (response.code == '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * 请求用户医保信息
 * @param fields
 */
const handleUserMedInfo = async fields => {
  try {
    const response = await getUserMedInfo({
      ...fields
    })
    if (response.code == '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

const Identity = () => {
  const [search] = useSearchParams()
  const nextpage = search.get('nextpage')
  const navigate = useNavigate()
  const [numPickerVisible, setNumPickerVisible] = useState(false) // 手输键盘
  const [typeModalVisible, setTypeModalVisible] = useState(false)
  const [typeModalType, setTypeModalType] = useState('') // 介质读取弹窗类型
  const [GPSData, setGPSDATA] = useState<string>() // GPS数据
  const timeoutInt = useRef(null)
  const { currentUser, loginDocData } = useSelector((state: RootState) => state.userInfo)
  const dispatch = useDispatch()

  // const TEST_FACE = {
  //   chnlld: '1DBHJRS5I01E0F34A8C00000B959FA87',
  //   ecQrCode: '4243927145468041984001330000',
  //   ecToken: '330000ecocrda4201j0a8fab0a0000cc4dca60',
  //   idType: '01',
  //   insuOrg: '330110',
  //   ecIndexNo: 'OF73433EDD6C84CB4447FDOFD745AFOE',
  //   userName: '胡嘉文',
  //   idNo: '330802199402285015'
  // }

  const TEST_FACE = {
    "birthday": null,
    "chnlld": null,
    "ecToken": "330000fecj860641mc1294ab0a0000a7acb7e9",
    "idType": "01",
    "gender": null,
    "cityCode": "330110",
    "userName":"胡嘉文",
    "idNo":"330802199402285015",
    "ecQrCode": null,
    "insuOrg": "330110",
    "ecIndexNo":"0F73433EDD6C84CB4447FD0FD745AF0E",
    "nationality": null,
    "insCityCode": "330110",
    "email": null,
    "isSelf": true
  }

  useEffect(() => {
    dispatch({
      type: 'list/resetData'
    })
    dispatch({
      type: 'userInfo/resetData'
    })
    window.config.isSpeak && getSpeak({ content: '请选择登录方式' })
    return () => {
      loading.end()
      clearTimeout(timeoutInt.current)
    }
  }, [])

  // 查档
  const firstQueryUser = async (normalValue, readCardData) => {
    // 如果是小票补打去查历史记录
    if(nextpage === '/ticket/list'){
      const hosList = await handleHospitalList(
        {
          patientIDCard: normalValue.cardNo,
          saID: window.config.SAID
        }
      )
      if(!hosList) return
      // 存储病人信息
      dispatch({
        type: `userInfo/setQueryUser`,
        payload: hosList
      })
      // 存储读卡信息
      dispatch({
        type: `userInfo/setReadCard`,
        payload: { ...readCardData }
      })
      navigate(nextpage)
      return;
    }
    const isHideError = nextpage === '/hospital/checkin' || false
    const userInfo = await handleUserInfo(
      {
        ...normalValue,
        saID: window.config.SAID
      },
      isHideError
    )
    if (!userInfo) {
      if (nextpage === '/hospital/checkin') {
        // 查询不到就去入院登记
        // 查询医保信息
        const medInfo = await handleUserMedInfo({
          ...readCardData.mdtrt_data,
          yonghuID: loginDocData?.YONGHUID,
          kaLeiXing: readCardData.mdtrt_cert_type,
          saID: window.config.SAID
        })
        if(!medInfo) return;
        // 存储医保信息
        dispatch({
          type: `userInfo/setMedicalInfo`,
          payload: { ...medInfo }
        })
        // 存储读卡信息
        dispatch({
          type: `userInfo/setReadCard`,
          payload: { ...readCardData }
        })
        navigate(nextpage)
        return
      } else {
        // 其他业务查询不到病人信息就返回
        return false
      }
    }
    // 如果查询到病人信息、功能是入院登记则提示病人已在院
    if (nextpage === '/hospital/checkin') {
      ETipModal('病人已在院，无需办理入院登记！', 'error')
      return
    }
    // 如果查询到病人信息且已经是预出院状态，功能是费用录入则提示无法录入新的费用
    if( (nextpage === '/delivery/list') && userInfo.bingrenzt === "0") {
      ETipModal('病人已预出院，无法录入新的费用！', 'error')
      return
    }
    // 查询医保信息 带上病人ID
    const medInfo = await handleUserMedInfo({
      ...readCardData.mdtrt_data,
      yonghuID: loginDocData?.YONGHUID,
      kaLeiXing: readCardData.mdtrt_cert_type,
      saID: window.config.SAID,
      patientID: userInfo?.patientID,
    })
    // if(!medInfo) return;
    // 存储病人信息
    dispatch({
      type: `userInfo/setQueryUser`,
      payload: { ...userInfo }
    })
    // 存储医保信息
    dispatch({
      type: `userInfo/setMedicalInfo`,
      payload: { ...medInfo }
    })
    // 存储读卡信息
    dispatch({
      type: `userInfo/setReadCard`,
      payload: { ...readCardData }
    })
    navigate(nextpage)
  }

  // 扫码核身  接⼝描述：YW.getUserInfoByEC 是打开扫码墩界⾯，引导⽤户扫码，返回码值
  const handleScanCode = () => {
    console.log('拉起医保电子凭证方法')
    loading.start('扫码启动中，请稍候...')
    if (window.config.isOpenRealReadId) {
      window.YW.getUserInfoByEC({
        success: async (res: any) => {
          loading.end()
          const { idNo, userName, ecToken } = res || {}
          firstQueryUser(
            {
              cardNo: idNo,
              READDATA: JSON.stringify(res)
            },
            {
              certNo: idNo,
              certName: userName,
              mdtrt_cert_no: JSON.stringify(res),
              mdtrt_cert_type: '8',
              mdtrt_type_name: '医保电子凭证',
              mdtrt_data: res
            }
          )
        },
        fail: err => {
          loading.end()
          // 电子凭证验证失败，返回刷医保电子凭证失败原因，
          console.log('fail')
          console.log(err)
          ETipModal(JSON.stringify(err), 'error')
        }
      })
    } else {
      // 模拟电子凭证
      loading.end()
      setTypeModalType('elMedCard')
      setTypeModalVisible(true)
      timeoutInt.current = setTimeout(() => {
        setTypeModalVisible(false)
        firstQueryUser(
          {
            cardType: '6',
            cardNo: '332601196712190610',
            READDATA: ''
          },
          {
            certNo: '332601196712190610',
            certName: '张三',
            mdtrt_cert_no: '',
            mdtrt_cert_type: '8',
            mdtrt_type_name: '医保电子凭证',
            mdtrt_data: ''
          }
        )
      }, 3000)
    }
  }

  // 刷脸核身 获取医保电子凭证  接⼝描述：YW.getUserInfoByFace 是通过刷脸获取电⼦凭证相关参数的接⼝。
  const handleBrushTheFace = () => {
    // 调用YW.getUserInfoByFace 引导用户刷脸，返回bizNo, authNo
    loading.start('刷脸启动中，请稍候...')
    if (window.config.isOpenRealReadId) {
      window.YW.getUserInfoByFace({
        success: (res: any) => {
          console.log(res)
          loading.end()
          const { idNo, userName, ecToken } = res || {}
          firstQueryUser(
            {
              cardNo: idNo,
              READDATA: JSON.stringify(res)
            },
            {
              certNo: idNo,
              certName: userName,
              mdtrt_cert_no: JSON.stringify(res),
              mdtrt_cert_type: '8',
              mdtrt_type_name: '医保刷脸',
              mdtrt_data: res
            }
          )
        },
        fail: err => {
          loading.end()
          // 刷脸获取信息失败
          console.log('fail')
          console.log(err)
          ETipModal(JSON.stringify(err), 'error')
        }
      })
    } else {
      // 模拟刷脸
      loading.end()
      setTypeModalType('face')
      setTypeModalVisible(true)
      timeoutInt.current = setTimeout(() => {
        setTypeModalVisible(false)
        firstQueryUser(
          {
            // cardNo: '******************',
            cardNo: '330802199402285015',
            // certNo: '330802199402285015',
            READDATA: ''
          },
          {
            certNo: '330802199402285015',
            // certNo: '330802199402285015',
            certName: '胡嘉文',
            mdtrt_cert_no: '',
            mdtrt_cert_type: '8',
            mdtrt_type_name: '医保刷脸',
            mdtrt_data: TEST_FACE
          }
        )
      }, 3000)
    }
  }

  const handleSocialCard = async () => {
    loading.start('读卡中，请稍候...')
    // 请求读卡
    if (window.config.isOpenRealReadId) {
      const res = await fetchReadCard({})
      loading.end()
      if (!res) {
        return ETipModal('读卡失败，请重新再试一次', 'error')
      }
      const cardInfo = res?.data
      const data = {
        canOpen: true,
        cardIDCode: cardInfo?.cardIdentifier ?? '',
        cardNum: cardInfo?.cardNum ?? '',
        cardType: cardInfo?.cardType ?? '',
        gender: cardInfo?.sex ?? '',
        idNum: cardInfo?.cardID ?? '',
        name: cardInfo?.name ?? '',
        psamTN: '',
        success: true,
        flag: '1',
        ylbxtcqbm: cardInfo?.cardIdentifier?.substr(0, 6) ?? ''
      }
      firstQueryUser(
        {
          cardType: '2',
          cardNo: JSON.stringify(data),
          READDATA: JSON.stringify(cardInfo)
        },
        {
          certNo: cardInfo.cardID,
          certName: cardInfo.name,
          mdtrt_cert_no: JSON.stringify(data),
          mdtrt_cert_type: '3',
          mdtrt_type_name: '医保卡',
          mdtrt_data: data
        }
      )
    } else {
      // 模拟读卡
      loading.end()
      setTypeModalType('socialCard')
      setTypeModalVisible(true)
      timeoutInt.current = setTimeout(() => {
        setTypeModalVisible(false)
        firstQueryUser(
          {
            cardNo: '33262219461011672X'
          },
          {
            certNo: '33262219461011672X',
            certName: '张三',
            mdtrt_cert_no: '33262219461011672X', // 身份证
            mdtrt_cert_type: '2',
            mdtrt_type_name: '医保卡',
            mdtrt_data: '1'
          }
        )
      }, 3000)
    }
  }

  const handleDataTest = () => {
    console.log('测试数据')
    let fields
    if (nextpage.indexOf('/hoscheckin/confirm') > -1) {
      fields = {
        cardNo: '633533197808080820',
        cardType: '1'
      }
    } else {
      // fields = {
      //   cardType: '2',
      //   cardNo:
      //     '{"CanOpen":true,"CardIDCode":"330800D156000005901865267BE8841E","CardNum":"*********","CardType":"3","Gender":"","IDNum":"330822195605025712","Name":"甘秀益","PsamTN":"330800907222","Success":true,"flag":"1","ylbxtcqbm":"330800"}\n',
      //   deviceCode: window.config.DEVICE_CODE
      // }
      fields = {
        cardType: '1',
        cardNo: '522633202401010816'
      }
    }
    // const readData = {
    //   certNo: '330822195605025712',
    //   certName: '甘秀益',
    //   mdtrt_cert_no:
    //     '{"CanOpen":true,"CardIDCode":"330800D156000005901865267BE8841E","CardNum":"*********","CardType":"3","Gender":"","IDNum":"330822195605025712","Name":"甘秀益","PsamTN":"330800907222","Success":true,"flag":"1","ylbxtcqbm":"330800"}\n', //电子医保凭证
    //   mdtrt_cert_type: '2', // 类型
    //   mdtrt_type_name: '社保卡',
    //   mdtrt_data:
    //     '{"CanOpen":true,"CardIDCode":"330800D156000005901865267BE8841E","CardNum":"*********","CardType":"3","Gender":"","IDNum":"330822195605025712","Name":"甘秀益","PsamTN":"330800907222","Success":true,"flag":"1","ylbxtcqbm":"330800"}\n'
    // }
    const readData = {
      certNo: '522633202401010816',
      certName: '测试',
      mdtrt_cert_no: '522633202401010816', //电子医保凭证
      mdtrt_cert_type: '2', // 类型
      mdtrt_type_name: '身份证',
      mdtrt_data: '522633202401010816'
    }
    firstQueryUser(fields, readData)
  }

  const handleCodeInput = () => {
    // 手输住院号
    handleOpenNumPickerDrawer()
  }

  // 手输内容抽屉
  const handleOpenNumPickerDrawer = () => {
    setNumPickerVisible(true)
  }

  const handleCloseNumPickerDrawer = () => {
    setNumPickerVisible(false)
  }

  const getGPS = async () => {
    const GPSDATA = await fetchjwsGetGps({})
    if (GPSDATA && GPSDATA.data) {
      setGPSDATA(JSON.stringify(GPSDATA.data))
    } else {
      setGPSDATA(GPSDATA.msg || '获取定位失败')
    }
  }

  // 扫住院证处理
  const handleNormalScanCode = () => {
    console.log('拉起普通扫码方法')
    loading.start('扫码中，请稍候...')
    timeoutInt.current = setTimeout(() => {
      // navigate(nextpage)
      const fields = {
        cardNo: '123456',
        cardType: '2'
      }
      const readData = {
        certNo: '522633202401010816',
        certName: '测试',
        mdtrt_cert_no: '522633202401010816', //电子医保凭证
        mdtrt_cert_type: '2', // 类型
        mdtrt_type_name: '身份证',
        mdtrt_data: '522633202401010816'
      }
      loading.end()
      firstQueryUser(fields, readData)
    }, 2000)
    window.YW.getScanCode({
      success: async res => {
        console.log(res)
        const { scanCode } = res || {}
        const fields = {
          cardNo: scanCode,
          cardType: '2'
        }
        const readData = {
          certNo: '522633202401010816',
          certName: '测试',
          mdtrt_cert_no: '522633202401010816', //电子医保凭证
          mdtrt_cert_type: '2', // 类型
          mdtrt_type_name: '身份证',
          mdtrt_data: '522633202401010816'
        }
        firstQueryUser(fields, readData)
      },
      fail: err => {
        // 扫码失败
        console.log('fail')
        console.log(err)
        ETipModal(JSON.stringify(err), 'error')
      }
    })
  }

  const onConfirmNum = value => {
    // 获取到日期
    // 成功 关闭 close
    handleCloseNumPickerDrawer()
    firstQueryUser(
      {
        cardType: '3',
        cardNo: value
      },
      {
        certName: '测试',
        certNo: '33010619902255055',
        // mdtrt_cert_no: ecToken,
        mdtrt_cert_type: '1' // 自费
        // mdtrt_data: JSON.stringify(res)
      }
    )
  }

  const handleCloseTypeModal = () => {
    clearTimeout(timeoutInt.current)
    setTypeModalVisible(false)
  }

  const handlePrintTest = () => {
    console.log('调用打印方法')
    const result = [
      {
        text: window.config.HOS_NAME,
        size: 30,
        isBold: true,
        isUnderLine: false,
        align: 1
      },
      {
        text: '门诊挂号凭证',
        size: 30,
        isBold: true,
        isUnderLine: false,
        align: 1
      },
      '-----------------------',
      `患者姓名: 方彬祥`,
      `患者性别: 男`,
      `身份证号: 330802********5015`,
      `-----------------------`,
      `科室名称: 消化内科`,
      `医生名称: 刘芳红`,
      `就诊时间: 2024-04-23 10:30-11:00`,
      `就诊序号: 24`,
      {
        text: `就诊位置: 城中院区门诊一楼3号诊区`,
        size: 28,
        isBold: true,
        isUnderLine: false,
        align: 0
      },
      `具体就诊时间以诊区叫号为准`,
      `【注】 请提前30分钟到诊区签到成功后侯诊`,
      `-----------------------`,
      `挂号金额: 25.0元`,
      `医保支付: 25.0元`,
      `自费支付: 0.0元`,
      `挂号订单号: 5098021`,
      `打印时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
      `设备号: ${window.config.DEVICE_CODE}`,
      {
        text: '诊区报到码',
        size: 25,
        isBold: false,
        isUnderLine: false,
        align: 0
      },
      {
        type: 'qrcode',
        value: '2404223252'
      },
      {
        type: 'barcode',
        value: '2404223252',
        barType: 4
      }
    ]
    window.YW.print({
      type: 0,
      data: result,
      success: res => {
        ETipModal('打印成功', 'success')
      },
      fail: err => {
        console.log(err)
        ETipModal(err.message, 'error')
      }
    })
  }

  return (
    <div className={styles['identity-wrapper']}>
      <ModuleHeader title={''} />
      <h2 className={styles['title']} onClick={() => handleDataTest()}>
        请选择登录方式
      </h2>
      <div className={styles['content-center']}>
        {/* {nextpage.indexOf('/hospital/checkin') > -1 ? (
          <div className={styles['sm-ss-btn']} onClick={handleNormalScanCode}>
            <img
              className={styles['sm-icon']}
              src={require('@/assets/images/icons/sm-icon.png')}
              alt='扫码小图标'
            />
            <span className={styles['text']}>&nbsp;&nbsp;扫入院证条形码</span>
          </div>
        ) : nextpage.indexOf('/ticket/list?type=4') > -1 ||
          nextpage.indexOf('/ticket/list?type=6') > -1 ? (
          <Row gutter={[18, 18]} justify='center'>
            <Col span={24} onClick={handleCodeInput}>
              <CardBox title='手输住院号' type={'inhosInput'} />
            </Col>
          </Row>
        ) : ( */}
        {
          <>
            {window.config.isOpenSocialCard ? (
              <Row gutter={[18, 18]} justify='center'>
                <Col span={12} onClick={handleBrushTheFace}>
                  <CardBox title='刷脸登录(医保)' type={'face'} isRecommend={true} />
                </Col>
                <Col span={12} onClick={handleScanCode}>
                  <CardBox title='医保电子凭证' type={'elMedCard'} />
                </Col>
              </Row>
            ) : (
              <>
                <div className={styles['brush-face-btn']} onClick={handleBrushTheFace}>
                  <img src={require('@/assets/images/pay/chs-icon.png')} alt='医保图标' />
                  <span className={styles['text']}>医保刷脸</span>
                  <div className={styles['tuijian-box']}>
                    <img src={require('@/assets/images/others/tuijian.png')} alt='推荐使用图标' />
                  </div>
                </div>
                <div className={styles['more-title-img']}>
                  <img src={require('@/assets/images/others/line.png')} alt='' />
                  <span>其他登录方式</span>
                  <img src={require('@/assets/images/others/line.png')} alt='' />
                </div>
              </>
            )}
            <Row gutter={[0, 0]} justify='start'>
              <Col span={12} onClick={handleScanCode}>
                <CardBox title='医保码' type={'elMedCard'} />
              </Col>
              <Col span={12} onClick={handleSocialCard}>
                <CardBox title='社保卡' type={'socialCard'} />
              </Col>
              {/* <Col span={12} onClick={handleReadCard}>
                <CardBox title='身份证' type={'idCard'} />
              </Col> */}
              {/* {nextpage.indexOf('/hospital') > -1 && (
                <Col span={12} onClick={handleCodeInput}>
                  <CardBox title='手输住院号' type={'inhosInput'} />
                </Col>
              )} */}
              {/* <Col span={12} onClick={getGPS}>
                <CardBox title='定位测试' type={'printTest'} />
              </Col> */}
              {/* {GPSData} */}
            </Row>
          </>
        }
      </div>
      {numPickerVisible && (
        <NumKeyboardDrawer
          visible={numPickerVisible}
          title={'住院号'}
          onCancel={handleCloseNumPickerDrawer}
          onConfirm={onConfirmNum}
        />
      )}
      {typeModalVisible && (
        <TypeModal
          modalVisible={typeModalVisible}
          modalType={typeModalType}
          onCancel={handleCloseTypeModal}
        />
      )}
    </div>
  )
}

export default Identity
