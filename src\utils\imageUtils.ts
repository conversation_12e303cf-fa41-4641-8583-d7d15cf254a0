// 图片处理工具函数

/**
 * 压缩图片到指定大小
 * @param file 原始文件
 * @param maxSize 最大文件大小（字节）
 * @param quality 压缩质量 0-1
 * @returns 压缩后的文件
 */
export function compressImage(file: File, maxSize: number = 5 * 1024 * 1024, quality: number = 0.8): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img;
      const maxDimension = 1920; // 最大尺寸

      if (width > maxDimension || height > maxDimension) {
        if (width > height) {
          height = (height * maxDimension) / width;
          width = maxDimension;
        } else {
          width = (width * maxDimension) / height;
          height = maxDimension;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制图片
      ctx?.drawImage(img, 0, 0, width, height);

      // 转换为blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });

            // 如果压缩后仍然超过限制，进一步降低质量
            if (compressedFile.size > maxSize && quality > 0.1) {
              compressImage(file, maxSize, quality - 0.1)
                .then(resolve)
                .catch(reject);
            } else {
              resolve(compressedFile);
            }
          } else {
            reject(new Error('图片压缩失败'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * 验证图片文件
 * @param file 文件对象
 * @returns 验证结果
 */
export function validateImageFile(file: File): { valid: boolean; message?: string } {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    return { valid: false, message: '请选择图片文件' };
  }

  // 检查支持的格式
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
  if (!supportedTypes.includes(file.type)) {
    return { valid: false, message: '不支持的图片格式，请选择jpg、png、gif、bmp、webp格式的图片' };
  }

  // 检查文件大小（10MB限制，压缩前）
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    return { valid: false, message: '图片文件过大，请选择小于10MB的图片' };
  }

  return { valid: true };
}

/**
 * 获取图片预览URL
 * @param file 文件对象
 * @returns 预览URL
 */
export function getImagePreviewUrl(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * 释放预览URL
 * @param url 预览URL
 */
export function revokeImagePreviewUrl(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * 获取图片信息
 * @param file 文件对象
 * @returns 图片信息
 */
export function getImageInfo(file: File): Promise<{
  width: number;
  height: number;
  size: number;
  type: string;
}> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
        size: file.size,
        type: file.type,
      });
    };
    img.onerror = () => reject(new Error('无法获取图片信息'));
    img.src = URL.createObjectURL(file);
  });
}
