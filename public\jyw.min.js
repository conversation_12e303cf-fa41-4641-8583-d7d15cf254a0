!function(i,e){"undefined"!=typeof module&&"object"==typeof exports?module.exports=e():"function"==typeof define&&(define.cmd||define.amd)?define(e):i.YW=e()}(this,function(){var i=function(i){window.WebViewJavascriptBridge?i():document.addEventListener("WebViewJavascriptBridgeReady",function(){i()},!1)};return{init:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("init",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},getSerialNumber:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getSerialNumber","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getYBNumber:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getYBNumber","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getUserInfoByIdcard:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getUserInfoByIdcard","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getAuthNoByFace:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getAuthNoByFace",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},activeECByFace:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("activeECByFace","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getUserInfoByFace:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getUserInfoByFace","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getUserInfoByEC:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getUserInfoByEC","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getScanCode:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getScanCode",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},print:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("print",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},backHome:function(){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("backHome","",function(i){})})},backMenu:function(){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("backMenu","",function(i){})})},setTouchTimeout:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("setTouchTimeout",JSON.stringify(e),function(i){})})},getMac:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getMac","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},scanDurative:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("scanCodeDurative",e.type,function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},qrCodeReceiver:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.registerHandler("qrCodeReceiver",function(i,a){e.success(i),e.fail(i)})})},getUserInfoBySSCard:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getUserInfoBySSCard","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},lightController:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("lightController",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.msg):e.fail(a)})})},getNetWorkInfo:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getNetWorkInfo","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getBrightness:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getBrightness","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},setBrightness:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("setBrightness",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.msg):e.fail(a)})})},getVolume:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getVolume","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},setVolume:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("setVolume",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.msg):e.fail(a)})})},tts:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("tts",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.msg):e.fail(a)})})},h5Notify:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("h5Notify",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.msg):e.fail(a)})})},USBNotify:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.registerHandler("usbReceiver",function(i,a){e.success(i),e.fail(i)})})},printA4:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("printPaper",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},paperPrinterCallback:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.registerHandler("paperPrinterCallback",function(i,a){var n=JSON.parse(i);e.success(n),e.fail(n)})})},getUserByEccode:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getUserInfoByEC","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getUserByAuthno:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getUserByAuthno",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getBgPrinterStatus:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getBgPrinterStatus","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},setSensor:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("setSensor",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},sensorListener:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.registerHandler("sensorListener",function(i,a){var n=JSON.parse(i);e.success(n),e.fail(n)})})},printReceiptByUrl:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("printReceiptByUrl",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},getDeviceType:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getDeviceType","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},readMagCard:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("readMagCard","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},caSignature:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("caSignature",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getSecurityCardPublicKey:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getSecurityCardPublicKey","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getOrginfoBySN:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getOrginfoBySN","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},openAltimeter:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("openAltimeter","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},getPrinterStatus:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getPrinterStatus",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code&&200==a.data.printerCode?e.success(a.data):e.fail(a)})})},getBarCodeByFace:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getBarCodeByFace",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},printPaperColor:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("printPaperColor",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},openCamera:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("openCamera",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},codeKeyBoard:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("codeKeyBoard",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},setDefaultNetworkType:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("setDefaultNetworkType",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},getFacialPhoto:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getFacialPhoto",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},printLabel:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("printLabel",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},getLocationInfo:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getLocationInfo","",function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},openWarningTone:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("openWarningTone","",function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},grantMedicalRecordBook:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("grantMedicalRecordBook","",function(i){var a=JSON.parse(i);200==a.code?e.success(a):e.fail(a)})})},getAllInfoByFace:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("getAllInfoByFace","",function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})},medicareSettlement:function(e){i(function(){window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("medicareSettlement",JSON.stringify(e),function(i){var a=JSON.parse(i);200==a.code?e.success(a.data):e.fail(a)})})}}});