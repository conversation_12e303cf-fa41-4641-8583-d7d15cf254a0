/*
 * @Author: lhy
 * @Date: 2022-06-14 11:20:35
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2022-08-03 10:26:08
 * @FilePath: \源码4网络打印\src\services\print.js
 * @Description:
 */
import request from '@/utils/request'
import multiRequest from '@/utils/multiRequest'

// // 打印接口
export async function sendPrint(params) {
  const data = { data: [...params], ip: window.config.SERIAL_NO }
  console.log(data)
  // return
  return request({
    url: `/MsPrinter`,
    method: 'POST',
    data: data,
    isPrint: true,
    isHideLoading: true
  })
}

// 查询多个住院信息的凭条信息 
export async function getMultiCreditsStr(params, list) {
  return multiRequest({
      url: `/pay/getCreditsStr.dp`,
      method: 'GET',
      params: {...params, deviceCode: window.config.DEVICE_CODE},
      isHideLoading: params.isLoading,
      loadingText: '正在加载中，请稍候...',
  }, list);
}

// 保存凭条信息
export async function saveCreditsPostStr(params) {
  return request({
    url: `/pay/saveCreditsPostStr.dp`,
    method: 'POST',
    data: { ...params, deviceCode: window.config.DEVICE_CODE },
    isHideLoading: true
  })
}

// 查询凭条信息
export async function getCreditsStr(params) {
  return request({
    url: `/pay/getCreditsStr.dp`,
    method: 'GET',
    params,
    loadingText: '正在加载中，请稍候...',
    isHideLoading: params.isLoading,
  })
}

// 增加打印次数
export async function increasedPrintPayCount(params) {
  return request({
    url: `/pay/increasedPrintPayCount.dp`,
    method: 'GET',
    params,
    isHideLoading: true
  })
}
