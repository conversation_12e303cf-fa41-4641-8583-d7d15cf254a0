/*
 * @Description: 操作提醒框
 */
import { Modal, App } from 'antd'
import ModalCloseItem from '../ModalCloseItem'
import styles from './index.module.scss'
import { getSpeak } from '@/services/hardware'
import { PositionType } from 'antd/es/image/style'
import { useEffect } from 'react'

const Wrapp = ({ msg, type }) => {
  return type === 'success' ? (
    <div className={styles['info-tip-modal-wrap']}>
      <img src={require(`@/assets/images/modal/success.png`)} alt='成功图标' />
      <p className={styles['tip-text']}>{msg}</p>
    </div>
  ) : type === 'error' ? (
    <div className={styles['info-tip-modal-wrap']}>
      <img src={require(`@/assets/images/modal/error.png`)} alt='失败图标' />
      <p className={styles['tip-text']}>{msg}</p>
    </div>
  ) : type === 'finish' ? (
    <div className={styles['finish-modal-wrap']}>
      <div className={styles['modal-title']}>
        <img
          className={styles['icon']}
          src={require('@/assets/images/modal/success-sm.png')}
          alt='成功'
        />
        <h2 className={styles['text']}>{msg}</h2>
      </div>
      <div className={styles['modal-img-box']}>
        <img src={require('@/assets/images/gifs/gif-print.gif')} alt='凭条出口动图' />
      </div>
    </div>
  ) : null
}

const modalStyles = {
  header: {},
  body: {
    paddingTop: 30,
    minHeight: '220px'
  },
  mask: {
    position: 'absolute' as PositionType
  },
  wrapper: {
    position: 'absolute' as PositionType
  },
  content: {
    borderRadius: 16,
    minHeight: '250px',
    padding: 8
  }
}

const ETipModal = (
  msg: string,
  type: 'error' | 'success' | 'finish' | 'checkin' | undefined,
  closeFun?: () => void
) => {
  let _modal = null
  window.config.isSpeak && type !== 'error' && getSpeak({ content: msg })
  _modal = Modal.info({
    destroyOnClose: true,
    width: 660 / window.config.minPixelValue,
    centered: true,
    closable: false,
    maskClosable: true,
    icon: (
      <ModalCloseItem
        num={window.config.TIP_OVER_TIME}
        onCancel={() => {
          _modal && _modal.destroy()
          _modal = null
          if (closeFun) {
            closeFun()
          }
        }}
      />
    ),
    content: <Wrapp msg={msg} type={type ?? 'error'} />,
    onCancel: () => {
      _modal && _modal.destroy()
      _modal = null
      if (closeFun) {
        closeFun()
      }
    },
    footer: null,
    getContainer: document.getElementById('_module') as HTMLElement,
    styles: modalStyles
  })

  return _modal
}

export default ETipModal
