window.config = {
  // STATE_API_NAME: "http://127.0.0.1:8090", // 状态服务地址
  STATE_API_NAME: "http://127.0.0.1:8090",
  PRINT_API_NAME: "http://127.0.0.1:8090", // 网络打印机接口服务地址
  HARDWARE_API_NAME: "http://127.0.0.1:8090", // 刷脸，刷医保电子凭证，硬件服务接口地址
  // API_NAME: "http://192.168.1.42:8080/hsss-hospital-api-zj", // 赵栋彦本地接口服务
  // API_NAME: "http://*************:8086/hsss-hospital-api-zj",
  API_NAME: "http://************:28086/hsss-hospital-api-zj",
  // API_NAME: "https://aliapi.hndsrmyy.com/hsss-hospital-api-mib",   //线上业务接口服务  
  REQUEST_TIME_OUT: 60000, // 接口响应超时时间
  RESERVE_DAY_COUNT: 7, // 预约挂号天数
  OVER_TIME: 120, // 界面超时时间
  READ_CARD_OVER_TIME: 60, // 读卡超时时间
  PAY_OVER_TIME: 60, // 支付超时时间
  TIP_OVER_TIME: 5, // 提示超时时间
  QUERY_SAVEPRINT_TIMERANGE: 7, //查询补打周期
  QUERY_REG_TIMERANGE: 7, //查询挂号记录列表周期
  IS_VERTICAL: 1, // 横屏0 竖屏1 宽屏2   横屏 1920 * 1080  1280 * 1024  竖屏 1080 * 1920   宽屏 1920 * 1080  禁止配置
  NO_TOUCH_TIME_OUT: 43200, // 无操作超时时间
  SERIAL_NO: "HMDF4AS201014041366", // SN号 门诊 HMDF4AS201014041366   // 住院 HMDF4AS201014042315
  DEVICE_CODE: "", // 设备编号 禁止配置
  HIS_DEVICE_CODE: "SC01141", // HIS设备编号    
  DEVICE_TYPE: "", //设备类型 禁止 配置
  SAID: "", // 医院SAID
  API_VERSION: "tzjj", // 衢州手持终端
  VERSION: "0.0.6",
  SPBILL_CREATE_IP: '**************', //设备IP
  // DEVICE_NAME: "住院床旁结算系统",
  DEVICE_NAME: "\"码上医\"智慧医疗移动微诊室",
  HOS_NAME: '中心医院', //院区名称
  CAMERA_ROTATE_ANGLE: '0deg', //摄像头旋转角度
  CLOSE_PROGRAME_PWD: "123456", //首页退出程序密码 字符串类型
  ORG_ID: "H46020400009", //机构 ID，医保定点 机构代码
  NET_PRINTER_IP: "**************", //网络打印机IP地址
  minPixelValue: 2, //适配安卓分辨率屏幕尺寸
  isShowVconsole: false, //是否打开控制台 Vconsole
  showTestSmInput: true, // 
  isDebug: false, //是否测试数据
  isOpenSocialCard: false, //是否开启社保卡
  isOpenRealReadId: false, // 是否开启真实读卡介质
  isSpeak: false, // 是否播放语音
  MZ_TYPE: "6", //医保终端门诊type
  SERVICE_PHONE: "4008-678-511", //服务热线
  TECHNICAL_SUPPORT: "码上医", //技术支持文案
  PAGE_SIZE: 100,
  PAGE_MAX: 999,
  STEPPER_NUM : 10, // 记步器最大值
}
