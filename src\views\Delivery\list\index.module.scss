@import '@/assets/scss/base.scss';

.container {
  // height: calc(100vh - 60px);
  background-color: #f6f6f6;
  border-top-right-radius: 15px;
  border-top-left-radius: 15px;
  overflow: hidden;
  .wrapper {
    height: calc(100vh - 130px);
    border-radius: 6px;
    overflow-x: hidden;
    overflow-y: scroll;
    .confirm {
      margin: 0 auto;
      width: calc(680px / $pixel);
      position: relative;
      background-color: #fff;
      border-radius: 10px;
      border: 1px solid #99c4ff;
      padding: 40px 10px 0;
      .treetop {
        position: absolute;
        top: 0;
        right: 0;
        img {
          width: 70px;
        }
      }
      .line-wrapper {
        width: 100%;
        position: absolute;
        top: 185px;
        left: 50%;
        transform: translateX(-50%);
        align-items: center;
        z-index: 10;
        .line {
          width: 88%;
          height: 1px;
          flex: 1;
          margin: 0 auto;
          border-bottom: 1px solid #cfcfcf;
        }
        .circle-box {
          width: 15px;
          height: 15px;
          background-color: #f6f6f6;
          border: 1px solid #99c4ff;
          border-radius: 50%;
          &.left {
            position: absolute;
            bottom: -8px;
            left: -8px;
            clip: rect(0 15px 17px 7px); //图形裁剪
          }
          &.right {
            position: absolute;
            bottom: -8px;
            right: -8px;
            clip: rect(0 8px 15px 0); //图形裁剪
          }
        }
      }
      .title-wrapper {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 5px;
        font-size: 19px;
        .title {
          width: 100px;
          height: 20px;
          padding-right: 15px;
          text-align: justify;
          color: #979797;
          position: relative;
          & i {
            display: inline-block;
            width: 100%;
          }
          &::after {
            position: absolute;
            top: 1px;
            left: 90px;
            content: ':';
            color: #666;
            display: inline-block;
          }
        }
        .black-span {
          display: inline-block;
        }
      }
      .label {
        display: flex;
        align-items: center;
        img {
          width: 17px;
          margin-right: 3px;
        }
        span {
          line-height: calc(45px / 2);
          font-size: 18px;
          font-weight: 500;
        }
      }
      .total {
        display: flex;
        justify-content: center;
        margin-top: 5px;
        align-items: center;
        font-size: 17px;
        .red-text {
          display: inline-block;
          font-size: 16px;
          color: #ff8d1a;
          margin-left: 7px;
        }
      }
      .black-txt {
        font-size: 13px;
        color: #333333;
      }
      .blue-text {
        display: inline-block;
        margin-left: 10px;
        color: #1677ff;
      }
      .red-text {
        display: inline-block;
        font-size: 13px;
        color: #ff8d1a;
        margin-left: 15px;
      }
      .info-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 280px;
        height: 100px;
        margin: 48px auto 0;
        text-align: center;
        color: #ffffff;
        border-radius: 50px;
        background-color: #1677ff;
        font-size: 42px;
      }
      .grid-demo-item-block {
        text-align: center;
      }
      .delete {
        position: relative;
        img {
          position: absolute;
          top: 6px;
          right: 5px;
        }
      }
      .image {
        width: 100%;
        height: 180px;
        padding: 10px;
        margin: 0 auto;
        background-color: #f4f4f4;
        text-align: center;
        border-radius: 6px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .input-box {
      display: flex;
      align-items: center;
      // background-image: url("../../assets/images/icons/qc-icon.png");
    }
    .phone {
      .left {
        color: #333333;
        min-width: max-content;
        font-size: 18px;
      }
    }
    :global {
      .ant-input {
        width: 170px;
        background-color: transparent;
        border-radius: 6px;
        // border: 1px solid #1677ff;
        font-size: 14px;
        ::placeholder {
          color: #999999;
        }
      }
      .adm-text-area-element {
        padding: 5px;
        border: 1px solid #e8e8e8;
      }
      .adm-form {
        --border-top: none;
        ---border-bottom: none;
      }
      .adm-list-item-content-prefix {
        width: var(--prefix);
        flex: none;
        padding-right: var(--prefix-padding-right);
      }
      .adm-list-header {
        color: #1677ff;
        font-size: 18px;
        font-weight: bold;
        padding: 6px 0;
      }
      .adm-list-item {
        padding-left: 10px;
      }
      .adm-form-item.adm-form-item-vertical .adm-form-item-label {
        font-size: 18px;
        margin-bottom: 4px;
        font-weight: 600;
      }
    }
    .btn {
      width: 100%;
      margin: 34vh auto 0;
    }
  }
}
