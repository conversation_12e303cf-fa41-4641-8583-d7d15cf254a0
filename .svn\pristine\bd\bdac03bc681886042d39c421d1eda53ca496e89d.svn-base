/*
 * @Description: 键盘输入门诊号/手机号
 */
import { useEffect, useState } from 'react'
import { Drawer, Row, Col, Flex } from 'antd'
import ETipModal from '../ETipModal'
import ModalCloseItem from '../ModalCloseItem'
import styles from './index.module.scss'

interface Props {
  visible?: boolean
  title?: string
  length?: any
  type?: 'number' | 'money' | 'password'
  onConfirm?: any
  onCancel: () => void
  value?: any
}

const numberKeys: Array<string> = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0']
const moneyKeys: Array<string> = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '.', '00']

const NumKeyboardDrawer: React.FC<Props> = ({
  visible,
  title,
  onCancel,
  onConfirm,
  length,
  type,
  value
}) => {
  useEffect(() => {
    if (value && value.length > 0) {
      setmyKeys(value)
    }
  }, [value])
  const [keysType, setKeysType] = useState<any>([])
  const [dataKey, setmyKeys] = useState<any>('')
  const getKey = (event: any) => {
    if (dataKey.length > length) {
      ETipModal('输入内容过长！', 'error')
      return
    }
    const target = event.target
    const textContent = target.textContent
    setmyKeys([...dataKey, textContent].join(''))
  }
  const getBackspace = () => {
    const item = [...dataKey]
    item.splice(item.length - 1, 1)
    setmyKeys(item.join(''))
  }
  const getConfirm = () => {
    if (dataKey.length === 0 || dataKey === '') {
      ETipModal('请输入' + title, 'error')
      return
    }
    // if (dataKey.length > length) {
    //     WarngTipModal(`请输入${length}位${title}`)
    //     return;
    // }
    onConfirm(dataKey)
  }
  return (
    <Drawer
      title={null}
      destroyOnClose
      placement={'bottom'}
      closable={false}
      maskClosable={true}
      onClose={onCancel}
      open={visible}
      key={'num-keyboard'}
      height={'480px'}
      getContainer={document.getElementById('_module') as HTMLElement}
      styles={{
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0.7)'
        },
        body: {
          padding: 0
        }
      }}
      rootStyle={{
        position: 'absolute'
      }}
    >
      <div className={styles['money-title-box']}>
        <h3 className={styles['item-title']}>{title}</h3>
        <ModalCloseItem onCancel={onCancel} num={window.config.READ_CARD_OVER_TIME} />
        {dataKey.length > 0 ? (
          <h3 className={styles['item-title']}>
            {type === 'password' ? dataKey.replace(/[0-9]/g, '*') : dataKey}
          </h3>
        ) : (
          <h3 className={`${styles['item-title']} ${styles['gray']}`}>{'请输入' + title}</h3>
        )}
      </div>
      <Row gutter={[0, 1]} className={styles.keyboard}>
        <Col span={19}>
          <Row gutter={[1, 1]} onClick={getKey}>
            {type === 'money'
              ? moneyKeys.map((val, index) => {
                  return (
                    <Col key={index} span={8}>
                      <div data-id={index} className={styles.keys}>
                        {val}
                      </div>
                    </Col>
                  )
                })
              : numberKeys.map((val, index) => {
                  return (
                    <Col key={index} span={val === '0' ? 24 : 8}>
                      <div data-id={index} className={styles.keys}>
                        {val}
                      </div>
                    </Col>
                  )
                })}
          </Row>
        </Col>
        <Col span={5}>
          <Flex className={styles.row} align='start' vertical>
            <div className={styles.clean} onClick={() => getBackspace()}>
              <img src={require('../../assets/images/btns/delete.png')} />
            </div>
            <div className={styles.degenerate} onClick={() => getConfirm()}>
              <span>确定</span>
            </div>
          </Flex>
        </Col>
      </Row>
    </Drawer>
  )
}

export default NumKeyboardDrawer
