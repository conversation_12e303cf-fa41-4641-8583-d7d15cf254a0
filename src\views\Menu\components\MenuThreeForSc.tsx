/*
 * @Description: 手持竖版三按钮
 */

import BigButtonItem from './BigButtonItem'
import styles from '../index.module.scss'

const styleName = ['bgblue', 'bggreen', 'bgred']

const MenuThree = ({ data, findUrl }) => {
  return (
    <div className={styles['menu2']}>
      {data.map((item, index) => {
        return (
          <BigButtonItem key={index} item={item} class_name={styleName[index]} findUrl={findUrl} />
        )
      })}
    </div>
  )
}

export default MenuThree
