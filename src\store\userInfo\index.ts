import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit'
import { getUserInfo } from '@/services/user'

interface UserState {
  currentUser: any // 门诊信息
  readCardData: any // 读卡信息
  hospitalInfo: any // 住院信息
  medicalInfo: any  // 医保信息
  loginDocData: any // 登录的医生信息
}
const initialState: UserState = {
  currentUser: {},
  readCardData: {},
  hospitalInfo: {},
  medicalInfo: {},
  loginDocData: null,
}

// 定义异步请求的thunk
export const fetchCurrentUser = createAsyncThunk('userInfo/fetchCurrentUser', async params => {
  const response = await getUserInfo(params, true)
  return response.data
})

export const userInfo = createSlice({
  name: 'userInfo',
  // state数据的初始值
  initialState: initialState,
  reducers: {
    setQueryUser(state, action: PayloadAction<object>) {
      // 第一个参数 state 为当前state中的数据
      // 第二个参数 action 为{payload,type:'user/setDoctor'}
      state.currentUser = action.payload
    },
    setReadCard(state, action: PayloadAction<object>) {
      state.readCardData = action.payload
    },
    setHospitalInfo(state, action: PayloadAction<object>) {
      state.hospitalInfo = action.payload
    },
    setMedicalInfo(state, action: PayloadAction<object>) {
      state.medicalInfo = action.payload
    },
    setLoginDocData(state, action: PayloadAction<object>) {
      state.loginDocData = action.payload
    },
    resetData: (state) => {
      state.hospitalInfo = {}
      state.readCardData = {}
      state.currentUser = {}
      state.medicalInfo = {}
    }
  },
  extraReducers: builder => {
    builder
      .addCase(fetchCurrentUser.fulfilled, (state, action) => {
        state.currentUser = action.payload
      })
      .addCase(fetchCurrentUser.rejected, (state, action) => {
        state.currentUser = {}
      })
  }
})
