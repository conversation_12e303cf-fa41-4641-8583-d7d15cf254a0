import { Modal } from 'antd'
import ModalCloseItem from '@/components/ModalCloseItem'
import { useTheme } from 'antd-style'
import { PositionType } from 'antd/es/image/style'
import styles from './index.module.scss'
import { useEffect } from 'react'
import { getSpeak } from '@/services/hardware'

type ModelProps = {
  modalVisible: boolean
  onCancel: () => void
  modalCloseNum?: number
  modalType: string
}

const modalTypeText = {
  face: '请面向屏幕开始刷脸',
  idCard: '请将身份证放置感应区',
  socialCard: '请将社保卡插入设备右侧入口',
  elMedCard: '请按住设备右侧按键扫描医保电子凭证二维码'
}

const PayModal: React.FC<ModelProps> = ({ modalVisible, onCancel, modalType }) => {
  const token = useTheme()
  const modalStyles = {
    header: {},
    body: {
      paddingTop: '20px',
      minHeight: '250px'
    },
    mask: {
      position: 'absolute' as PositionType
    },
    wrapper: {
      position: 'absolute' as PositionType
    },
    footer: {
      display: 'flex',
      justifyContent: 'space-around',
      paddingBottom: '20px'
    },
    content: {
      borderRadius: 16,
      minHeight: '280px',
      padding: 8
    }
  }

  useEffect(() => {
    window.config.isSpeak && getSpeak({ content: modalTypeText[modalType] })
  }, [])

  return (
    <Modal
      destroyOnClose
      open={modalVisible}
      title={null}
      footer={null}
      maskClosable={false}
      closable={false}
      centered
      onCancel={() => onCancel()}
      width={`${660 / window.config.minPixelValue}px`}
      getContainer={document.getElementById('_module') as HTMLElement}
      styles={modalStyles}
    >
      <ModalCloseItem num={window.config.PAY_OVER_TIME} onCancel={onCancel} />
      <h2 className={styles['type-text']}>{modalTypeText[modalType]}</h2>
      <div className={styles['type-image-wrapper']}>
        {
          <img
            src={require(`../../../../assets/images/gifs/type-${modalType}.gif`)}
            alt='读卡动图'
          />
        }
      </div>
    </Modal>
  )
}

export default PayModal
