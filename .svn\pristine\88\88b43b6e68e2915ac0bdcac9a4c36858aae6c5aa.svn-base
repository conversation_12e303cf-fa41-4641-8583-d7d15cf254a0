// request.js 封装axios请求
import axios, { AxiosRequestConfig, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import ErrorModal from '../components/ETipModal'
import loading from './loading'

// 状态码错误信息
const codeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。'
}

const { STATE_API_NAME, PRINT_API_NAME, API_NAME, REQUEST_TIME_OUT } = window.config

// 定义请求配置的接口
interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  isState?: boolean
  isPrint?: boolean
  isHideLoading?: boolean
  isHideError?: boolean
  isReturnErrorRes?: boolean
  loadingText?: string
}

// 定义请求配置的接口
interface CustomInternalAxiosRequestConfig extends InternalAxiosRequestConfig {
  isState?: boolean
  isPrint?: boolean
  isHideLoading?: boolean
  isHideError?: boolean
  isReturnErrorRes?: boolean
  loadingText?: string
}

// 定义请求配置的接口
interface CustomAxiosResponse extends AxiosResponse {
  config: CustomInternalAxiosRequestConfig
}

const instance = axios.create()
// 设置全局参数，如响应超市时间，请求前缀等。
instance.defaults.timeout = REQUEST_TIME_OUT
// instance.defaults.withCredentials = true;
// 添加一个请求拦截器，用于设置请求过渡状态
instance.interceptors.request.use(
  (config: CustomInternalAxiosRequestConfig) => {
    // 请求开始，蓝色过渡滚动条开始出现
    // NProgress.start();
    let baseURL = API_NAME
    if (config.isState) {
      baseURL = STATE_API_NAME
    } else if (config.isPrint) {
      baseURL = PRINT_API_NAME
    } else {
      baseURL = API_NAME
    }
    if (!config.isHideLoading) {
      loading.start(config.loadingText)
    }
    return {
      ...config,
      baseURL
    }
  },
  error => {
    loading.end()
    return Promise.reject(error)
  }
)

// 添加一个返回拦截器 打印日志方法
instance.interceptors.response.use(
  (response: CustomAxiosResponse) => {
    if (!response.config?.isHideLoading) {
      loading.end()
    }
    return response
  },
  error => {
    loading.end()
    return Promise.reject(error)
  }
)

export default function request(opt: CustomAxiosRequestConfig) {
  // 调用 axios api，统一拦截
  return instance(opt)
    .then(response => {
      console.log(response)
      // >>>>>>>>>>>>>> 请求成功 <<<<<<<<<<<<<<
      // console.log(`【${opt.method} ${opt.url}】请求成功，响应数据：%o`, response);
      // 业务错误提示
      if (response.data && response.data.code != '0') {
        if (!opt.isHideError) {
          ErrorModal(response.data.msg, 'error')
        }
        if (opt.isReturnErrorRes) {
          return { ...response.data }
        }
        return false
      }
      return { ...response.data }
    })
    .catch(error => {
      // >>>>>>>>>>>>>> 请求失败 <<<<<<<<<<<<<<
      // console.log(error)
      // 请求配置发生的错误
      if (!error.response) {
        // console.log('Error', error.message);
        if (!opt.isHideError) {
          ErrorModal(error.message, 'error')
        }
        return false
      }
      // 响应时状态码处理
      const status = error.response.status
      const errortext = codeMessage[status] || error.response.statusText
      if (!opt.isHideError) {
        ErrorModal(errortext, 'error')
      }
      return false
    })
}
