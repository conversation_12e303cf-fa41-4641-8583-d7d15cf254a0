import { Table, ConfigProvider } from 'antd'
import type { ColumnsType, TableProps } from 'antd/es/table'
import { createStyles, useTheme } from 'antd-style'
import BlueBgTitle from '../BlueBgTitle'
import PageButton from '../PageButton'
import styles from './index.module.scss'

type DataType = {
  key: React.Key
  name: string
  dept: string
  money: string
  amount: number
  unit: string
}

interface ETableProps {
  tableTitle: string // 表格标题,
  tableColumns: ColumnsType
  dataList: DataType[]
}

const handlePageChange = () => {}

const ETable: React.FC<ETableProps> = ({ tableTitle, tableColumns, dataList }) => {
  return (
    <div className={styles['e-table-wrapper']}>
      <div className={styles['table-wrapper']}>
        <div className={styles['table-content']}>
          <BlueBgTitle title={tableTitle}></BlueBgTitle>
          <ConfigProvider
            theme={{
              token: {
                colorText: '#333333',
                fontSize: 30,
                controlInteractiveSize: 38,
                lineWidthBold: 4,
                colorBgBase: '#F0F6FF'
              },
              components: {
                Table: {
                  cellFontSize: 28,
                  headerBg: '#F0F6FF',
                  borderColor: '#F0F6FF',
                  headerSplitColor: '#F0F6FF'
                },
                Pagination: { itemSize: 60, itemSizeSM: 30 }
              }
            }}
          >
            <Table
              rowSelection={{
                type: 'checkbox',
                onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
                  console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows)
                }
              }}
              columns={tableColumns}
              dataSource={dataList}
              pagination={false}
              className={styles['main-table']}
            />
          </ConfigProvider>
        </div>
      </div>
      <div className={styles['table-page']}>
        <PageButton
          totalPage={dataList.length}
          pageSize={7}
          current={0}
          onChange={handlePageChange}
        ></PageButton>
      </div>
    </div>
  )
}

export default ETable
