@import '@/assets/scss/base.scss';

.container {
    .wrapper {
        height: calc(100vh - 130px);
        padding: 0 12px;
        overflow-y: scroll;
        .confirm {
            width: 100%;
            height: 140px;
            position: relative;
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 10px;
            border: 1px solid #99c4ff;
            padding: 30px 20px 0;
            margin-bottom: 8px;
            .line-wrapper {
                width: 100%;
                position: absolute;
                top: 105px;
                left: 50%;
                transform: translateX(-50%);
                align-items: center;
                z-index: 2;
                .line {
                    width: 88%;
                    height: 1px;
                    flex: 1;
                    margin: 0 auto;
                    border-bottom: 1px solid #cfcfcf;
                }
                .circle-box {
                    width: 15px;
                    height: 15px;
                    background-color: #f6f6f6;
                    border: 1px solid #99c4ff;
                    border-radius: 50%;
                    &.left {
                        position: absolute;
                        bottom: -8px;
                        left: -9px;
                        clip: rect(0 15px 17px 7px); //图形裁剪
                    }
                    &.right {
                        position: absolute;
                        bottom: -8px;
                        right: -9px;
                        clip: rect(0 8px 15px 0); //图形裁剪
                    }
                }
            }
            .title-wrapper {
                display: flex;
                justify-content: flex-start;
                font-size: 19px;
                .title {
                    width: 100px;
                    height: 20px;
                    padding-right: 15px;
                    text-align: justify;
                    color: #979797;
                    position: relative;
                    & i {
                        display: inline-block;
                        width: 100%;
                    }
                    &::after {
                        position: absolute;
                        top: 1px;
                        left: 90px;
                        content: ':';
                        color: #666;
                        display: inline-block;
                    }
                }
            }
            // .total {
            //     display: flex;
            //     justify-content: center;
            //     margin-top: 5px;
            //     align-items: center;
            //     font-size: 17px;
            //     .red-text {
            //         display: inline-block;
            //         font-size: 16px;
            //         color: #ff8d1a;
            //         margin-left: 7px;
            //     }
            //     border: 5px solid red;
            // }
            // .black-txt {
            //     font-size: 13px;
            //     color: #333333;
            //     border: 1px solid red;
            // }
            // .blue-text {
            //     display: inline-block;
            //     margin-left: 10px;
            //     color: #1677ff;
            // }
            // .red-text {
            //     display: inline-block;
            //     font-size: 13px;
            //     color: #ff8d1a;
            //     margin-left: 17px;
            // }
            // }
            // .info-button {
            //     display: flex;
            //     align-items: center;
            //     justify-content: center;
            //     width: 280px;
            //     height: 100px;
            //     margin: 48px auto 0;
            //     text-align: center;
            //     color: #ffffff;
            //     border-radius: 50px;
            //     background-color: #1677ff;
            //     font-size: 42px;
            // }
        }
        .take-list {
            // height: 58vh;
            flex: 1;
            // border: 1px solid red;
            // overflow-y: scroll;
            .item-take-num {
                padding: 15px 12px;
                // box-shadow: 0 2px 6px 1px rgba(187, 187, 187, 0.4);
                background-color: #fff;
                border-radius: 6px;
                .img-box {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                    }
                }
                .doctor-info {
                    .name {
                        display: flex;
                        align-items: center;
                        color: #333;
                        font-size: 19px;
                        font-weight: bold;

                    }
                    p {
                        width: 100%;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        margin: 0;
                        padding: 0;
                        font-size: 18px;
                        color: #666666;
                        .black-color {
                            color: #333333;
                            .type {
                                display: inline-block;
                                margin-left: 5px;
                                padding: 1px 0;
                                color: #faad14;
                                border-radius: 6px;
                                text-align: center;
                                font-size: 18px;
                            }
                        }
                    }
                }
                .btn-wrapper {
                    .item-btn {
                        text-align: center;
                        font-size: 18px;
                        border-radius: 15px;
                        &.take-btn,
                        &.take-grey {
                            color: #ffffff;
                            padding: 4px 0;
                        }
                        &.take-btn {
                            background-color: #1677ff;
                            &.gray {
                                background-color: #666666;
                                color: #ffffff;
                            }
                            &:hover {
                                background-color: #0a60da;
                            }
                        }
                        &.take-grey {
                            background-color: #d9d9d9;
                        }
                        &.cancel-btn {
                            border: 1px solid #1677ff;
                            color: #1677ff;
                            &:hover {
                                border: 1px solid #0a60da;
                                color: #0a60da;
                            }
                        }
                    }
                }
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
        // .page-button {
        //   width: 30px;
        //   height: 460px;
        //   display: flex;
        //   justify-content: center;
        //   align-items: center;
        //   padding-left: 5px;
        // }
    }
}