import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
// import legacy from '@vitejs/plugin-legacy'
import postcssPxToViewport from 'postcss-px-to-viewport'
// import requireTransform from 'vite-plugin-require-transform'
// import commonjs from '@rollup/plugin-commonjs'
import path from 'path'

function requirePlugin() {
  return {
    // 插件名称
    name: 'vite-plugin-react-requireToUrlPlugin',

    // 默认值post：在 Vite 核心插件之后调用该插件，pre：在 Vite 核心插件之前调用该插件
    // enforce: "post",

    // 代码转译，这个函数的功能类似于 "webpack" 的 "loader"
    transform(code, id, opt) {
      const reactRE = /\.tsx$/
      const require = /require/g

      // 过滤掉非目标文件
      if (!reactRE.test(id) || !require.test(code)) return code

      // 匹配 require() 内的内容
      const requireRegex = /require\((.*?)\)/g

      // 将 require() 内的内容替换为 new URL 的写法
      const finalCode = code.replace(requireRegex, 'new URL($1,import.meta.url).href')

      // 将转换后的代码返回
      return finalCode
    }
  }
}

export default defineConfig(({ mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  const env = loadEnv(mode, process.cwd(), '')
  return {
    server: {
      host: 'localhost',
      port: 8080
      // proxy: {
      //   '/api': 'http://api-driver.marsview.cc/'
      // }
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },
    base: env.VITE_BASE_PATH,
    plugins: [
      react(),
      requirePlugin()
      // legacy({
      //   targets: ['ie>=11'],
      //   additionalLegacyPolyfills: ['regenerator-runtime/runtime']
      // })
    ],
    css: {
      postcss: {
        plugins: [
          postcssPxToViewport({
            viewportWidth: 360, // (Number) 设计稿宽度
            viewportHeight: 720, // (Number) 设计稿高度
            unitPrecision: 3, // (Number) 单位转换后保留的精度
            viewportUnit: 'vw', // (String) 希望使用的视口单位
            fontViewportUnit: 'vw', // 字体使用的视口单位
            selectorBlackList: [], // (Array) 要忽略的选择器
            minPixelValue: 1, // (Number) 最小的转换数值
            mediaQuery: false, // (Boolean) 是否在媒体查询中也转换px
            exclude: /(\/|\\)(node_modules)(\/|\\)/, //忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
            unitToConvert: 'px', // 要转换的单位
            propList: ['*'], // 指定转换那些属性，*表示全部
            replace: true // 是否直接更换原来的规则
          })
        ]
      },
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/assets/scss/base.scss";',
          javascriptEnabled: true
        }
      }
    }
  }
})
