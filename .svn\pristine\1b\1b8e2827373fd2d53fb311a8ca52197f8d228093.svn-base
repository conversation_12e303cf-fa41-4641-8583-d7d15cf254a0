// vite.config.ts
import { defineConfig, loadEnv } from "file:///D:/code/jjsc/node_modules/vite/dist/node/index.js";
import react from "file:///D:/code/jjsc/node_modules/@vitejs/plugin-react/dist/index.mjs";
import postcssPxToViewport from "file:///D:/code/jjsc/node_modules/postcss-px-to-viewport/index.js";
import path from "path";
var __vite_injected_original_dirname = "D:\\code\\jjsc";
function requirePlugin() {
  return {
    // 插件名称
    name: "vite-plugin-react-requireToUrlPlugin",
    // 默认值post：在 Vite 核心插件之后调用该插件，pre：在 Vite 核心插件之前调用该插件
    // enforce: "post",
    // 代码转译，这个函数的功能类似于 "webpack" 的 "loader"
    transform(code, id, opt) {
      const reactRE = /\.tsx$/;
      const require2 = /require/g;
      if (!reactRE.test(id) || !require2.test(code))
        return code;
      const requireRegex = /require\((.*?)\)/g;
      const finalCode = code.replace(requireRegex, "new URL($1,import.meta.url).href");
      return finalCode;
    }
  };
}
var vite_config_default = defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  return {
    server: {
      host: "localhost",
      port: 8080
      // proxy: {
      //   '/api': 'http://api-driver.marsview.cc/'
      // }
    },
    resolve: {
      alias: {
        "@": path.resolve(__vite_injected_original_dirname, "./src")
      }
    },
    base: env.VITE_BASE_PATH,
    plugins: [
      react(),
      requirePlugin()
      // legacy({
      //   targets: ['ie>=11'],
      //   additionalLegacyPolyfills: ['regenerator-runtime/runtime']
      // })
    ],
    css: {
      postcss: {
        plugins: [
          postcssPxToViewport({
            viewportWidth: 360,
            // (Number) 设计稿宽度
            viewportHeight: 720,
            // (Number) 设计稿高度
            unitPrecision: 3,
            // (Number) 单位转换后保留的精度
            viewportUnit: "vw",
            // (String) 希望使用的视口单位
            fontViewportUnit: "vw",
            // 字体使用的视口单位
            selectorBlackList: [],
            // (Array) 要忽略的选择器
            minPixelValue: 1,
            // (Number) 最小的转换数值
            mediaQuery: false,
            // (Boolean) 是否在媒体查询中也转换px
            exclude: /(\/|\\)(node_modules)(\/|\\)/,
            //忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
            unitToConvert: "px",
            // 要转换的单位
            propList: ["*"],
            // 指定转换那些属性，*表示全部
            replace: true
            // 是否直接更换原来的规则
          })
        ]
      },
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/assets/scss/base.scss";',
          javascriptEnabled: true
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
