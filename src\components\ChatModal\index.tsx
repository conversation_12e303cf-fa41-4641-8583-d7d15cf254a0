import React, { useState, useEffect, useRef } from 'react';
import { Modal, Input, Button, Image, Toast, ConfigProvider } from 'antd-mobile';
import { PictureOutline, SendOutline, CloseOutline } from 'antd-mobile-icons';
import { useTheme } from 'antd-style';
import { ChatMessage, sendTextMessage, sendImageMessage, getChatHistory } from '@/services/chat';
import styles from './index.module.scss';

interface ChatModalProps {
  visible: boolean;
  onClose: () => void;
  doctorId?: string;
  doctorName?: string;
  sessionId?: string;
  modalCloseNum?: number;
}

const ChatModal: React.FC<ChatModalProps> = ({ 
  visible, 
  onClose, 
  doctorId = '1', 
  doctorName = '医生',
  sessionId = 'session_001',
  modalCloseNum = 5
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(modalCloseNum);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const countdownRef = useRef<NodeJS.Timeout>();
  const token = useTheme();

  // 倒计时逻辑
  useEffect(() => {
    if (visible && modalCloseNum > 0) {
      setCountdown(modalCloseNum);
      countdownRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            onClose();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (countdownRef.current) {
        clearInterval(countdownRef.current);
      }
    };
  }, [visible, modalCloseNum, onClose]);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 加载聊天历史
  const loadChatHistory = async () => {
    try {
      const response = await getChatHistory({ sessionId });
      if (response && response.data) {
        setMessages(response.data);
      } else {
        // 如果没有历史记录，添加欢迎消息
        const welcomeMessage: ChatMessage = {
          id: 'welcome',
          content: `您好！我是${doctorName}，很高兴为您服务。请问有什么可以帮助您的吗？`,
          type: 'text',
          sender: 'doctor',
          timestamp: Date.now(),
          status: 'sent'
        };
        setMessages([welcomeMessage]);
      }
    } catch (error) {
      console.error('加载聊天历史失败:', error);
      // 添加默认欢迎消息
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        content: `您好！我是${doctorName}，很高兴为您服务。请问有什么可以帮助您的吗？`,
        type: 'text',
        sender: 'doctor',
        timestamp: Date.now(),
        status: 'sent'
      };
      setMessages([welcomeMessage]);
    }
  };

  // 发送文本消息
  const handleSendText = async () => {
    if (!inputValue.trim()) return;

    // 重置倒计时
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      setCountdown(modalCloseNum);
      countdownRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            onClose();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue,
      type: 'text',
      sender: 'user',
      timestamp: Date.now(),
      status: 'sending'
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue('');
    setLoading(true);

    try {
      const response = await sendTextMessage({
        content: inputValue,
        receiverId: doctorId,
        sessionId
      });

      if (response) {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id 
              ? { ...msg, status: 'sent' }
              : msg
          )
        );
        
        // 模拟医生回复
        setTimeout(() => {
          const doctorReply: ChatMessage = {
            id: (Date.now() + 1).toString(),
            content: '我已经收到您的消息，请稍等，我会尽快回复您。',
            type: 'text',
            sender: 'doctor',
            timestamp: Date.now(),
            status: 'sent'
          };
          setMessages(prev => [...prev, doctorReply]);
        }, 1000);
      } else {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id 
              ? { ...msg, status: 'failed' }
              : msg
          )
        );
        Toast.show('消息发送失败，请重试');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: 'failed' }
            : msg
        )
      );
      Toast.show('消息发送失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 选择图片
  const handleImageSelect = () => {
    fileInputRef.current?.click();
  };

  // 发送图片
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      Toast.show('请选择图片文件');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      Toast.show('图片大小不能超过5MB');
      return;
    }

    // 重置倒计时
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      setCountdown(modalCloseNum);
      countdownRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            onClose();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    const imageUrl = URL.createObjectURL(file);
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      content: '',
      type: 'image',
      sender: 'user',
      timestamp: Date.now(),
      imageUrl,
      status: 'sending'
    };

    setMessages(prev => [...prev, newMessage]);
    setLoading(true);

    try {
      const response = await sendImageMessage({
        imageFile: file,
        receiverId: doctorId,
        sessionId
      });

      if (response) {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id 
              ? { ...msg, status: 'sent', imageUrl: response.data?.imageUrl || imageUrl }
              : msg
          )
        );
      } else {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id 
              ? { ...msg, status: 'failed' }
              : msg
          )
        );
        Toast.show('图片发送失败，请重试');
      }
    } catch (error) {
      console.error('发送图片失败:', error);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: 'failed' }
            : msg
        )
      );
      Toast.show('图片发送失败，请重试');
    } finally {
      setLoading(false);
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  useEffect(() => {
    if (visible) {
      loadChatHistory();
    }
  }, [visible, sessionId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const modalStyles = {
    header: {},
    body: {
      padding: 0,
      height: '70vh',
      minHeight: '500px'
    },
    mask: {
      position: 'absolute' as const
    },
    wrapper: {
      position: 'absolute' as const
    },
    content: {
      borderRadius: 16,
      overflow: 'hidden'
    }
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: token.colorPrimary,
        },
      }}
    >
      <Modal
        visible={visible}
        onClose={onClose}
        closeOnMaskClick={false}
        bodyStyle={modalStyles.body}
        style={{
          '--z-index': '1000',
        }}
      >
        <div className={styles.chatModal}>
          {/* 头部 */}
          <div className={styles.header}>
            <div className={styles.doctorInfo}>
              <div className={styles.avatar}>
                <img src="/api/placeholder/40/40" alt={doctorName} />
              </div>
              <div className={styles.info}>
                <div className={styles.name}>{doctorName}</div>
                <div className={styles.status}>在线</div>
              </div>
            </div>
            <div className={styles.headerActions}>
              <div className={styles.countdown}>
                {countdown}s后自动关闭
              </div>
              <Button
                fill="none"
                size="small"
                onClick={onClose}
                className={styles.closeButton}
              >
                <CloseOutline />
              </Button>
            </div>
          </div>

          {/* 消息区域 */}
          <div className={styles.messagesContainer}>
            {messages.map((message) => (
              <div 
                key={message.id} 
                className={`${styles.messageItem} ${
                  message.sender === 'user' ? styles.userMessage : styles.doctorMessage
                }`}
              >
                <div className={styles.messageContent}>
                  {message.type === 'text' ? (
                    <div className={styles.textMessage}>
                      {message.content}
                    </div>
                  ) : (
                    <div className={styles.imageMessage}>
                      <Image
                        src={message.imageUrl}
                        alt="聊天图片"
                        fit="cover"
                        style={{ maxWidth: '150px', maxHeight: '150px' }}
                      />
                    </div>
                  )}
                  <div className={styles.messageTime}>
                    {formatTime(message.timestamp)}
                    {message.sender === 'user' && (
                      <span className={styles.messageStatus}>
                        {message.status === 'sending' && '发送中...'}
                        {message.status === 'sent' && '已送达'}
                        {message.status === 'failed' && '发送失败'}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          {/* 输入区域 */}
          <div className={styles.inputContainer}>
            <div className={styles.inputWrapper}>
              <Button
                fill="none"
                size="small"
                onClick={handleImageSelect}
                className={styles.imageButton}
              >
                <PictureOutline />
              </Button>
              
              <Input
                placeholder="请输入消息..."
                value={inputValue}
                onChange={setInputValue}
                onEnterPress={handleSendText}
                className={styles.textInput}
              />
              
              <Button
                color="primary"
                size="small"
                onClick={handleSendText}
                loading={loading}
                disabled={!inputValue.trim() || loading}
                className={styles.sendButton}
              >
                <SendOutline />
              </Button>
            </div>
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              style={{ display: 'none' }}
            />
          </div>
        </div>
      </Modal>
    </ConfigProvider>
  );
};

export default ChatModal;
