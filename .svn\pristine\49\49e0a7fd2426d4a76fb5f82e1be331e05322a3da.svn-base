/*
 * @Description: 门诊菜单
 */

import { useState, useEffect } from 'react'
import { Resul<PERSON>, <PERSON><PERSON>, ConfigP<PERSON>ider, Flex } from "antd";
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import { useNavigate } from 'react-router-dom'
import MenuOne from './components/MenuOne'
import MenuTwo from './components/MenuTwo'
import MenuThree from './components/MenuThree'
import MenuFour from './components/MenuFour'
import MenuFive from './components/MenuFive'
import MenuSix from './components/MenuSix'
import MenuNew from './components/MenuNew'
import Config from '@/common'
import { useDispatch } from 'react-redux'
import styles from './index.module.scss'
import storage from '@/utils/storage';

const Menu = () => {
  const menus = Config.menuMzdata || []
  const [data, setData] = useState([...menus])
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { loginDocData } = useSelector((state: RootState) => state.userInfo)

  const findUrl = (value, key = 'name') => {
    if (!value) return ''
    if(!loginDocData){
      return '/login'
    }
    return data.find(item => item[key] === value).menu_link || ''
  }

  useEffect(() => {
    dispatch({
      type: 'userInfo/resetData'
    })
    dispatch({
      type: 'list/resetData'
    })
    const loginDoc = storage.get('loginDocData')
    if(loginDoc){
      dispatch({
        type: "userInfo/setLoginDocData",
        payload: loginDoc,
      });
    }
  }, [])

  return (
    <>
      <div className={styles['login-wrapper']}>
        {
          <Flex justify="flex-end" align="center" className={styles.label}>
            {/* {(loginDocData) ? (
              <img src={require("@/assets/images/btns/ysdl-o.png")}></img>
            ) : (
              <img src={require("@/assets/images/btns/ysdl.png")}></img>
            )} */}
            <div className={[styles['login-status'], styles[`${loginDocData ? 'loginIn': 'loginOut'}`]].join(' ')}></div>
            {(loginDocData) ? (
              <div className={styles.info} onClick={() => navigate("/login")}>
                登录医生：<span>{loginDocData?.YONGHUXM }</span>
              </div>
            ) : (
              <Button
                ghost
                size="middle"
                type="primary"
                shape="round"
                style={{ marginLeft: "5px", fontSize: "19px" }}
                onClick={() => navigate("/login")}
              >
                医生登录
              </Button>
            )}
          </Flex>
        }
      </div>
      <div className={styles['menu-wrapper']}>
        {data.length === 1 ? (
          <MenuOne data={data} findUrl={findUrl} />
        ) : data.length === 2 ? (
          <MenuTwo data={data} findUrl={findUrl} />
        ) : data.length === 3 ? (
          <MenuThree data={data} findUrl={findUrl} />
        ) : data.length === 4 ? (
          <MenuFour data={data} findUrl={findUrl} />
        ) : data.length === 5 ? (
          <MenuFive data={data} findUrl={findUrl} />
        ) : data.length === 6 ? (
          <MenuSix data={data.slice(0, 6)} findUrl={findUrl} />
        ) : data.length > 6 ? (
          <MenuNew data={data} findUrl={findUrl} />
        ) : (
          <></>
        )}
      </div>
    </>
  )
}

export default Menu
