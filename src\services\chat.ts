import request from '../utils/request';

// 聊天消息类型定义
export interface ChatMessage {
  id: string;
  content: string;
  type: 'text' | 'image';
  sender: 'user' | 'doctor';
  timestamp: number;
  imageUrl?: string;
  status?: 'sending' | 'sent' | 'failed';
}

// 发送文本消息
export async function sendTextMessage(params: {
  content: string;
  receiverId: string;
  sessionId: string;
}) {
  return request({
    url: '/api/chat/sendText',
    method: 'POST',
    data: params,
  });
}

// 发送图片消息
export async function sendImageMessage(params: {
  imageFile: File;
  receiverId: string;
  sessionId: string;
}) {
  const formData = new FormData();
  formData.append('image', params.imageFile);
  formData.append('receiverId', params.receiverId);
  formData.append('sessionId', params.sessionId);

  return request({
    url: '/api/chat/sendImage',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 获取聊天历史记录
export async function getChatHistory(params: {
  sessionId: string;
  page?: number;
  pageSize?: number;
}) {
  return request({
    url: '/api/chat/history',
    method: 'GET',
    params,
  });
}

// 创建聊天会话
export async function createChatSession(params: {
  doctorId: string;
  patientId: string;
}) {
  return request({
    url: '/api/chat/createSession',
    method: 'POST',
    data: params,
  });
}

// 获取在线医生列表
export async function getOnlineDoctors() {
  return request({
    url: '/api/chat/onlineDoctors',
    method: 'GET',
  });
}
