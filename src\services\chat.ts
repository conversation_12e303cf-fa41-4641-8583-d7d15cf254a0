import request from '../utils/request';

// 聊天消息类型定义 - 基于接口文档
export interface ChatMessage {
  messageId: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  messageType: number; // 1-文字，2-图片
  content: string;
  imageUrl?: string;
  fileSize?: number;
  sendStatus: number; // 1-发送中，2-发送成功，3-发送失败
  readStatus: number; // 0-未读，1-已读
  createTime: string;
  senderName?: string;
  senderAvatar?: string;
}

// 会话信息类型
export interface Conversation {
  id: number;
  conversationId: string;
  user1Id: string;
  user2Id: string;
  user1UnreadCount: number;
  user2UnreadCount: number;
  status: number;
  createTime: string;
  updateTime: string;
  lastMessageContent?: string;
  lastMessageType?: number;
  lastMessageTime?: string;
}

// 创建或获取会话
export async function createOrGetConversation(params: {
  user1Id: string;
  user2Id: string;
}) {
  return request({
    url: '/chat/conversation/create',
    method: 'POST',
    data: params,
  });
}

// 发送文本消息
export async function sendTextMessage(params: {
  senderId: string;
  receiverId: string;
  content: string;
}) {
  return request({
    url: '/chat/message/text',
    method: 'POST',
    data: params,
  });
}

// 发送图片消息
export async function sendImageMessage(params: {
  senderId: string;
  receiverId: string;
  imageFile: File;
}) {
  const formData = new FormData();
  formData.append('senderId', params.senderId);
  formData.append('receiverId', params.receiverId);
  formData.append('image', params.imageFile);

  return request({
    url: '/chat/message/image',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 获取聊天历史记录
export async function getChatHistory(params: {
  conversationId: string;
  page?: number;
  size?: number;
}) {
  return request({
    url: '/chat/message/history',
    method: 'GET',
    params: {
      conversationId: params.conversationId,
      page: params.page || 1,
      size: params.size || 20,
    },
  });
}

// 获取会话列表
export async function getConversationList(params: {
  userId: string;
  page?: number;
  size?: number;
}) {
  return request({
    url: '/chat/conversation/list',
    method: 'GET',
    params: {
      userId: params.userId,
      page: params.page || 1,
      size: params.size || 20,
    },
  });
}

// 标记消息已读
export async function markMessageAsRead(params: {
  conversationId: string;
  userId: string;
}) {
  return request({
    url: '/chat/message/read',
    method: 'POST',
    data: params,
  });
}

// 获取会话详情
export async function getConversationDetail(conversationId: string) {
  return request({
    url: '/chat/conversation/detail',
    method: 'GET',
    params: { conversationId },
  });
}
