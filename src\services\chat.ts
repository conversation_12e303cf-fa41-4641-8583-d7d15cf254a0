import request from '../utils/request';

// 聊天消息类型定义 - 基于接口文档
export interface ChatMessage {
  messageId: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  messageType: number; // 1-文字，2-图片
  content: string;
  imageUrl?: string;
  fileSize?: number;
  sendStatus: number; // 1-发送中，2-发送成功，3-发送失败
  readStatus: number; // 0-未读，1-已读
  createTime: string;
  senderName?: string;
  senderAvatar?: string;
}

// 会话信息类型
export interface Conversation {
  id: number;
  conversationId: string;
  user1Id: string;
  user2Id: string;
  user1UnreadCount: number;
  user2UnreadCount: number;
  status: number;
  createTime: string;
  updateTime: string;
  lastMessageContent?: string;
  lastMessageType?: number;
  lastMessageTime?: string;
}

// 创建或获取会话
export async function createOrGetConversation(params: {
  user1Id: string;
  user2Id: string;
}) {
  return request({
    url: '/chat/conversation/create',
    method: 'POST',
    data: {
      ...params,
    },
    isChat: true,
    isHideLoading: true,
  });
}

// 发送文本消息
export async function sendTextMessage(params: {
  senderId: string;
  receiverId: string;
  content: string;
}) {
  return request({
    url: '/chat/message/text',
    method: 'POST',
    data: {
      ...params,
    },
    isChat: true,
    isHideLoading: true,
  });
}

// 发送图片消息（base64格式）
export async function sendImageMessage(params: {
  senderId: string;
  receiverId: string;
  imageBase64: string;
  imageType: string;
}) {
  return request({
    url: '/chat/message/image-base64',
    method: 'POST',
    data: {
      senderId: params.senderId,
      receiverId: params.receiverId,
      imageBase64: params.imageBase64,
      imageType: params.imageType,
    },
    isChat: true,
    isHideLoading: true,
  });
}

// 将文件转换为base64格式
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        // 移除data:image/xxx;base64,前缀，只保留base64数据
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });
}

// 获取文件扩展名
export function getFileExtension(file: File): string {
  const fileName = file.name;
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) {
    // 如果没有扩展名，根据MIME类型推断
    const mimeType = file.type;
    if (mimeType.includes('jpeg')) return 'jpg';
    if (mimeType.includes('png')) return 'png';
    if (mimeType.includes('gif')) return 'gif';
    if (mimeType.includes('bmp')) return 'bmp';
    if (mimeType.includes('webp')) return 'webp';
    return 'jpg'; // 默认
  }
  return fileName.substring(lastDotIndex + 1).toLowerCase();
}

// 获取聊天历史记录
export async function getChatHistory(params: {
  conversationId: string;
  page?: number;
  size?: number;
}) {
  return request({
    url: '/chat/message/history',
    method: 'GET',
    params: {
      conversationId: params.conversationId,
      page: params.page || 1,
      size: params.size || 20,
    },
    isChat: true,
    isHideLoading: true,
  });
}

// 获取会话列表
export async function getConversationList(params: {
  userId: string;
  page?: number;
  size?: number;
}) {
  return request({
    url: '/chat/conversation/list',
    method: 'GET',
    params: {
      userId: params.userId,
      page: params.page || 1,
      size: params.size || 20,
    },
    isChat: true,
  });
}

// 标记消息已读
export async function markMessageAsRead(params: {
  conversationId: string;
  userId: string;
}) {
  return request({
    url: '/chat/message/read',
    method: 'POST',
    data: {
      ...params, 
    },
    isChat: true,
    isHideLoading: true,
  });
} 

// 获取会话详情
export async function getConversationDetail(conversationId: string) {
  return request({
    url: '/chat/conversation/detail',
    method: 'GET',
    params: { conversationId },
    isChat: true
  });
}
