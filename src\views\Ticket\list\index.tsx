import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Flex, Row, Col } from "antd";
import dayjs from "dayjs";
import ModuleHeader from "@/components/ModuleHeader";
import BlueBgTitle from "@/components/BlueBgTitle";
import gotoMenu from '@/utils/gotoMenu';
import ETipModal from '@/components/ETipModal';
import Empty from "@/components/Empty";
import { RootState } from "@/store/index";
import { getSpeak } from "@/services/hardware";
import { getCreditsStr, getMultiCreditsStr, increasedPrintPayCount } from "@/services/print";
import { IdCard, IdNoPrivate} from "@/utils/utils";
import styles from "./index.module.scss";

/**
 * 打印记录
 * @param fields
 */
const handleCreditsStr = async (fields: any, list) => {
  console.log("打印记录fields", fields);
  try {
    let response;
    if (window.config.isDebug) {
      response = {
        "msg": "请求成功！",
        "code": "0",
        "data": [
          {
            "count": 1,
            "createTime": "2025-03-03 16:00",
            "voucherID": "PH1423BW90129586933",
            "content": {
              "totalAmount": "13.08",
              "print": [
                {
                  "size": 30,
                  "isUnderLine": false,
                  "text": "诸暨市中心医院",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "院区名称:望云社区",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "开单缴费（医保）",
                  "align": 1,
                  "isBold": true
                },
                "--------------------------",
                "患者姓名:胡茂宏",
                "患者性别:男",
                "患者年龄:40岁",
                "--------------------------",
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "缴费明细",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 22,
                  "isUnderLine": false,
                  "text": "名称:材料费  金额:1.08元",
                  "align": 0,
                  "isBold": false
                },
                {
                  "size": 22,
                  "isUnderLine": false,
                  "text": "名称:化验费  金额:12.00元",
                  "align": 0,
                  "isBold": false
                },
                "--------------------------",
                "总 金 额:13.08",
                "统筹金额:9.79元",
                "个账金额:0.00元",
                "自费金额:3.29元",
                "缴费时间:2025-01-23 16:06",
                "--------------------------",
                "医生工号:458",
                "发票号码:M654472",
                // "打印时间:2025-01-23 16:06",
                "--------------------------",
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "本次小票可当结算凭证用，请妥善保管！可凭此凭条打印一次正规发票！",
                  "align": 0,
                  "isBold": false
                }
              ],
              "cardType": "出院结算",
              "psyTime": "2025-01-23 16:06",
              "title": "",
              "yymc": "白云社区卫生院"
            }
          },
          {
            "count": 0,
            "createTime": "2025-01-23 15:58",
            "voucherID": "PH1423BW90129453440",
            "content": {
              "totalAmount": "8.00",
              "print": [
                {
                  "size": 30,
                  "isUnderLine": false,
                  "text": "诸暨市中心医院",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "院区名称:望云社区",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "挂号缴费（医保）",
                  "align": 1,
                  "isBold": true
                },
                "--------------------------",
                "患者姓名:胡茂宏",
                "患者性别:男",
                "患者年龄:40岁",
                "--------------------------",
                "总 金 额:8.00元",
                "统筹金额:6.00元",
                "个账金额:0元",
                "自费金额:2.00元",
                "--------------------------",
                "挂号序号:47",
                "就诊科室:望云社区",
                "就诊日期:2025-01-23",
                "--------------------------",
                "医生工号:458",
                "发票号码:G1218418",
                // "打印时间:2025-01-23 16:04",
                "--------------------------",
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "本次小票可当结算凭证用，请妥善保管！可凭此凭条打印一次正规发票！",
                  "align": 0,
                  "isBold": false
                }
              ],
              "cardType": "出院结算",
              "psyTime": "2025-01-23 16:04",
              "yymc": "白云社区卫生院"
            }
          },
          {
            "count": 4,
            "createTime": "2025-01-23 10:44",
            "voucherID": "M654319PH1423BW90129",
            "content": {
              "totalAmount": "13.08",
              "print": [
                {
                  "size": 30,
                  "isUnderLine": false,
                  "text": "诸暨市中心医院",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "院区名称:望云社区",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "开单缴费（医保）",
                  "align": 1,
                  "isBold": true
                },
                "--------------------------",
                "患者姓名:胡茂宏",
                "患者性别:男",
                "患者年龄:40岁",
                "--------------------------",
                "总 金 额:13.08",
                "统筹金额:8.48元",
                "个账金额:0.00元",
                "自费金额:4.60元",
                "缴费时间:2025-01-23 10:50",
                "--------------------------",
                "医生工号:458",
                "发票号码:M654319",
                "打印时间:2025-01-23 10:50",
                "--------------------------",
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "本次小票可当结算凭证用，请妥善保管！可凭此凭条打印一次正规发票！",
                  "align": 0,
                  "isBold": false
                }
              ],
              "cardType": "出院结算",
              "psyTime": "2025-01-23 10:50",
              "yymc": "白云社区卫生院"
            }
          },
          {
            "count": 0,
            "createTime": "2025-01-23 10:41",
            "voucherID": "G1218085PH1423BW90129",
            "content": {
              "totalAmount": "8.00",
              "print": [
                {
                  "size": 30,
                  "isUnderLine": false,
                  "text": "诸暨市中心医院",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "院区名称:望云社区",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "挂号缴费（医保）",
                  "align": 1,
                  "isBold": true
                },
                "--------------------------",
                "患者姓名:胡茂宏",
                "患者性别:男",
                "患者年龄:40岁",
                "--------------------------",
                "总 金 额:8.00元",
                "统筹金额:4.39元",
                "个账金额:0元",
                "自费金额:3.61元",
                "--------------------------",
                "挂号序号:36",
                "就诊科室:望云社区",
                "就诊地址:undefined",
                "就诊日期:2025-01-23",
                "--------------------------",
                "医生工号:458",
                "发票号码:G1218085",
                "打印时间:2025-01-23 10:46",
                "--------------------------",
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "本次小票可当结算凭证用，请妥善保管！可凭此凭条打印一次正规发票！",
                  "align": 0,
                  "isBold": false
                }
              ],
              "cardType": "出院结算",
              "psyTime": "2025-01-23 10:46",
              "title": "白云社区卫生院",
              "yymc": "白云社区卫生院"
            }
          },
          {
            "count": 0,
            "createTime": "2025-01-23 10:41",
            "voucherID": "G1218085PH1423BW90129",
            "content": {
              "totalAmount": "8.00",
              "print": [
                {
                  "size": 30,
                  "isUnderLine": false,
                  "text": "诸暨市中心医院",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "院区名称:望云社区",
                  "align": 1,
                  "isBold": true
                },
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "挂号缴费（医保）",
                  "align": 1,
                  "isBold": true
                },
                "--------------------------",
                "患者姓名:胡茂宏",
                "患者性别:男",
                "患者年龄:40岁",
                "--------------------------",
                "总 金 额:8.00元",
                "统筹金额:4.39元",
                "个账金额:0元",
                "自费金额:3.61元",
                "--------------------------",
                "挂号序号:36",
                "就诊科室:望云社区",
                "就诊地址:undefined",
                "就诊日期:2025-01-23",
                "--------------------------",
                "医生工号:458",
                "发票号码:G1218085",
                "打印时间:2025-01-23 10:46",
                "--------------------------",
                {
                  "size": 26,
                  "isUnderLine": false,
                  "text": "本次小票可当结算凭证用，请妥善保管！可凭此凭条打印一次正规发票！",
                  "align": 0,
                  "isBold": false
                }
              ],
              "cardType": "出院结算",
              "psyTime": "2025-01-23 10:46",
              "title": "白云社区卫生院",
              "yymc": "白云社区卫生院"
            }
          }
        ]
      }
    } else {
      response = await getMultiCreditsStr({
        ...fields,
      }, list);
    }
      // 多详情合并
      let List = [];
      for (const key in response) {
        if (response[key]) {
          List = List.concat(response[key]);
        }
      }
      return List
  } catch (error) {
    return false;
  }
};

/**
 * 增加打印次数
 * @param fields
 */
const handleIncreasedPrintPayCount = async (fields: any) => {
  console.log("增加打印次数fields", fields);
  try {
    let response;
    if (window.config.isDebug) {
      response = {
        code: "0",
        msg: "请求成功！",
        data: "",
      };
    } else {
      response = await increasedPrintPayCount({
        ...fields,
      });
    }
    if (response.code === "0") {
      return response;
    }
    return false;
  } catch (error) {
    return false;
  }
};

const TakeList = () => {
  const navigate = useNavigate();
  // const dispatch = useDispatch();
  const { currentUser, logOnData, readCardData}: any = useSelector((state: RootState) => state.userInfo);
  const [registerRecord, setRegisterRecord] = useState<any>("");
  const [isEmpty, setIsEmpty] = useState<boolean>(false);
  const [scModalVisible, setScModalVisible] = useState(false);//小票补打gif图
  // console.log("currentUser", currentUser)

  // 打印记录
  const onCreditsStr = async (isLoading: boolean) => {
    console.log(currentUser)
    const fields = {
      beginTime: dayjs().subtract(window.config.RESERVE_DAY_COUNT, "day").format("YYYY-MM-DD"),
      endTime: dayjs().add(window.config.RESERVE_DAY_COUNT, "day").format("YYYY-MM-DD"),
      isLoading
    };
    const patientIDs = currentUser.map(item=> { return { patientID: item.patientID} })
    const res = await handleCreditsStr(fields, patientIDs);
    console.log("打印记录res", res);
    if (res && res?.length > 0) {
      setIsEmpty(false);
      setRegisterRecord(res);
    } else {
      setIsEmpty(true);
      setRegisterRecord("");
    }
  };

  const onPrint = async (event: any) => {
    const target = event.target;
    // const name = target.textContent;
    // const id = target.getAttribute('data-id');
    const id = target.dataset.id;
    const item: any = registerRecord[id];
    if (target.tagName !== "DIV" && !id) return;
    if (item?.count >= window.config.print_count) {
      return
    };
    // // 创建一个新数组，其中所有匹配的值都被替换
    // // let print = item?.content?.print.map((item: any) => {
    // //   return typeof item === "string" && item?.includes("打印时间") ? `打印时间:${dayjs().format("YYYY-MM-DD HH:mm")}` : item
    // // });
    let print = JSON.parse(item?.content?.print);
    console.log("print", print);
    
    if (window.config.isSpeak) getSpeak({ content: "补打成功，请取走凭条" });
    window.YW.print({
      type: "0",
      data: print,
      success: async (res: any) => {
        console.log("打印成功res", res);
        ETipModal('补打成功，请取走凭条', 'finish')
        const respond = await handleIncreasedPrintPayCount({
          voucherID: item?.voucherID,
          patientID: currentUser?.patientID,
        });
        console.log("增加打印次数respond", respond);
        onCreditsStr(true);
      },
      fail: (err: any) => {
        console.log("打印失败err", err);
        onCreditsStr(true);
        ETipModal(`小票打印失败，失败原因：${err}`, "error");
      },
    });
  };

  useEffect(() => {
    onCreditsStr(false);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className={styles.container}>
      <ModuleHeader title="凭条补打" />
      <div className={[styles['wrapper'], styles['scroll-style']].join(' ')}>
        <div className={styles["confirm"]}>
          <BlueBgTitle title={"患者信息"} />
          <Row>
            <Col span={24} className={styles["item-message-col"]}>
              <div className={styles["title-wrapper"]}>
                <div className={styles.title}>
                  患者姓名
                  <i></i>
                </div>
                <span className={styles["black-span"]}>
                  {readCardData?.certName}
                </span>
              </div>
            </Col>
            <Col span={24} className={styles["item-message-col"]}>
              <div className={styles["title-wrapper"]}>
                <div className={styles.title}>
                  姓别
                  <i></i>
                </div>
                <span className={styles["black-span"]}>
                  {["", "男", "女", "其他"][IdCard(readCardData?.certNo, 2)]}
                </span>
              </div>
            </Col>
            <Col span={24} className={styles["item-message-col"]}>
              <div className={styles["title-wrapper"]}>
                <div className={styles.title}>
                  证件号
                  <i></i>
                </div>
                <span className={styles["black-span"]}>
                  {IdNoPrivate(readCardData?.certNo)}
                </span>
              </div>
            </Col>
          </Row>
        </div>
        <div className={styles["take-list"]} onClick={onPrint}>
          <Row gutter={[0, 6]}>
            {registerRecord?.length > 0 &&
              registerRecord?.map((item: any, index: number) => {
                return (
                  <Col
                    span={24}
                    className={styles["item-take-num"]}
                    key={index}
                  >
                    <Row gutter={[6, 0]} justify="space-between" align="middle">
                      <Col span={4}>
                        <div className={styles["img-box"]}>
                          <img
                            src={require( "@/assets/images/others/dyj.png")}
                            alt="列表照片"
                          />
                        </div>
                      </Col>
                      <Col span={15}>
                        <div className={styles["doctor-info"]}>
                          <p className={styles.name}>
                            {item?.content?.yymc}
                          </p>
                          <p>
                            <span className={styles["black-color"]}>
                              {item?.content?.cardType || "-"}
                              <span className={styles.type}>
                                {item?.content?.totalAmount || "-"}元
                              </span>
                            </span>
                          </p>
                          <p>
                            <span className={styles["black-color"]}>
                              {item?.createTime || "-"}
                            </span>
                          </p>
                        </div>
                      </Col>
                      <Col span={5}>
                        <div className={styles["btn-wrapper"]}>
                          <Flex gap="middle" vertical>
                            <div
                              className={`${styles["item-btn"]} ${item?.count >= window.config.print_count ? styles["take-grey"] : styles["take-btn"]}`}
                              data-id={index}
                            >
                              {item?.count >= window.config.print_count ? "已打印" : "打印"}
                            </div>
                          </Flex>
                        </div>
                      </Col>
                    </Row>
                  </Col>
                );
              })}
            <Col span={24}>{isEmpty && <Empty />}</Col>
          </Row>
        </div>
      </div>
    </div>
  );
};

export default TakeList;
