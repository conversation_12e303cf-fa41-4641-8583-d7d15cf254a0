// TS根据身份证号获取出生日期、性别和年龄 1 出生日期 2 性别 3 年龄
export const IdCard = (IdCard: string, type: 1 | 2 | 3) => {
  if (!IdCard) return ''
  if (type === 1) {
    //获取出生日期
    const birthday =
      IdCard.substring(6, 10) + '-' + IdCard.substring(10, 12) + '-' + IdCard.substring(12, 14)
    return birthday
  }
  if (type === 2) {
    //获取性别
    if (parseInt(IdCard.substr(16, 1)) % 2 == 1) {
      return '1'
    } else {
      return '2'
    }
  }
  if (type === 3) {
    //获取年龄
    const ageDate = new Date()
    const month = ageDate.getMonth() + 1
    const day = ageDate.getDate()
    let age = ageDate.getFullYear() - parseInt(IdCard.substring(6, 10)) - 1
    if (
      parseInt(IdCard.substring(10, 12)) < month ||
      (parseInt(IdCard.substring(10, 12)) == month && parseInt(IdCard.substring(12, 14)) <= day)
    ) {
      age++
    }
    if (age <= 0) {
      age = 1
    }
    return age
  }
}

// 姓名脱敏
export const NamePrivate = (name: string) => {
  if (!name) {
    return ''
  }
  if (name.length === 2) {
    return name.substring(0, 1) + '*' //截取name 字符串截取第⼀个字符，
  } else if (name.length === 3) {
    return name.substring(0, 1) + '*' + name.substring(2, 3) //截取第⼀个和第三个字符
  } else if (name.length > 3) {
    return name.substring(0, 1) + '*' + '*' + name.substring(3, name.length) //截取第⼀个和⼤于第4个字符
  }
}

// 身份证号脱敏
export const IdNoPrivate = (idNo: string) => {
  if (!idNo) {
    return ''
  }
  const id = idNo.replace(/(\w{5})\w*(\w{4})/, '$1*********$2')
  return id
}

// 计算金额 保留两位小数
export function keepTwoDecimalFull(num: any) {
  let result = parseFloat(num)
  if (isNaN(result)) {
    return false
  }
  result = Math.round(num * 100) / 100
  let s_x = result.toString()
  let pos_decimal = s_x.indexOf('.')
  if (pos_decimal < 0) {
    pos_decimal = s_x.length
    s_x += '.'
  }
  while (s_x.length <= pos_decimal + 2) {
    s_x += '0'
  }
  return s_x
}

// 生成指定位数随机数字串
export function rand(m: number) {
  m = m > 16 ? 16 : m
  const num = Math.random().toString()
  if (num.substr(num.length - m, 1) === '0') {
    return rand(m)
  }
  return num.substring(num.length - m)
}

// 分组
export function group(arr: any, key: string) {
  const map = {},
    dest = []
  for (let i = 0; i < arr.length; i++) {
    const ai = arr[i]
    if (!map[ai[key]]) {
      dest.push({
        key: ai[key],
        data: [ai]
      })
      map[ai[key]] = ai
    } else {
      for (let j = 0; j < dest.length; j++) {
        const dj = dest[j]
        if (dj.key == ai[key]) {
          dj.data.push(ai)
          break
        }
      }
    }
  }
  return dest
}

// 计算各种金额展示
export function getMoneyCost(arr: any, key: string) {
  let money
  const list = [...arr]
  if (list.length > 0) {
    if (list.length > 1) {
      money = list.reduce((pre, cur) => {
        return parseFloat(pre) + parseFloat(cur[key] || 0)
      }, 0)
    } else {
      money = list[0][key] && parseFloat(list[0][key] || 0)
    }
  }
  money = keepTwoDecimalFull(money)
  return money
}

//从 array1 中删除 array2 中存在的对象
export function filterArray (arr1: any, arr2: any, key: any) {
  // 创建一个 Set 来存储 array2 中的唯一标识符
  const idsToRemove = new Set(arr2.map((item: any) => item[key]));
  // 过滤 array1，保留那些不在 idsToRemove 中的对象
  return arr1.filter((item: any) => !idsToRemove.has(item[key]));
};