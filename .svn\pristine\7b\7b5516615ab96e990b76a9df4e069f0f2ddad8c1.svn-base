// 确认费用明细 并上传诊断

import { useEffect, useState, useCallback} from 'react'
import { useNavigate } from 'react-router-dom'
import { Row, Col, Flex} from 'antd'
import { CalendarPicker, Form, TextArea, Button, Toast} from 'antd-mobile'
import { IdNoPrivate, group } from '@/utils/utils'
import { queryCostDetail, queryPreOutHospital} from '@/services/hospital'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import dayjs from 'dayjs'
import ChooseModal from '@/components/ChooseModal'
import ModuleHeader from '@/components/ModuleHeader'
import BlueBgTitle from '@/components/BlueBgTitle'
import styles from './index.module.scss'
import gotoMenu from '@/utils/gotoMenu'

/**
 * 查询费用明细
 * @param fields
 */
const handleQueryDetail = async (fields: any) => {
  console.log('查询费用明细fields', fields)
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        msg: '请求成功！',
        data: [
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-01-21 11:14:02',
            DANJIA: 80,
            FEIYONGMC: '上门服务费（基层医疗卫生机构）',
            JINE: 80,
            PATIENT_NO: '217892',
            JILUXH: '952307',
            FEIYONGDM: '20286262'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-01-21 11:14:02',
            DANJIA: 50,
            FEIYONGMC: '家庭病床建床费',
            JINE: 50,
            PATIENT_NO: '217892',
            JILUXH: '952306',
            FEIYONGDM: '20283907'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-01-21 11:14:02',
            DANJIA: 2,
            FEIYONGMC: '一般专项护理',
            JINE: 2,
            PATIENT_NO: '217892',
            JILUXH: '952310',
            FEIYONGDM: '20283805'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-01-21 11:14:02',
            DANJIA: 10,
            FEIYONGMC: '胃管置管',
            JINE: 10,
            PATIENT_NO: '217892',
            JILUXH: '952309',
            FEIYONGDM: '20283870'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-01-21 11:14:02',
            DANJIA: 130,
            FEIYONGMC: '鼻胃肠管',
            JINE: 130,
            PATIENT_NO: '217892',
            JILUXH: '952308',
            FEIYONGDM: '20286329'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-03-11 14:05:33',
            DANJIA: 130,
            FEIYONGMC: '鼻胃肠管',
            JINE: 130,
            PATIENT_NO: '217892',
            JILUXH: '995272',
            FEIYONGDM: '20286329'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-03-11 14:05:33',
            DANJIA: 80,
            FEIYONGMC: '上门服务费（基层医疗卫生机构）',
            JINE: 80,
            PATIENT_NO: '217892',
            JILUXH: '995271',
            FEIYONGDM: '20286262'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-03-11 14:05:33',
            DANJIA: 10,
            FEIYONGMC: '胃管置管',
            JINE: 10,
            PATIENT_NO: '217892',
            JILUXH: '995273',
            FEIYONGDM: '20283870'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-03-11 14:05:33',
            DANJIA: 2,
            FEIYONGMC: '一般专项护理',
            JINE: 2,
            PATIENT_NO: '217892',
            JILUXH: '995274',
            FEIYONGDM: '20283805'
          }
        ]
      }
    } else {
      response = await queryCostDetail({
        ...fields
      })
    }
    if (response.code === '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * 预出院
 * @param fields
 */
const handlePreOutHospital = async (fields: any) => {
  console.log("预出院fields", fields);
  try {
    let response;
    if (window.config.isDebug) {
      response = {
        code: '0',
        data: '',
        msg: '成功'
      }
    } else {
      response = await queryPreOutHospital({
        ...fields,
      });
    }
    if (response.code === "0") {
      return response;
    }
    return false;
  } catch (error) {
    return false;
  }
};

const List = () => {
  const [form] = Form.useForm();
  const [selectDiagnosis, setSelectDiagnosis] = useState<any[string]>([]); // 选中的诊断信息
  const [selectTypes, setSelectTypes] = useState<any[string]>(""); //弹出框数据类型
  const [multipleModalVisible, setMultipleModalVisible] = useState(false); //弹出框
  const { currentUser, medicalInfo, loginDocData} = useSelector((state: RootState) => state.userInfo)
  const [dateVisible, setDateVisible] = useState<boolean>(false)
  const [selectDate, setSelectDate] = useState(dayjs())
  const [detailData, setDetailData] = useState<any>()
  const navigate = useNavigate()
  const min = new Date()
  min.setMonth(min.getMonth() - 2)
  const max = new Date()
  max.setDate(max.getDate())

  const handleDateConfirm = val => {
    setSelectDate(dayjs(val))
  }

  const handleQuery = async () => {
    const res = await handleQueryDetail({
      patientID: currentUser.patientID,
      saID: window.config.SAID
    })
    if (!res) return gotoMenu(navigate)
    const formatData = res.map(item => {
      return {
        ...item,
        date: dayjs(item.FEIYONGRQ).format('YYYY-MM-DD HH')
      }
    })
    const dateGroup = group(formatData, 'date')
    console.log(dateGroup)
    setDetailData(dateGroup)
  }

  // 表单提交失败的方法
  const onFinishFailed = (errorInfo: any) => {
    // 可以在这里处理验证失败的情况
    console.log("Failed:", errorInfo);
    Toast.show({
      content: errorInfo.errorFields[0].errors[0],
      afterClose: () => {
        console.log("after");
      },
    });
  };

  const onFinish = async () => {
    const outPreDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
    const outPre = await handlePreOutHospital({
      preYuChuYuanDate: outPreDate,
      yonghuID: loginDocData?.YONGHUID,
      saID: window.config.SAID,
      patientID: currentUser.patientID,
      diagCode: selectDiagnosis[0].value,
      diagICD: selectDiagnosis[0].icd,
      diagName: selectDiagnosis[0].name,
    });
    if(!outPre) return;
    navigate('/hospital/discharge', { replace: true })
  }

  useEffect(() => {
    // 已经是预出院状态，跳转直接结算
    if(currentUser.bingrenzt === '0'){
      navigate('/hospital/discharge', { replace: true })
      return
    }
    handleQuery();
  }, [])

  return (
    <>
      <ModuleHeader title={'确认出院'} />
      <div className={[styles['wrapper'], styles['scroll-style']].join(' ')}>
        <div className={styles['confirm-page-wrapper']}>
          <Row gutter={[0, 6]}>
            <Col span={24}>
              <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
                <BlueBgTitle title={'病人信息'} />
                <div className={styles['info-box']}>
                  <div className={styles['item']}>
                    <div className={styles.title}>
                      姓名<i></i>
                    </div>
                    <span>{currentUser?.patientName}</span>
                  </div>
                  <div className={styles['item']}>
                    <div className={styles.title}>
                      证件号<i></i>
                    </div>
                    <span>{IdNoPrivate(currentUser?.idCard)}</span>
                  </div>
                  <div className={styles['item']}>
                    <div className={styles.title}>
                      病案号<i></i>
                    </div>
                    <span>{currentUser?.patientID}</span>
                  </div>
                </div>
              </div>
            </Col>
            <Col span={24}>
              <div className={[styles['confirm-info-list-wrapper'], styles['pay']].join(' ')}>
                <BlueBgTitle title={'费用确认'} />
                <div className={styles['pay-info-wrapper']}>
                  {detailData &&
                    detailData.length > 0 &&
                    detailData.map(item => {
                      return (
                        <div className={styles['pay-item']} key={item.key}>
                          <div className={styles['pay-summary']}>
                            费用日期：{item.key?.split(' ')[0]}
                          </div>
                          <Row>
                            <Col span={14}>
                              <div className={[styles['title'], styles['name']].join(' ')}>名称</div>
                            </Col>
                            <Col span={5}>
                              <div className={styles.title}>数量</div>
                            </Col>
                            <Col span={5}>
                              <div className={styles.title}>金额</div>
                            </Col>
                          </Row>
                          {item.data.map(i => {
                            return (
                              <Row key={i.JILUXH}>
                                <Col span={14}>
                                  <div className={[styles['content'], styles['name']].join(' ')}>{i.FEIYONGMC}</div>
                                </Col>
                                <Col span={5}>
                                  <div className={styles.content}>{i.SHULIANG}</div>
                                </Col>
                                <Col span={5}>
                                  <div className={styles.content}>{i.JINE}元</div>
                                </Col>
                              </Row>
                            )
                          })}
                        </div>
                      )
                    })}
                </div>
              </div>
            </Col>
            <Col span={24}>
              <div className={[styles['confirm-info-list-wrapper'], styles['pay']].join(' ')}>
                <BlueBgTitle title={'出院信息'} />
                <Form
                  name='form'
                  form={form}
                  onFinish={onFinish}
                  onFinishFailed={onFinishFailed}
                  footer={
                    <Button block type='submit' color='primary' size='middle' shape='rounded'>
                      确认出院
                    </Button>
                  }
                >
                  <Form.Item
                    name="diagnosis"
                    label="出院诊断"
                    rules={[{ required: true, message: "请选择出院诊断" }]}
                  >
                    <TextArea placeholder="请选择出院诊断" readOnly rows={2} onClick={(event) => {
                      event.stopPropagation();
                      event.preventDefault(); // 阻止表单的默认提交行为
                      setSelectTypes("cbzd");
                      setMultipleModalVisible(true);
                    }} />
                  </Form.Item>
                  { selectDiagnosis.length > 0 && <div className={styles['clear-btn']}><Button color='primary' size='mini' shape='rounded' onClick={()=>{
                    setSelectDiagnosis('')
                    form.setFieldsValue({
                      diagnosis: ''
                    });
                  }}>清除已选诊断</Button></div> }
                </Form>
              </div>
            </Col>
          </Row>
        </div>
      </div>
      <CalendarPicker
        visible={dateVisible}
        selectionMode='single'
        min={min}
        max={max}
        defaultValue={selectDate.toDate()}
        onClose={() => setDateVisible(false)}
        onMaskClick={() => setDateVisible(false)}
        onConfirm={handleDateConfirm}
      />
      
      <ChooseModal
        visibleListPopup={multipleModalVisible}
        onCancel={() => {
          setMultipleModalVisible(false)
        }}
        onConfirm={(val: any) => {
          setMultipleModalVisible(false)
          // 诊断信息
          if (selectTypes === "cbzd") {
            // 改成单选
            // const _arr = [...selectDiagnosis, ...val]
            // // 数组去重
            // const map = new Map()
            // const _nwearr = _arr.filter((v: any) => !map.has(v.code) && map.set(v.code, v))
            setSelectDiagnosis(val)
            // const diagnosisStr = _nwearr.map(item => item.label).join("，");
            form.setFieldsValue({
              diagnosis: val[0].label
            });
          }
        }}
        selectType = {'cbzd'}  // 选择的类型
        isSearch={true} //是否显示搜索框
        isMultiple= {false} // 支持多选
      ></ChooseModal>
    </>
  )
}

export default List
