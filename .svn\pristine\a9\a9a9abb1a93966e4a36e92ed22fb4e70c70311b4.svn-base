import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Row, Col } from 'antd'
import { CalendarPicker, Picker, Dialog, Toast} from 'antd-mobile'
import { IdNoPrivate, group } from '@/utils/utils'
import { RightOutlined } from '@ant-design/icons'
import { queryCostDetail, delateCharge} from '@/services/hospital'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import dayjs from 'dayjs'
import ModuleHeader from '@/components/ModuleHeader'
import BlueBgTitle from '@/components/BlueBgTitle'
import styles from './index.module.scss'
import gotoMenu from '@/utils/gotoMenu'

/**
 * 查询费用明细
 * @param fields
 */
const handleQueryDetail = async (fields: any, isLoading = false) => {
  console.log('查询费用明细fields', fields)
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        msg: '请求成功！',
        data: [
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-01-21 11:14:02',
            DANJIA: 80,
            FEIYONGMC: '上门服务费（基层医疗卫生机构）',
            JINE: 80,
            PATIENT_NO: '217892',
            JILUXH: '952307',
            FEIYONGDM: '20286262'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-01-21 11:14:02',
            DANJIA: 50,
            FEIYONGMC: '家庭病床建床费',
            JINE: 50,
            PATIENT_NO: '217892',
            JILUXH: '952306',
            FEIYONGDM: '20283907'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-01-21 11:14:02',
            DANJIA: 2,
            FEIYONGMC: '一般专项护理',
            JINE: 2,
            PATIENT_NO: '217892',
            JILUXH: '952310',
            FEIYONGDM: '20283805'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-01-21 11:14:02',
            DANJIA: 10,
            FEIYONGMC: '胃管置管',
            JINE: 10,
            PATIENT_NO: '217892',
            JILUXH: '952309',
            FEIYONGDM: '20283870'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-01-21 11:14:02',
            DANJIA: 130,
            FEIYONGMC: '鼻胃肠管',
            JINE: 130,
            PATIENT_NO: '217892',
            JILUXH: '952308',
            FEIYONGDM: '20286329'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-03-11 14:05:33',
            DANJIA: 130,
            FEIYONGMC: '鼻胃肠管',
            JINE: 130,
            PATIENT_NO: '217892',
            JILUXH: '995272',
            FEIYONGDM: '20286329'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-03-11 14:05:33',
            DANJIA: 80,
            FEIYONGMC: '上门服务费（基层医疗卫生机构）',
            JINE: 80,
            PATIENT_NO: '217892',
            JILUXH: '995271',
            FEIYONGDM: '20286262'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-03-11 14:05:33',
            DANJIA: 10,
            FEIYONGMC: '胃管置管',
            JINE: 10,
            PATIENT_NO: '217892',
            JILUXH: '995273',
            FEIYONGDM: '20283870'
          },
          {
            SHULIANG: 1,
            FEIYONGRQ: '2025-03-11 14:05:33',
            DANJIA: 2,
            FEIYONGMC: '一般专项护理',
            JINE: 2,
            PATIENT_NO: '217892',
            JILUXH: '995274',
            FEIYONGDM: '20283805'
          }
        ]
      }
    } else {
      response = await queryCostDetail({
        ...fields
      }, isLoading)
    }
    if (response.code === '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * 费用删除
 * @param fields
 */
const handleDelateCharge = async (fields: any) => {
  console.log('费用删除fields', fields)
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        data: '',
        msg: '成功'
      }
    } else {
      response = await delateCharge({
        ...fields
      })
    }
    if (response.code === '0') {
      return response
    }
    return response
  } catch (error) {
    return false
  }
}

const List = () => {
  const { currentUser, loginDocData} = useSelector((state: RootState) => state.userInfo)
  const [dateVisible, setDateVisible] = useState<boolean>(false)
  const [selectDate, setSelectDate] = useState(dayjs())
  const [detailData, setDetailData] = useState<any>()
  const [numPickerVisible, setNumPickerVisible] = useState<boolean>(false);
  const [basicColumns, setBasicColumns] = useState<any>([])
  const [delateItem, setDelateItem] = useState<any>()
  const navigate = useNavigate()
  const min = new Date()
  min.setMonth(min.getMonth() - 2)
  const max = new Date()
  max.setDate(max.getDate())

  const handleDateConfirm = val => {
    setSelectDate(dayjs(val))
  }

  const handleQuery = async (isLoading) => {
    const res = await handleQueryDetail({
      patientID: currentUser.patientID,
      saID: window.config.SAID
    }, isLoading)
    if (!res) return gotoMenu(navigate)
    const formatData = res.map(item => {
      return {
        ...item,
        date: dayjs(item.FEIYONGRQ).format('YYYY-MM-DD HH')
      }
    })
    const dateGroup = group(formatData, 'date')
    console.log(dateGroup)
    setDetailData(dateGroup)
  }

  const handleDelate = (item) => {
    console.log(item);
    if(item.SHULIANG > 1){
      let picker = []
      for(let i = 1; i <= item.SHULIANG; i++){
        picker.push({
          label: i, 
          value: i
        })
      }
      console.log(picker)
      setDelateItem(item)
      setBasicColumns([picker])
      setNumPickerVisible(true)
      return
    }
    Dialog.confirm({
      content: `是否确认删除${item.SHULIANG}项${item.FEIYONGMC}`,
      onConfirm: async () => {
        const res = await handleDelateCharge({
          jiluXh: item.JILUXH,
          patientID: currentUser.patientID,
          saID: window.config.SAID,
          tuifeiSl: 1,
          yonghuID: loginDocData.YONGHUID
        })
        if(!res) return;
        Toast.show({
          content: '费用删除成功',
        })
        handleQuery(true)
      },
    })
  }

  const handlePickerNum = (val) => {
     console.log(val) // [1]
     Dialog.confirm({
      content: `是否确认删除${val[0]}项${delateItem.FEIYONGMC}`,
      onConfirm: async () => {
        const res = await handleDelateCharge({
          jiluXh: delateItem.JILUXH,
          patientID: currentUser.patientID,
          saID: window.config.SAID,
          tuifeiSl: val[0],
          yonghuID: loginDocData.YONGHUID
        })
        if(!res) return;
        Toast.show({
          content: '费用删除成功',
        })
        handleQuery(true)
      },
    })
  }

  useEffect(() => {
    handleQuery(false)
  }, [])

  return (
    <>
      <ModuleHeader title={'费用明细查询'} />
      <div className={[styles['wrapper'], styles['scroll-style']].join(' ')}>
        <div className={styles['confirm-page-wrapper']}>
          <Row gutter={[0, 6]}>
            <Col span={24}>
              <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
                <BlueBgTitle title={'病人信息'} />
                <div className={styles['info-box']}>
                  <div className={styles['item']}>
                    <div className={styles.title}>
                      姓名<i></i>
                    </div>
                    <span>{currentUser?.patientName}</span>
                  </div>
                  <div className={styles['item']}>
                    <div className={styles.title}>
                      证件号<i></i>
                    </div>
                    <span>{IdNoPrivate(currentUser?.idCard)}</span>
                  </div>
                  <div className={styles['item']}>
                    <div className={styles.title}>
                      病案号<i></i>
                    </div>
                    <span>{currentUser?.patientID}</span>
                  </div>
                </div>
              </div>
            </Col>
            <Col span={24}>
              <div className={[styles['confirm-info-list-wrapper'], styles['pay']].join(' ')}>
                <BlueBgTitle title={'费用明细'} />
                <div className={styles['pay-info-wrapper']}>
                  {detailData &&
                    detailData.length > 0 &&
                    detailData.map(item => {
                      return (
                        <div className={styles['pay-item']} key={item.key}>
                          <div className={styles['pay-summary']}>
                            费用日期：{item.key?.split(' ')[0]}
                          </div>
                          <Row>
                            <Col span={9}>
                              <div className={[styles['title'], styles['name']].join(' ')}>名称</div>
                            </Col>
                            <Col span={5}>
                              <div className={styles.title}>数量</div>
                            </Col>
                            <Col span={5}>
                              <div className={styles.title}>金额</div>
                            </Col>
                            <Col span={5}>
                              <div className={styles.title}>操作</div>
                            </Col>
                          </Row>
                          {item.data.map(i => {
                            return (
                              <Row key={i.JILUXH}>
                                <Col span={9}>
                                  <div className={[styles['content'], styles['name']].join(' ')}>{i.FEIYONGMC}</div>
                                </Col>
                                <Col span={5}>
                                  <div className={styles.content}>{i.SHULIANG}</div>
                                </Col>
                                <Col span={5}>
                                  <div className={styles.content}>{i.JINE}元</div>
                                </Col>
                                <Col span={5}>
                                  { i.KETUISL > 0 && <div className={styles.action} onClick={() => handleDelate(i)}>删除</div>}
                                </Col>
                              </Row>
                            )
                          })}
                        </div>
                      )
                    })}
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
      <Picker
        columns={basicColumns}
        visible={numPickerVisible}
        title={'请选择要删除的数量'}
        onClose={() => {
          setNumPickerVisible(false)
        }}
        onConfirm={v => {
          handlePickerNum(v)
        }}
      />
      <CalendarPicker
        visible={dateVisible}
        selectionMode='single'
        min={min}
        max={max}
        defaultValue={selectDate.toDate()}
        onClose={() => setDateVisible(false)}
        onMaskClick={() => setDateVisible(false)}
        onConfirm={handleDateConfirm}
      />
    </>
  )
}

export default List
