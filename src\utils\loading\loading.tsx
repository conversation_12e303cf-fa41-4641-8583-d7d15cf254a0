import { useEffect, useState, useRef } from 'react'
import ReactDOM from 'react-dom/client'
// import { CloseCircleOutlined } from "@ant-design/icons";
import styles from './loading.module.scss'

let requestCount = 0
let startTime = 0
let time = ''

const LoadingContainer = (data: any) => {
  const { mainBody, dom, loadingText } = data
  const { text, second } = loadingText
  const timeOutId = useRef<any>(null)
  const [count, setCount] = useState(second)
  const load = document.getElementById('loading')
  const minorItemLogo = new URL('../assets/images/gifs/loading.gif', import.meta.url).href
  if (load) {
    return <></>
  }
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    timeOutId.current = setTimeout(() => {
      if (count > 1) {
        setCount((c: number) => c - 1)
      } else {
        if (mainBody && dom && dom.parentNode === mainBody) {
          mainBody.removeChild(dom)
        }
      }
    }, 1000)
    time = timeOutId.current
    return () => clearTimeout(timeOutId.current)
  }, [count])

  return (
    <div className={styles['loading-container']}>
      <div className={styles['loading-modal-init']}>
        {/* <CloseCircleOutlined className={styles.close} onClick={() => {
          if (mainBody && dom && dom.parentNode === mainBody) {
            mainBody.removeChild(dom);
            clearTimeout(timeOutId.current);
          }
        }} /> */}
        <img className={styles.logo} src={minorItemLogo} alt='加载中...'></img>
        {second ? (
          <div className={styles.text}>{`正在加载，预计需要${count}秒`}</div>
        ) : (
          <div className={styles.text}>{text || '正在加载，请稍候...'}</div>
        )}
      </div>
    </div>
  )
}

export default {
  start: function (loadingText: string) {
    if (requestCount === 0) {
      startTime = new Date().getTime()
      const dom = document.createElement('div')
      dom.setAttribute('class', 'myLoading')
      const mainBody = document.getElementById('root') as HTMLElement
      mainBody.appendChild(dom)
      ReactDOM.createRoot(dom).render(
        <LoadingContainer loadingText={loadingText} mainBody={mainBody} dom={dom} />
      )
    }
    requestCount++
  },
  end: function () {
    if (time) clearTimeout(time)
    if (requestCount <= 0) return
    requestCount--
    if (requestCount === 0) {
      const endTime = new Date().getTime()
      // 请求接口超1000毫秒，关闭弹出框
      const timer = endTime - startTime >= 1000 ? 0 : 1000
      setTimeout(() => {
        const load = document.getElementById('loading')
        if (load) {
          document.body.removeChild(load)
        }
        const mainBody = document.getElementById('root') as HTMLElement
        const dom = document.querySelector('.myLoading') as HTMLElement
        return mainBody.removeChild(dom)
      }, timer)
    }
  }
}
