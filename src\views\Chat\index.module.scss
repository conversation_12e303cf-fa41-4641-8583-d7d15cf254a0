@import '@/assets/scss/base.scss';

.chatContainer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #f5f5f5;
  position: relative;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.loadingMore {
  text-align: center;
  padding: 12px;
  color: #666;
  font-size: 14px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  margin-bottom: 12px;
}

.messageItem {
  margin-bottom: 16px;
  display: flex;
  
  &.userMessage {
    justify-content: flex-end;
    
    .messageContent {
      background-color: #1890ff;
      color: white;
      border-radius: 18px 18px 4px 18px;
      max-width: 70%;
    }
  }
  
  &.doctorMessage {
    justify-content: flex-start;
    
    .messageContent {
      background-color: white;
      color: #333;
      border-radius: 18px 18px 18px 4px;
      max-width: 70%;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }
}

.messageContent {
  padding: 12px 16px;
  position: relative;
  word-wrap: break-word;
  word-break: break-all;
}

.textMessage {
  font-size: 16px;
  line-height: 1.4;
}

.imageMessage {
  img {
    border-radius: 8px;
    display: block;
  }
}

.messageTime {
  font-size: 12px;
  margin-top: 4px;
  opacity: 0.7;
  display: flex;
  align-items: center;
  gap: 4px;
}

.messageStatus {
  font-size: 11px;
}

.inputContainer {
  background-color: white;
  border-top: 1px solid #e8e8e8;
  padding: 12px 16px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
}

.inputWrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f7f7f7;
  border-radius: 24px;
  padding: 8px 12px;
}

.imageButton {
  color: #666;
  border: none;
  background: none;
  padding: 4px;
  min-width: auto;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.textInput {
  flex: 1;
  border: none;
  background: none;
  font-size: 16px;
  
  :global {
    .adm-input-element {
      background: none;
      border: none;
      outline: none;
      font-size: 16px;
    }
  }
}

.sendButton {
  border-radius: 50%;
  width: 36px;
  height: 36px;
  min-width: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:disabled {
    background-color: #ccc;
    border-color: #ccc;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .messageItem {
    &.userMessage .messageContent,
    &.doctorMessage .messageContent {
      max-width: 85%;
    }
  }
  
  .textMessage {
    font-size: 15px;
  }
  
  .inputWrapper {
    padding: 6px 10px;
  }
  
  .sendButton {
    width: 32px;
    height: 32px;
    min-width: 32px;
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .chatContainer {
    background-color: #1a1a1a;
  }
  
  .messagesContainer {
    background-color: #1a1a1a;
  }
  
  .messageItem {
    &.doctorMessage .messageContent {
      background-color: #2a2a2a;
      color: #e8e8e8;
    }
  }
  
  .inputContainer {
    background-color: #2a2a2a;
    border-top-color: #3a3a3a;
  }
  
  .inputWrapper {
    background-color: #3a3a3a;
  }
  
  .imageButton {
    color: #ccc;
  }
}

// 动画效果
.messageItem {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 消息状态指示器
.messageStatus {
  &:before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 4px;
    background-color: currentColor;
  }
}

// 图片预览优化
.imageMessage {
  img {
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:hover {
      transform: scale(1.02);
    }
  }
}

// 输入框焦点状态
.inputWrapper:focus-within {
  background-color: #f0f0f0;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
