@import '../../../assets/scss/base.scss';

.emergency-list-wrapper {
  height: calc(100vh - 280px / $pixel);
  padding: 0 calc(25px / $pixel);
}

.my-form {
  border-radius: calc(16px / $pixel);
}

.my-input {
  --font-size: var(--adm-font-size-7);
}


.info-box {
  margin-top: calc(20px / $pixel);
  padding: calc(20px / $pixel);
  border-radius: calc(16px / $pixel);
  background-color: #fff;
  font-size: calc(30px / $pixel);
  line-height: calc(60px / $pixel);
  color: #383838;
  font-weight: 500;
}

.diagnosis-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: calc(20px / $pixel);
  background-color: #fff;
  padding: calc(20px / $pixel) calc(80px / $pixel) calc(20px / $pixel) calc(20px / $pixel);
  border-radius: calc(16px / $pixel);
  color: #383838;

  .label {
    display: flex;
    align-items: center;

    >img {
      width: calc(30px / $pixel);
      height: calc(35px / $pixel);
    }

    >span {
      line-height: calc(45px / $pixel);
      font-size: calc(30px / $pixel);
      font-weight: 500;
      margin-left: calc(20px / $pixel);
    }
  }

  .content {
    >span {
      line-height: calc(45px / $pixel);
      font-size: calc(30px / $pixel);
      font-weight: 500;
      margin-left: calc(20px / $pixel);
      color: #1677ff;
    }
  }
}

.item-table {
  min-height: calc(660px / $pixel);
  margin: calc(20px / $pixel) 0;
  background-color: #fff;
  padding: calc(20px / $pixel);
  border-radius: calc(16px / $pixel);

  .title-wrap {
    font-size: calc(30px / $pixel);
    font-weight: 400;
    color: #383838;
    margin-bottom: calc(20px / $pixel);
  }
}

.button-wrapper {
  padding-bottom: calc(20px / $pixel);
}
