// import React, { useState, useEffect } from 'react'
import styles from './loading.module.scss'

export default function LoadingContainer({ loadingText }) {
  // const [remainingTime, setRemainingTime] = useState(60)
  const load = document.getElementById('loading')
  // useEffect(() => {
  //   let timerID = null
  //   if (remainingTime > 0) {
  //     timerID = setInterval(() => {
  //       setRemainingTime(prevTime => prevTime - 1)
  //     }, 1000)
  //   }
  //   return () => {
  //     if (timerID) {
  //       clearInterval(timerID)
  //     }
  //   }
  // }, [remainingTime])

  if (load) {
    return <></>
  }
  return (
    <div className={styles['loading-container']}>
      <div className={styles['loading-modal-init']}>
        <img
          className={styles['logo']}
          src={require('@/assets/images/gifs/loading.gif')}
          alt=''
        ></img>
        <div className={styles['text']}>{loadingText || '正在加载中，请稍候...'}</div>
      </div>
    </div>
  )
}
