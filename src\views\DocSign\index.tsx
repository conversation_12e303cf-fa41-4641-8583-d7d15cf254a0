import { useSelector } from 'react-redux'
import { IdNoPrivate, IdCard } from '@/utils/utils'
import { Form, Input, Button, Selector, Radio, Space } from 'antd-mobile'
import { Flex } from 'antd'
import { useNavigate } from 'react-router-dom'
import BlueBgTitle from '@/components/BlueBgTitle'
import ModuleHeader from '@/components/ModuleHeader'
import { RootState } from '@/store/index'
import ETipModal from '@/components/ETipModal'
import gotoMenu from '@/utils/gotoMenu'
// import useDebounce from "@/common/useDebounce"; //防抖封装函数
import styles from './index.module.scss'

const PatientCreate = () => {
  const [form] = Form.useForm()
  // const dispatch = useDispatch();
  const navigate = useNavigate()
  // const [search] = useSearchParams();
  // const nextpage = search.get("nextpage");

  const { readCardData, currentUser }: any = useSelector((state: RootState) => state.userInfo)
  console.log('readCardData', readCardData)

  const onFinish = (values: any) => {
    console.log('Success:', values)
    ETipModal('家庭医生签约成功', 'success', () => {
      gotoMenu(navigate)
    })
  }

  return (
    <>
      <div className={styles.container}>
        <ModuleHeader title='家庭医生签约' />
        <div className={styles.wrapper}>
          <div className={styles.confirm}>
            <BlueBgTitle title={'用户信息'} />
            <div className={styles['line-wrapper']}>
              <div className={`${styles['circle-box']} ${styles.left}`}></div>
              <div className={styles.line}></div>
              <div className={`${styles['circle-box']} ${styles.right}`}></div>
            </div>
            <div className={styles['title-wrapper']}>
              <div className={styles.title}>
                姓名
                <i></i>
              </div>
              <span className={styles['black-span']}>{currentUser?.patientName}</span>
            </div>
            <div className={styles['title-wrapper']}>
              <div className={styles.title}>
                身份证号
                <i></i>
              </div>
              {currentUser?.patientIDCard && (
                <span className={styles['black-span']}>
                  {IdNoPrivate(currentUser?.patientIDCard || '')}
                </span>
              )}
            </div>
            <div className={styles['title-wrapper']}>
              <div className={styles.title}>
                出生日期
                <i></i>
              </div>
              <span className={styles['black-span']}>
                {IdCard(currentUser?.patientIDCard || '', 1)}
              </span>
            </div>
            <div className={styles['title-wrapper']}>
              <div className={styles.title}>
                手机号
                <i></i>
              </div>
              <span className={styles['black-span']}>{currentUser?.phone}</span>
            </div>
            <div className={styles['title-wrapper']}>
              <div className={styles.title}>
                档案编号
                <i></i>
              </div>
              <span className={styles['black-span']}>
                {IdNoPrivate(currentUser?.patientIDCard) || ''}
              </span>
            </div>
            <div className={styles['title-wrapper']}>
              <div className={styles.title}>
                地址
                <i></i>
              </div>
              <span className={styles['black-span']}>杭州市余杭区余杭街道</span>
            </div>
            <Form
              name='form'
              form={form}
              // layout="horizontal"
              onFinish={onFinish}
              footer={
                <Button block type='submit' color='primary' size='middle' shape='rounded'>
                  提交
                </Button>
              }
            >
              {/* <Form.Header>
                <Flex align="center" className={styles.label}>
                  <img
                    src={require("@/assets/images/others/diagnosis.png")}
                  ></img>
                  <span>医生工号</span>
                </Flex>
              </Form.Header> */}
              {/* <Form.Item
                name="num"
                label=""
                rules={[{ required: true, message: "必填，请输入" }]}
              >
                <Input placeholder="必填，请输入" readOnly />
              </Form.Item> */}
              <Form.Header>
                <Flex align='center' className={styles.label}>
                  <img src={require('@/assets/images/others/diagnosis.png')}></img>
                  <span>医生信息</span>
                </Flex>
              </Form.Header>
              <Form.Item
                name='num'
                label='医生工号'
                rules={[{ required: true, message: '必填，不能为空' }]}
              >
                <Input
                  placeholder='必填，请输入医生工号'
                  // readOnly
                  clearable
                />
              </Form.Item>
              <Form.Item
                name='dept'
                label='组长电话'
                // rules={[{ required: true, message: "不能为空" }]}
              >
                <Input type='number' placeholder='请输入组长电话' clearable />
              </Form.Item>
              <Form.Item name='doctor' label='签约医生'>
                <Input placeholder='请输入签约医生' clearable />
              </Form.Item>
              <Form.Item
                name='diagnosis'
                label='医生电话'
                // rules={[{ required: true, message: "不能为空" }]}
              >
                <Input type='number' placeholder='请输入医生电话' clearable />
              </Form.Item>
              <Form.Item name='diagnosis' label='签约助理'>
                <Input placeholder='请输入签约助理' clearable />
              </Form.Item>
              <Form.Header>
                <Flex align='center' className={styles.label}>
                  <img src={require('@/assets/images/others/diagnosis.png')}></img>
                  <span>人群分类</span>
                </Flex>
              </Form.Header>
              <Form.Item name='marriage' label='' rules={[{ required: true, message: '不能为空' }]}>
                <Radio.Group>
                  <Space direction='vertical' block={true}>
                    <Radio value='1'>老年人</Radio>
                    <Radio value='2'>0-12婴幼儿</Radio>
                    <Radio value='3'>孕妇</Radio>
                    <Radio value='4'>高血压</Radio>
                  </Space>
                </Radio.Group>
              </Form.Item>
              <Form.Header>
                <Flex align='center' className={styles.label}>
                  <img src={require('@/assets/images/others/diagnosis.png')}></img>
                  <span>服务包</span>
                </Flex>
              </Form.Header>
              <Form.Item
                name='favoriteFruits'
                label=''
                rules={[{ required: true, message: '不能为空' }]}
              >
                <Selector
                  columns={3}
                  multiple
                  options={[
                    { label: '基础服务包', value: 'apple' },
                    { label: '全科服务基础包', value: 'orange' },
                    { label: '儿童服务包', value: 'banana' },
                    { label: '残疾人服务包', value: 'apple1' }
                  ]}
                />
              </Form.Item>
              {/* <Form.Item
                name="duoxuan"
                label="多选"
                trigger="onConfirm"
                rules={[{ required: true, message: "不能为空" }]}
                onClick={() => {
                  // console.log(e, datePickerRef);
                  setVisibleListPopup(true);
                }}
              >
                <Input placeholder="请选择服务包" readOnly clearable />
              </Form.Item> */}
            </Form>
          </div>
        </div>
      </div>
    </>
  )
}

export default PatientCreate
