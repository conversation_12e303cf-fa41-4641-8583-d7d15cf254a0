import {
  createPay,
  acquiringPay,
  queryOrderPay,
  closedPay,
  addPayInfo,
  hisPreSettlement,
  paymentSettlement,
  refundPay
} from '@/services/pay'
import {
  outHospitalSettlement,
  rechargeSettlement,
  getInvoice,
  getInvoiceDetail
} from '@/services/hospital'
import {
  registerSettlement,
  registerPreSettlement,
  cancelLockNumber,
  lockNumber
} from '../../services/register'
import { sendPrint, saveCreditsPostStr } from '../../services/print'

/**
 * @description: 院内订单缴费二维码生成
 * @param {*} fields
 * @return {*}
 */
export const handleCreatePay = async fields => {
  try {
    const response = await createPay({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * @description: 院内订单主扫支付
 * @param {*} fields
 * @return {*}
 */
export const handleAcquiringPay = async fields => {
  try {
    const response = await acquiringPay({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return response
  } catch (error) {
    return false
  }
}

/**
 * @description: 支付信息查询
 * @param {*} fields
 * @return {*}
 */
export const handleOrderPay = async fields => {
  try {
    const response = await queryOrderPay({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * 关闭订单
 * @param fields
 */
export const handleClosedPay = async fields => {
  try {
    const response = await closedPay({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * 订单退款
 * @param fields
 */
export const handleRefund = async fields => {
  try {
    const response = await refundPay({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * HIS预结算
 * @param fields
 */
export const handleHisPre = async fields => {
  try {
    const response = await hisPreSettlement({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * @description: 打印
 * @param {*} fields 打印参数
 * @return {*}
 */
export const handlePrint = async fields => {
  try {
    const response = await sendPrint(fields)
    if (response.code === '0') {
      return true
    }
    return false
  } catch (error) {
    return false
  }
}

// 保存凭条
export const handleSave = async fields => {
  try {
    const response = await saveCreditsPostStr({
      ...fields
    })
    if (response.code === '0') {
      return true
    }
    // return response;
    return false
  } catch (error) {
    return false
  }
}

// 挂号预结算
export const handleRegisterPreSettlement = async fields => {
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        msg: '请求成功！',
        data: {
          preRegisterID: '',
          totalMoney: '25.0',
          medicalAmount: '20.0',
          privateAmount: '5.0',
          deductionMoney: '0.0',
          preSettleID: '',
          apiPar:
            '{"bingRenXM":"唐玉仙","jiuZhenSJ":"08:00-08:30","dianHua":"13584159620","keShiMC":"中医妇科","jinE":"0.0","yiZhouPBID":"04-15","dangTianPBID":"04-15","jiuZhenRQ":"2024-04-15","duKaFS":"","yuYueGHId":"2907188","guaHaoXH":"3","yiShengDM":"2000965","zhengJianHM":"320219195507206269","hisYuJieSuanID":"2113548","keShiDM":"012","yiBaoJE":"0.0","bingRenLX":"1","yiBaoDKXX":"","guaHaoBC":"1","yiBaoKa":"","ziFeiJE":"25.0","yiBaoData":"","yiShengXM":"司晨君"}'
        }
      }
    } else {
      response = await registerPreSettlement({
        ...fields
      })
    }
    if (response.code === '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

// 挂号结算
export const handleRegisterSettlement = async fields => {
  try {
    let response
    if (window.config.isDebug) {
      response = { code: '0', msg: '请求成功！', data: { guidedID: '23435639' } }
    } else {
      response = await registerSettlement({
        ...fields
      })
    }
    if (response.code === '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

// 多单据结算
export const handlePaymentSettlement = async fields => {
  try {
    const response = await paymentSettlement({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

// 锁号
export const handleLockNumber = async fields => {
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        msg: '请求成功！',
        data: {
          billNo: '2907188',
          scheduleID: null,
          orderNo: '3',
          guidedInfo: '请到【城中院区门诊二楼中医科诊区】就诊',
          apiPar:
            '{"bingRenXM":"唐玉仙","guaHaoID":"","jiuZhenSJ":"08:00-08:30","dianHua":"13584159620","guaHaoLB":"6","keShiMC":"中医妇科","jinE":"0.0","yiZhouPBID":"04-15","dangTianPBID":"04-15","jiuZhenRQ":"2024-04-15","duKaFS":"","yuYueGHId":"2907188","guaHaoXH":"3","yiShengDM":"2000965","zhengJianHM":"320219195507206269","keShiDM":"012","bingRenLX":"1","yiBaoDKXX":"","guaHaoBC":"1","yiBaoKa":"","yiBaoData":"","yiShengXM":"司晨君"}'
        }
      }
    } else {
      response = await lockNumber({
        ...fields
      })
    }
    if (response.code === '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

// 取消锁号
export const handleCancelLockNumber = async fields => {
  try {
    const response = await cancelLockNumber({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * @description: 住院充值
 * @param {*} fields
 * @return {*}
 */
export const handleRecharge = async fields => {
  try {
    const response = await rechargeSettlement({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * @description: 出院结算
 * @param {*} fields
 * @return {*}
 */
export const handleOutHospital = async fields => {
  try {
    const response = await outHospitalSettlement({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * @description: 出院结算查询发票二维码
 * @param {*} fields
 * @return {*}
 */
export const handleGetInvoice = async fields => {
  try {
    const response = await getInvoice({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * @description: 出院结算查询发票详情
 * @param {*} fields
 * @return {*}
 */
export const handleGetInvoiceDetail = async fields => {
  try {
    const response = await getInvoiceDetail({
      ...fields
    })
    if (response.code === '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * @description: 添加支付信息
 * @param {*} fields
 * @return {*}
 */
export const handleAddPayInfo = async fields => {
  try {
    const response = await addPayInfo({
      ...fields
    })
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}
