import { createSlice, PayloadAction } from '@reduxjs/toolkit'
interface RegisterState {
  selectDept: any
  selectDoc: any
  selectSchedule: any
  selectOrderNum: any
}
const initialState: RegisterState = {
  selectDept: null,
  selectDoc: null,
  selectSchedule: null,
  selectOrderNum: null
}
export const register = createSlice({
  name: 'register',
  // state数据的初始值
  initialState: initialState,
  reducers: {
    setDept(state, action: PayloadAction<any>) {
      state.selectDept = action.payload
    },
    setDoctor(state, action: PayloadAction<any>) {
      state.selectDoc = action.payload
    },
    setSchedule(state, action: PayloadAction<any>) {
      state.selectSchedule = action.payload
    },
    setSelectOrderNum(state, action: PayloadAction<any>) {
      state.selectOrderNum = action.payload
    }
  }
})
