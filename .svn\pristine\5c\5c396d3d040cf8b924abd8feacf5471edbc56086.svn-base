import { createSlice, PayloadAction } from '@reduxjs/toolkit'
interface UserState {
  listData: any
  detailsData: string
  otherData: {
    key?: string
    name?: string
  }
}
const initialState: UserState = {
  listData: [],
  detailsData: '',
  otherData: {}
}

export const list = createSlice({
  name: 'list',
  // state数据的初始值
  initialState: initialState,
  reducers: {
    setList(state, action: PayloadAction<string>) {
      // 第一个参数 state 为当前state中的数据
      // 第二个参数action为{payload,type:'user/setDoctor'}
      state.listData = action.payload
    },
    setDetails(state, action: PayloadAction<string>) {
      state.detailsData = action.payload
    },
    setOtherData(state, action: PayloadAction<object>) {
      state.otherData = action.payload
    },
    resetData: () => initialState
  }
})
// 导出外部给组件调用, 也可以不导出
// export const { setDoctor, setAge } = userInfo.actions;
