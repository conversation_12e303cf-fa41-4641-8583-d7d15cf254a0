import request from '@/utils/request'

//查询科室列表
export async function queryDeptList(params) {
  return request({
    url: `/jyryRegister/parentDeptList`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION }
  })
}

//查询子科室列表
export async function queryDeptChildList(params) {
  return request({
    url: `/jyryRegister/childDeptList`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION }
  })
}

//查询医生列表
export async function queryDocList(params) {
  return request({
    url: `/jyryRegister/deptScheduleList`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION }
  })
}

//查询排班
export async function queryDeptScheduleList(params) {
  return request({
    url: `/jyryRegister/deptScheduleList`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION }
  })
}
//查询号源
export async function queryDeptIntervalList(params) {
  return request({
    url: `/jyryRegister/deptIntervalList`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION }
  })
}

//预约锁号-江阴预约挂号
export async function lockNumber(params) {
  return request({
    url: `/jyryRegister/registerSettlement`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION }
  })
}

//查询预约记录
export async function queryOrder(params) {
  return request({
    url: `/register/queryOrder`,
    method: 'POST',
    data: { ...params }
  })
}

// 取消锁号
export async function cancelLockNumber(params) {
  return request({
    url: `/jyryRegister/cancelLockNumber`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION },
    isHideLoading: true
  })
}

// 挂号预结算
export async function registerPreSettlement(params) {
  return request({
    url: `/jyryRegister/registerPreSettlement`,
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE,
      apiVersion: window.config.API_VERSION
    },
    isHideLoading: true
  })
}

// 挂号结算
export async function registerSettlement(params) {
  return request({
    url: `/qzPayment/registerSettlement`,
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE,
      apiVersion: window.config.API_VERSION
    },
    loadingText: '正在挂号中请稍候...'
  })
}

// 挂号记录查询
export async function registerRecordList(params) {
  return request({
    url: `/register/registerRecordList`,
    method: 'POST',
    data: { ...params },
    isHideLoading: true
  })
}

// 核酸挂号确认
export async function registeredCovid(params) {
  return request({
    url: `/register/registeredCovid`,
    method: 'POST',
    data: { ...params }
  })
}
