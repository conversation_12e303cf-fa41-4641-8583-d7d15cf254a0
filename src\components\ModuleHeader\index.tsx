/*
 * @Description: 页面头部
 */

import { NavBar } from 'antd-mobile'
import { useNavigate } from 'react-router-dom'
import styles from './index.module.scss'

const ModuleHeader = ({ title }) => {
  const navigate = useNavigate()
  const back = () => {
    navigate(-1)
  }
  return (
    <div className={styles['module-page-header']}>
      <NavBar back='返回' onBack={back}>
        {title || ''}
      </NavBar>
    </div>
  )
}

export default ModuleHeader
