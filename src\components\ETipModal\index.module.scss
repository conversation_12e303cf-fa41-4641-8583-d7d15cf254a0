.info-tip-modal-wrap {
  padding-top: calc(40px / $pixel);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  >img {
    width: calc(160px / $pixel);
    height: calc(160px / $pixel);
    margin-bottom: calc(50px / $pixel);
  }

  .tip-text {
    margin-top: calc(30px / $pixel);
    font-size: calc(35px / $pixel);
    color: #333333;
    text-align: center;
    line-height: calc(44px / $pixel);
    font-weight: 500;
    margin-bottom: 0;
    width: 100%;
  }
}

.finish-modal-wrap {
  .modal-title {
    width: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: calc(15px / $pixel);

    .icon {
      width: calc(56px / $pixel);
      height: calc(56px / $pixel);
      margin-right: calc(25px / $pixel);
    }

    .text {
      font-size: calc(30px / $pixel);
      color: #333333;
      line-height: calc(44px / $pixel);
      font-weight: 500;
      margin-bottom: 0;
    }
  }

  .modal-img-box {
    width: calc(500px / $pixel);
    height: calc(320px / $pixel);
    margin: 0 auto;
    background-color: #CCCCCC;

    >img {
      width: 100%;
      height: 100%;
    }
  }
}
