import LoadingContainer from './loading2'
import ReactDOM from 'react-dom/client'
let requestCount = 0
let startTime = 0

export default {
  start: function (loadingText: string) {
    if (requestCount === 0) {
      startTime = new Date().getTime()
      const dom = document.createElement('div')
      dom.setAttribute('class', 'myLoading')
      const mainBody = document.getElementById('_module')
      mainBody.appendChild(dom)
      ReactDOM.createRoot(dom).render(<LoadingContainer loadingText={loadingText} />)
    }
    requestCount++
  },
  end: function () {
    if (requestCount <= 0) return
    requestCount--
    if (requestCount === 0) {
      const endTime = new Date().getTime()
      const timer = endTime - startTime >= 100 ? 0 : 100
      setTimeout(() => {
        const load = document.getElementById('loading')
        if (load) {
          document.body.removeChild(load)
        }
        const mainBody = document.getElementById('_module')
        const dom = document.querySelector('.myLoading')
        return mainBody?.removeChild(dom)
      }, timer)
    }
  }
}
