.money-title-box {
  padding: calc(38px / $pixel) calc(50px/ $pixel);

  .item-title {
    color: #333333;
    font-size: calc(40px / $pixel);
    line-height: calc(58px / $pixel);
    margin-bottom: 0;
    font-weight: 500;

    &:first-child {
      margin-bottom: calc(46px / $pixel);
    }

    &.gray {
      color: #cccccc;
      font-size: calc(40px / $pixel);
      margin-bottom: 0;
    }
  }
}

.keyboard {
  border-top: 1px solid #e9e9e9;
  background-color: #e9e9e9;

  .keys {
    height: calc(150px / $pixel);
    background-color: #fff;
    color: #333333;
    text-align: center;
    font-weight: 500;
    font-size: calc(50px / $pixel);
    line-height: calc(145px / $pixel);

    &:active {
      background-color: #eee;
      color: #fff;
    }
  }
}

.row {
  height: 100%;

  .degenerate,
  .clean {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    text-align: center;
    font-size: calc(35px / $pixel);

    >img {
      width: calc(56px / $pixel);
      height: calc(40px / $pixel);
    }
  }

  .degenerate {
    width: 100%;
    height: 70%;
    background-color: #1890ff;

    &:active {
      background-color: #0b7de9;
      color: #fff;
    }
  }

  .clean {
    width: 100%;
    height: 30%;
    background-color: #fff;
    color: #333;
    border-left: 1px solid #e9e9e9;

    &:active {
      background-color: #eee;
      color: #fff;
    }
  }
}
