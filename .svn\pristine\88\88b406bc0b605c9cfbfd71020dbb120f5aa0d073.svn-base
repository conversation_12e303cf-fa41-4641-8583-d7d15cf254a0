import { useState, useEffect, useCallback, useRef } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { Form, Input, Button, Toast, Grid } from 'antd-mobile'
import { Table, ConfigProvider, Flex } from 'antd'
import BlueBgTitle from '@/components/BlueBgTitle'
import ModuleHeader from '@/components/ModuleHeader'
import ETipModal from '@/components/ETipModal'
import ChooseModal from '@/components/ChooseModal'
import EditTable from '@/components/EditTable'
import { getPrescription, inputCharge } from '@/services/hospital'
import { getSpeak } from '@/services/hardware'
import { RootState } from '@/store/index'
import { filterArray, IdNoPrivate } from '@/utils/utils'
import styles from './index.module.scss'
import gotoMenu from '@/utils/gotoMenu'

/**
 * 费用录入
 * @param fields
 */
const handleInputCharge = async (fields: any) => {
  console.log('诊疗项目录入fields', fields)
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        data: '',
        msg: '成功'
      }
    } else {
      response = await inputCharge({
        ...fields
      })
    }
    if (response.code === '0') {
      return response
    }
    return response
  } catch (error) {
    return false
  }
}

const Diagnosis = () => {
  const childRef = useRef<any>(null)
  const [form] = Form.useForm()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const sigCanvas = useRef<any>()
  const [checkDrugItems, setCheckDrugItems] = useState<any[]>([]) //选中用药/修改表格数据
  const [dataSource, setDataSource] = useState<any[]>([]) //修改表格数据
  const [selectTypes, setSelectTypes] = useState<any[string]>('') //弹出框数据类型
  const [multipleModalVisible, setMultipleModalVisible] = useState(false) //弹出框
  const { readCardData, currentUser, loginDocData }: any = useSelector(
    (state: RootState) => state.userInfo
  )

  type EditableTableProps = Parameters<typeof Table>[0]
  type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>
  // 药品名称 规格 厂家 单位
  const tableColumns: (ColumnTypes[number] & {
    editable?: boolean
    dataIndex: string
  })[] = [
    {
      title: '项目名称',
      dataIndex: 'name',
      width: 120
    },
    {
      title: '单价',
      dataIndex: 'price',
      editable: false, //是否允许修改
      width: 80
    },
    {
      title: '数量/单位',
      dataIndex: 'num', //DW单位
      editable: true, //是否允许修改
      width: 90,
      render: (_, record: any, index) => {
        return (
          <>
            <div>{`${record?.num}/${record?.unit}`}</div>
          </>
        )
      }
    }
  ]


  // 表单提交失败的方法
  const onFinishFailed = (errorInfo: any) => {
    Toast.show({
      content: errorInfo.errorFields[0].errors[0],
      afterClose: () => {
        console.log('after')
      }
    })
  }

  //选中表格
  const onSelectAll = (val: any) => {
    console.log('onSelectAll', val)
    // if (val?.length > 0) {
    //   form.setFieldsValue({ prescription: '*' })
    // } else {
    //   form.setFieldsValue({ prescription: '' })
    // }
    setCheckDrugItems(val)
  }

  // 删除选中的处方信息
  const onDelete = () => {
    //对比选中删除选中的数据
    const result = filterArray(dataSource, checkDrugItems, 'code')
    setCheckDrugItems([]) //置空存储的数据
    setDataSource([...result]) //更新表格数据
    childRef.current.clearSelection() // 清空选中项
    if (result.length === 0) {
      form.setFieldsValue({ prescription: '' })
    }
  }


  // 表单提交
  const onFinish = async (values: any) => {
    console.log('values', dataSource)
    const prescription = dataSource.map((item: any) => {
      return {
        xh: item.code,
        dj: item.price,
        sl: item.num,
        tcpb: item.meal,
      }
    })
    console.log(prescription)
    const res = await handleInputCharge({
      expenseEntries: prescription,
      patientID: currentUser.patientID,
      saID: window.config.SAID,
      yonghuID: loginDocData.YONGHUID
    })
    if(!res) return 
    if(res.code === '0'){
      ETipModal('费用录入成功', 'success', ()=>{
        gotoMenu(navigate)
      })
    }else{
      ETipModal(res.msg, 'error')
    }
  }

  useEffect(() => {
    if (window.config.isSpeak) getSpeak({ content: '请选择诊疗项目' })
  }, [])

  
  return (
    <div className={styles.container}>
      <ModuleHeader title='费用录入' />
      <div className={[styles['wrapper'], styles['scroll-style']].join(' ')}>
        <div className={styles.confirm}>
          <>
            <BlueBgTitle title={'住院信息'} />
            <div className={styles['line-wrapper']}>
              <div className={`${styles['circle-box']} ${styles.left}`}></div>
              <div className={styles.line}></div>
              <div className={`${styles['circle-box']} ${styles.right}`}></div>
            </div>
            <div className={styles['title-wrapper']}>
              <div className={styles.title}>
                患者姓名
                <i></i>
              </div>
              <span>{currentUser?.patientName}</span>
            </div>
            <div className={styles['title-wrapper']}>
              <div className={styles.title}>
                性别
                <i></i>
              </div>
              <span className={styles['black-span']}>
                {['', '男', '女', '其他'][currentUser?.patientGender]}
              </span>
            </div>
            <div className={styles['title-wrapper']}>
              <div className={styles.title}>
                证件号
                <i></i>
              </div>
              <span className={styles['black-span']}>{IdNoPrivate(currentUser.idCard)}</span>
            </div>
            <div className={styles['title-wrapper']}>
              <div className={styles.title}>
                病案号
                <i></i>
              </div>
              <span className={styles['black-span']}>{currentUser?.patientID}</span>
            </div>
          </>
          <Form
            name='form'
            form={form}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            footer={
              <Button block type='submit' color='primary' size='middle' shape='rounded'>
                提交
              </Button>
            }
          >
            <Form.Item
              name='prescription'
              label='诊疗项目'
              rules={[{ required: true, message: '请选择诊疗项目' }]}
              onClick={event => {
                event.stopPropagation()
                event.preventDefault() //阻止表单的默认提交行为
                setSelectTypes('cfxx')
                setMultipleModalVisible(true)
              }}
            >
              {!dataSource.length && <Input placeholder='请选择诊疗项目' readOnly clearable />}
            </Form.Item>
            {dataSource.length > 0 && (
              <ConfigProvider
                theme={{
                  token: {
                    colorText: '#333',
                    fontSize: 14,
                    lineWidthBold: 1
                  },
                  components: {
                    Table: {
                      cellFontSize: 12,
                      headerBg: '#FFFFFF',
                      borderColor: '#FFFFFF',
                      headerSplitColor: '#FFFFFF'
                    }
                  }
                }}
              >
                <EditTable
                  height='470px'
                  type='checkbox' //多选
                  emptyText='请选择诊疗项目'
                  ref={childRef}
                  isStepper={true} //是否是进步器
                  dataSource={dataSource}
                  tableColumns={tableColumns}
                  setDataSource={setDataSource} //修改表格数据
                  onSelectAll={onSelectAll}
                />
                <Grid columns={2} gap={20} style={{ padding: '0 6vw', margin: '15px 0 25px' }}>
                  <Grid.Item>
                    <div className={styles['grid-demo-item-block']}>
                      <Button
                        block
                        size='small'
                        color='primary'
                        fill='solid'
                        onClick={(event: any) => {
                          event.stopPropagation()
                          event.preventDefault() //阻止表单的默认提交行为
                          setSelectTypes('cfxx')
                          setMultipleModalVisible(true)
                        }}
                      >
                        新增
                      </Button>
                    </div>
                  </Grid.Item>
                  <Grid.Item>
                    <div className={styles['grid-demo-item-block']}>
                      <Button color='primary' block size='small' fill='outline' onClick={onDelete}>
                        删除
                      </Button>
                    </div>
                  </Grid.Item>
                </Grid>
              </ConfigProvider>
            )}
          </Form>
        </div>
      </div>
      
      <ChooseModal
        visibleListPopup={multipleModalVisible}
        onCancel={() => {
          setMultipleModalVisible(false)
        }}
        onConfirm={(val: any) => {
          console.log('val', val)
          setMultipleModalVisible(false)
          // 处方信息
          if (selectTypes === 'cfxx') {
            const _arr = [...dataSource, ...val]
            // 数组去重
            const map = new Map()
            const _nwearr = _arr.filter((v: any) => !map.has(v.code) && map.set(v.code, v))
            setDataSource(_nwearr)
            setCheckDrugItems(_nwearr)
            if(_nwearr.length > 0){
              form.setFieldsValue({ prescription: '*' })
            }
          }
        }}
        selectType = {'cfxx'}  // 选择的类型
        isSearch={true} //是否显示搜索框
        isMultiple= {true} // 支持多选
      ></ChooseModal>
    </div>
  )
}

export default Diagnosis
