// 防抖封装函数
import { useRef, useCallback, useEffect } from "react";

function useDebounce(fn: any, delay: any) {
  const delayRef = useRef<any>(delay);
  const fnRef = useRef<any>(fn);
  fnRef.current = fn;
  const debounced = useRef<any>();
  const cancel = useCallback(() => {
    if (debounced.current) {
      clearTimeout(debounced.current);
    }
  }, []);

  useEffect(() => {
    cancel();
  }, [cancel]);

  const debouncedFn = useCallback(
    (...args: any) => {
      cancel();
      debounced.current = setTimeout(() => {
        fnRef.current(...args);
      }, delayRef.current);
    },
    [cancel]
  );

  return debouncedFn;
}

export default useDebounce;
