import { useState, useEffect } from 'react'
import { SearchBar, SideBar, List } from 'antd-mobile'
import { Flex } from 'antd'
import ModuleHeader from '@/components/ModuleHeader'
import { queryDeptList } from '@/services/register'
import { getSpeak } from '@/services/hardware'
import { useNavigate } from 'react-router-dom'
import { RootState } from '@/store'
import { useDispatch, useSelector } from 'react-redux'
import { group } from '@/utils/utils'
import styles from './index.module.scss'

/**
 * 查询排班科室
 * @param fields
 */
const handleQuery = async fields => {
  try {
    let response
    if (window.config.isDebug) {
      response = {
        msg: '请求成功！',
        code: '0',
        data: [
          {
            deptName: '儿科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"儿科","deptID":"0700","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0700',
            surplusNum: '',
            list: [],
            deptRemark:
              '2003年初，儿科急诊与内科总诊分开，这在同级医院中是第一家。科内拥有双磁蓝光治疗仪，小儿美国INVIVO心电呼吸监护仪，新生儿保暖培养箱，远红外抢救台，经皮黄疸测定仪，小儿微泵等一大批先进医疗仪器，能收治儿科各种常见重危病人，并与复旦大学儿科建立双向互诊关系。儿科病区内有高级病房和普通病房，拥有中央空调、中心吸引与中心供氧等先进系统，并设有儿科活动室。近年来科内医师积极总结临床经验，在省内外《实用临床儿科杂志》、《临床儿科杂志》等刊物上发表十余篇论文。',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '333-345室',
            isFatherNode: '',
            parentDeptName: '儿科',
            deptAdvantage:
              '我院儿科成立于1983年，是温岭东部地区及路桥一带的儿科医疗中心，是医院成长最快科室之一            ',
            parentDeptID: '14',
            registeredAmount: ''
          },
          {
            deptName: '中医科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"中医科","deptID":"5000","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '5000',
            surplusNum: '',
            list: [],
            deptRemark:
              '以中医理论为指导，辨证施治，应用中医扶正攻毒法，治疗肺癌、鼻咽癌、恶性淋巴瘤、以及食管、胃、肝、肠、胰腺、脑、乳腺、甲状腺、肾、前列腺、膀胱、子宫、卵巢等各类良恶性肿瘤，和各类肿瘤术后、放化疗后、介入治疗后的辅助治疗。能起到控制病情，减少复发，延长生存期，改善生活质量及减少放化疗毒副作用的疗效。 ',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '二楼中医馆',
            isFatherNode: '',
            parentDeptName: '中医科',
            deptAdvantage: '于2007年建立温岭市首个中医肿瘤科。',
            parentDeptID: '15',
            registeredAmount: ''
          },
          {
            deptName: '感染内科(肝病科)',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"感染内科(肝病科)","deptID":"1200","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1200',
            surplusNum: '',
            list: [],
            deptRemark:
              '我科在扎实的普内科基础上，擅长心内、呼吸等系统疾病的诊治，并在重危病人的抢救成功率方面有较高的水平，尤其在心脑血管意外、各种原因所致的呼吸衰竭等病例方面积累了丰富的临床经验，并已开展如起博器安装等心血管介入治疗。',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '231室',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '我院内科目前拥有医护人员近20人，配有国内先进设备',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '眼科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"眼科","deptID":"1300002","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1300002',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '322-323室',
            isFatherNode: '',
            parentDeptName: '眼科',
            deptAdvantage: '',
            parentDeptID: '19',
            registeredAmount: ''
          },
          {
            deptName: '疼痛门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"疼痛门诊","deptID":"0010001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0010001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '344室',
            isFatherNode: '',
            parentDeptName: '麻醉科',
            deptAdvantage: '',
            parentDeptID: '21',
            registeredAmount: ''
          },
          {
            deptName: '骨科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"骨科","deptID":"0800","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0800',
            surplusNum: '',
            list: [],
            deptRemark:
              '开展业务范围较广，包括脊柱、骨盆、四肢、关节骨病，主要开展：四肢创伤、骨折、脱位的各类手术，包括普通髓肉钉、重建髓肉钉固定、近关节骨折的解剖型钢板、锁定钢板内固定；脊柱创伤骨折的椎弓根螺钉系列内固定，如国产的RF、AF，脊柱通用系统（GSSI-IV型），进口的有AO系列、枢法膜等；人工关节置换术可治疗老年人髋部疾病骨折，行人工股骨头置换术的老人中，年龄最大的有90岁，极大地提高了老年患者的生活质量。                 ',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '111--112室',
            isFatherNode: '',
            parentDeptName: '外科系列',
            deptAdvantage:
              '我院骨科有医生8人，与浙二院、邵逸夫医院、温州医学院附属二院、台州医院、台州中心医院建立技术合作关系',
            parentDeptID: '9',
            registeredAmount: ''
          },
          {
            deptName: '皮肤科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"皮肤科","deptID":"1400","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1400',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '318-319室',
            isFatherNode: '',
            parentDeptName: '皮肤科',
            deptAdvantage: '中西医结合皮肤病、性病防治',
            parentDeptID: '18',
            registeredAmount: ''
          },
          {
            deptName: '口腔科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"口腔科","deptID":"1300003","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1300003',
            surplusNum: '',
            list: [],
            deptRemark:
              '科室拥有全自动电脑综合治疗台、超声波洁牙机、光固化治疗仪及全套德国技工生产设备等高档医疗设施',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '338室',
            isFatherNode: '',
            parentDeptName: '口腔科',
            deptAdvantage: '我院口腔科是一个拥有二十多年历史的科室。',
            parentDeptID: '20',
            registeredAmount: ''
          },
          {
            deptName: 'PICC',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"PICC","deptID":"100001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '100001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '一楼输液室',
            isFatherNode: '',
            parentDeptName: '护理门诊',
            deptAdvantage: '',
            parentDeptID: '12',
            registeredAmount: ''
          },
          {
            deptName: '耳鼻咽喉科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"耳鼻咽喉科","deptID":"1300001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1300001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '320-321室',
            isFatherNode: '',
            parentDeptName: '耳鼻咽喉科',
            deptAdvantage: '',
            parentDeptID: '17',
            registeredAmount: ''
          },
          {
            deptName: '妇产妇瘤科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"妇产妇瘤科","deptID":"0500","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0500',
            surplusNum: '',
            list: [],
            deptRemark:
              '科室目前拥有医护人员26人，其中副主任医师2名，主治医师4名，主管护师5名，医师4名，护师4名，各位医师曾先后到浙江大学医学院附属妇产科医院、上海国际妇幼保健院等专科医院进修深造。科室综合实力强大，能开展遗传咨询、产前诊断、优生优育检测、高危妊娠筛查及处理、各种术式的剖宫产、处理各种原因引起的难产，重危疑难病例抢救成功率100%，并开展无痛分娩、新生儿游泳等项目。我院1995年被评为温岭市首家“全国爱婴医院”，实行母婴同室，母乳喂养。 ',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '214--226室',
            isFatherNode: '',
            parentDeptName: '妇产科',
            deptAdvantage:
              '妇科是我院久负盛名的医护团体，我科拥有精湛的医疗技术，一流的医疗设施，和谐温馨的医护环境。    ',
            parentDeptID: '16',
            registeredAmount: ''
          },
          {
            deptName: '乳腺外科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"乳腺外科","deptID":"0931","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0931',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '特需门诊',
            deptAdvantage: '',
            parentDeptID: '23',
            registeredAmount: ''
          },
          {
            deptName: '超声科门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"超声科门诊","deptID":"700101","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '700101',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '门诊二楼',
            isFatherNode: '',
            parentDeptName: '医技门诊',
            deptAdvantage: '',
            parentDeptID: '22',
            registeredAmount: ''
          },
          {
            deptName: '肺结节一体化门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肺结节一体化门诊","deptID":"09000014","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '09000014',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '联合门诊',
            deptAdvantage: '',
            parentDeptID: '24',
            registeredAmount: ''
          },
          {
            deptName: '胸部放疗科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"胸部放疗科","deptID":"1101","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1101',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '甲状腺微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"甲状腺微创介入门诊","deptID":"1900002","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900002',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '中医肿瘤',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"中医肿瘤","deptID":"5000002","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '5000002',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '二楼中医馆',
            isFatherNode: '',
            parentDeptName: '中医科',
            deptAdvantage: '',
            parentDeptID: '15',
            registeredAmount: ''
          },
          {
            deptName: '儿科+生长发育',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"儿科+生长发育","deptID":"0700002","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0700002',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '333室',
            isFatherNode: '',
            parentDeptName: '儿科',
            deptAdvantage: '',
            parentDeptID: '14',
            registeredAmount: ''
          },
          {
            deptName: '淋巴水肿治疗门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"淋巴水肿治疗门诊","deptID":"0902002","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0902002',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '护理门诊',
            deptAdvantage: '',
            parentDeptID: '12',
            registeredAmount: ''
          },
          {
            deptName: '头颈外科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"头颈外科","deptID":"0928","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0928',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '特需门诊',
            deptAdvantage: '',
            parentDeptID: '23',
            registeredAmount: ''
          },
          {
            deptName: '骨软组织皮肤外科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"骨软组织皮肤外科","deptID":"0802","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0802',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '外科系列',
            deptAdvantage: '',
            parentDeptID: '9',
            registeredAmount: ''
          },
          {
            deptName: '介入微创外科综合门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"介入微创外科综合门诊","deptID":"1900010","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900010',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '胸部肿瘤门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"胸部肿瘤门诊","deptID":"1107","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1107',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '肿瘤妇科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肿瘤妇科","deptID":"0500001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0500001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '214--226室',
            isFatherNode: '',
            parentDeptName: '妇产科',
            deptAdvantage: '',
            parentDeptID: '16',
            registeredAmount: ''
          },
          {
            deptName: '放射科门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"放射科门诊","deptID":"800101","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '800101',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '门诊四楼',
            isFatherNode: '',
            parentDeptName: '医技门诊',
            deptAdvantage: '',
            parentDeptID: '22',
            registeredAmount: ''
          },
          {
            deptName: '风湿免疫科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"风湿免疫科","deptID":"0301008","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0301008',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '胸外科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"胸外科","deptID":"0929","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0929',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '特需门诊',
            deptAdvantage: '',
            parentDeptID: '23',
            registeredAmount: ''
          },
          {
            deptName: '肛肠外科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肛肠外科","deptID":"0600001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0600001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '114室',
            isFatherNode: '',
            parentDeptName: '外科系列',
            deptAdvantage: '',
            parentDeptID: '9',
            registeredAmount: ''
          },
          {
            deptName: '药学疼痛门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"药学疼痛门诊","deptID":"990001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '990001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '医技门诊',
            deptAdvantage: '',
            parentDeptID: '22',
            registeredAmount: ''
          },
          {
            deptName: '病理科门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"病理科门诊","deptID":"900101","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '900101',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '门诊三楼',
            isFatherNode: '',
            parentDeptName: '医技门诊',
            deptAdvantage: '',
            parentDeptID: '22',
            registeredAmount: ''
          },
          {
            deptName: '针推科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"针推科","deptID":"0800003","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0800003',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '121室',
            isFatherNode: '',
            parentDeptName: '中医科',
            deptAdvantage: '',
            parentDeptID: '15',
            registeredAmount: ''
          },
          {
            deptName: '腹部放疗科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"腹部放疗科","deptID":"1103","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1103',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '肺结节微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肺结节微创介入门诊","deptID":"1900011","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900011',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '脑卒中康复护理门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"脑卒中康复护理门诊","deptID":"00032","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '00032',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '护理门诊',
            deptAdvantage: '',
            parentDeptID: '12',
            registeredAmount: ''
          },
          {
            deptName: '儿科+儿科呼吸门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"儿科+儿科呼吸门诊","deptID":"0700005","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0700005',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '儿科',
            deptAdvantage: '',
            parentDeptID: '14',
            registeredAmount: ''
          },
          {
            deptName: '内分泌科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"内分泌科","deptID":"030004","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '030004',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '233室',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '神经内科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"神经内科","deptID":"0301003","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0301003',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '233室',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '科室配有国内先进的仪器，扎实的内科基础和丰富的临床经验。',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '头颈、妇瘤放疗科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"头颈、妇瘤放疗科","deptID":"1105","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1105',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '肝胆胰微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肝胆胰微创介入门诊","deptID":"1900001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '生长发育',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"生长发育","deptID":"0700004","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0700004',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '儿科',
            deptAdvantage: '',
            parentDeptID: '14',
            registeredAmount: ''
          },
          {
            deptName: '药学营养门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"药学营养门诊","deptID":"990002","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '990002',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '医技门诊',
            deptAdvantage: '',
            parentDeptID: '22',
            registeredAmount: ''
          },
          {
            deptName: '腹部外科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"腹部外科","deptID":"0930","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0930',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '特需门诊',
            deptAdvantage: '',
            parentDeptID: '23',
            registeredAmount: ''
          },
          {
            deptName: '糖尿病教育门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"糖尿病教育门诊","deptID":"0301007","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0301007',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '护理门诊',
            deptAdvantage: '',
            parentDeptID: '12',
            registeredAmount: ''
          },
          {
            deptName: '泌尿外科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"泌尿外科","deptID":"0600002","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0600002',
            surplusNum: '',
            list: [],
            deptRemark:
              '泌尿外科开展了经尿道、前列腺切除（TURP）、膀胱切除（TURBt）、尿道狭窄治疗、复杂肾结石手术治疗、肾癌、输尿管癌、膀胱癌根治术，其中膀胱全切，原位回肠新膀胱术在温岭市处于领先。',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '116室',
            isFatherNode: '',
            parentDeptName: '外科系列',
            deptAdvantage:
              '泌尿外科开展了经尿道、前列腺切除（TURP）、膀胱切除（TURBt）、尿道狭窄治疗、复杂肾结石手术治疗、肾癌',
            parentDeptID: '9',
            registeredAmount: ''
          },
          {
            deptName: '肿瘤内科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肿瘤内科","deptID":"1000","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1000',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '316室',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '肿瘤化疗内科',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '乳腺微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"乳腺微创介入门诊","deptID":"1900003","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900003',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '呼吸内科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"呼吸内科","deptID":"030001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '030001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '231-232室',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '伤口造口门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"伤口造口门诊","deptID":"0900012","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0900012',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '护理门诊',
            deptAdvantage: '',
            parentDeptID: '12',
            registeredAmount: ''
          },
          {
            deptName: '神经微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"神经微创介入门诊","deptID":"1900016","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900016',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '普外科、肝胆胰微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"普外科、肝胆胰微创介入门诊","deptID":"0600","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0600',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '115室',
            isFatherNode: '',
            parentDeptName: '外科系列',
            deptAdvantage: '',
            parentDeptID: '9',
            registeredAmount: ''
          },
          {
            deptName: '心血管内科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"心血管内科","deptID":"0301001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0301001',
            surplusNum: '',
            list: [],
            deptRemark:
              '我科在扎实的普内科基础上，擅长心内、呼吸等系统疾病的诊治，并在重危病人的抢救成功率方面有较高的水平，尤其在心脑血管意外、各种原因所致的呼吸衰竭等病例方面积累了丰富的临床经验，并已开展如起博器安装等心血管介入治疗。',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '232室',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '我院内科目前拥有医护人员近20人，其中主任医师1名，配有国内先进的设备。',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '乳腺癌、淋巴瘤专科门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"乳腺癌、淋巴瘤专科门诊","deptID":"0900013","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0900013',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '神经外科(脑外科)',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"神经外科(脑外科)","deptID":"00031","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '00031',
            surplusNum: '',
            list: [],
            deptRemark:
              '长期与上海长征医院、浙一医院、浙二医院、台州市中心医院建立良好的技术合作关系，成立“浙江大学附属第二医院神经外科温岭基地”，他们会定期派专家来我院进行查房、技术指导及手术，对我院无条件开展的手术可介绍入住浙二医院并优先手术。自最初的脑外伤开颅手术，现已逐渐开展高血压脑出血(中风）、脑血管畸形出血、颅内良恶性肿瘤等手术，并开展了脑积水V-P分流术（脑室-腹腔分流术）、颅骨缺损修补术。最近开展的?“软通道”微创介入技术治疗高血压性脑出血及恶性肿瘤晚期脑转移化疗泵植入术为台州市领先。',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '113室',
            isFatherNode: '',
            parentDeptName: '外科系列',
            deptAdvantage:
              '本院开展脑外科已有20余年的历史，是继温岭市第一人民医院后在温岭首先开展的。',
            parentDeptID: '9',
            registeredAmount: ''
          },
          {
            deptName: '中西医结合科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"中西医结合科","deptID":"130701","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '130701',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '335室',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '甲亢微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"甲亢微创介入门诊","deptID":"1900015","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900015',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '消化内科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"消化内科","deptID":"030002","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '030002',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '232-233室',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '失眠介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"失眠介入门诊","deptID":"1900013","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900013',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '普内科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"普内科","deptID":"0302","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0302',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '234--235室',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '甲乳外科、甲状腺微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"甲乳外科、甲状腺微创介入门诊","deptID":"09000011","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '09000011',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '一楼121',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '外科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"外科","deptID":"060001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '060001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '132室',
            isFatherNode: '',
            parentDeptName: '外科系列',
            deptAdvantage: '',
            parentDeptID: '9',
            registeredAmount: ''
          },
          {
            deptName: '疼痛微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"疼痛微创介入门诊","deptID":"1900012","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900012',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '戒烟门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"戒烟门诊","deptID":"030006","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '030006',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '317室',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '甲乳外科、乳腺微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"甲乳外科、乳腺微创介入门诊","deptID":"0900001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0900001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '一楼121',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '前列腺微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"前列腺微创介入门诊","deptID":"1900006","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900006',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '腹部外科、胃肠微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"腹部外科、胃肠微创介入门诊","deptID":"0900002","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0900002',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '三楼313',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '肠道门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肠道门诊","deptID":"1200002","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1200002',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '裙楼一楼',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '乳腺外科、乳腺微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"乳腺外科、乳腺微创介入门诊","deptID":"0900005","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0900005',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '一楼121',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '静脉曲张微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"静脉曲张微创介入门诊","deptID":"1900009","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900009',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '颈肩腰腿痛微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"颈肩腰腿痛微创介入门诊","deptID":"1900014","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900014',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '头颈外科、甲状腺微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"头颈外科、甲状腺微创介入门诊","deptID":"0900004","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0900004',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '一楼118-2',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '肌肉骨骼微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肌肉骨骼微创介入门诊","deptID":"1900008","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900008',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '胸外科、肺结节微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"胸外科、肺结节微创介入门诊","deptID":"0900003","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0900003',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '315室',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '心理卫生科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"心理卫生科","deptID":"1300004","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1300004',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '317室',
            isFatherNode: '',
            parentDeptName: '内科系列',
            deptAdvantage: '',
            parentDeptID: '8',
            registeredAmount: ''
          },
          {
            deptName: '血管微创介入门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"血管微创介入门诊","deptID":"1900004","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1900004',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '介入微创',
            deptAdvantage: '',
            parentDeptID: '13',
            registeredAmount: ''
          },
          {
            deptName: '肿瘤方便门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肿瘤方便门诊","deptID":"5402","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '5402',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '三楼322',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '胃肠肿瘤早期筛查咨询门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"胃肠肿瘤早期筛查咨询门诊","deptID":"0933","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0933',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '临床营养科',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"临床营养科","deptID":"0902001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0902001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '三楼311/313',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '肝肿瘤门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肝肿瘤门诊","deptID":"1106","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1106',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '骨质疏松门诊',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"骨质疏松门诊","deptID":"0800001","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '0800001',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '外科系列',
            deptAdvantage: '',
            parentDeptID: '9',
            registeredAmount: ''
          },
          {
            deptName: '呼吸护理',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"呼吸护理","deptID":"1200005","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1200005',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '护理门诊',
            deptAdvantage: '',
            parentDeptID: '12',
            registeredAmount: ''
          },
          {
            deptName: '肺癌内科治疗',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"肺癌内科治疗","deptID":"1109","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '1109',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '肿瘤系列',
            deptAdvantage: '',
            parentDeptID: '10',
            registeredAmount: ''
          },
          {
            deptName: '两癌筛查',
            apiPar:
              '{"zhuyuanbz": "1","menzhenbz":"1","shanchubz":"0","deptName":"两癌筛查","deptID":"20181","jizhenbz":"1"}\n',
            deptPY: '',
            deptID: '20181',
            surplusNum: '',
            list: [],
            deptRemark: '',
            isSpecial: '',
            subscribedNum: '',
            deptAddress: '',
            isFatherNode: '',
            parentDeptName: '外科系列',
            deptAdvantage: '',
            parentDeptID: '9',
            registeredAmount: ''
          }
        ]
      }
    } else {
      response = await queryDeptList({
        ...fields
      })
    }
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

// 父级科室列表-假数据
export const tabs = [
  {
    key: '内科系列',
    title: '内科系列'
  },
  {
    key: '外科系列',
    title: '外科系列'
  },
  {
    key: '肿瘤系列',
    title: '肿瘤系列'
  },
  {
    key: '儿科',
    title: '儿科'
  },
  {
    key: '中医科',
    title: '中医科'
  },
  {
    key: '妇产科',
    title: '妇产科'
  },
  {
    key: '耳鼻咽喉科',
    title: '耳鼻咽喉科'
  },
  {
    key: '皮肤科',
    title: '皮肤科'
  },
  {
    key: '眼科',
    title: '眼科'
  },
  {
    key: '口腔科',
    title: '口腔科'
  },
  {
    key: '麻醉科',
    title: '麻醉科'
  },
  {
    key: '护理门诊',
    title: '护理门诊'
  },
  {
    key: '医技门诊',
    title: '医技门诊'
  }
]

const RoomPage = () => {
  const { currentUser } = useSelector((state: RootState) => state.userInfo)
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const [deptList, setDepList] = useState([])
  const [fatherDept, setFatherDept] = useState<string>('')
  const [groupDepList, setGroupDepList] = useState([])

  // 请求数据
  const queryLists = async () => {
    const fields = {
      deviceCode: window.config.DEVICE_CODE,
      apiPar: currentUser.apiPar,
      patientID: currentUser.patientID
    }
    const res = await handleQuery(fields)
    const arr = res && res.data

    const groupArr = group(arr, 'parentDeptName')
    setGroupDepList(groupArr)
    setFatherDept(tabs[0].key)
    setDepList(groupArr.find(item => item.key === tabs[0].key).data)
  }

  const handleSelectFatherDept = value => {
    setFatherDept(value)
    setDepList(groupDepList.find(item => item.key === value).data)
  }

  const goNext = item => {
    dispatch({
      type: 'register/setDept',
      payload: item
    })
    navigate(`/register/doctorlist`)
  }

  useEffect(() => {
    queryLists()
    window.config.isSpeak && getSpeak({ content: '请选择科室' })
  }, [])

  return (
    <>
      <ModuleHeader title={'选择科室'} />
      <div className={styles['room-wrapper']}>
        <div className={styles['search-wrapper']}>
          <SearchBar
            placeholder='请输入科室名称进行搜索'
            showCancelButton
            style={{
              '--border-radius': '100px',
              '--background': '#ffffff',
              '--height': '32px',
              '--padding-left': '12px'
            }}
          />
        </div>
        <div className={styles.container}>
          <div className={styles.side}>
            <SideBar activeKey={fatherDept} onChange={handleSelectFatherDept}>
              {tabs.map(item => (
                <SideBar.Item key={item.key} title={item.title} />
              ))}
            </SideBar>
          </div>
          <div className={styles.main}>
            <div className={styles.content}>
              <List
                style={{
                  '--border-top': 'none',
                  '--font-size': '15px',
                  '--padding-left': '20px'
                }}
                className={[styles['info-list'], styles['scroll-style']].join(' ')}
              >
                {deptList.map((item, index) => (
                  <List.Item
                    key={index}
                    onClick={() => {
                      goNext(item)
                    }}
                  >
                    {item.deptName}
                  </List.Item>
                ))}
              </List>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default RoomPage
