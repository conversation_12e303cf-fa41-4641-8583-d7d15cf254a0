import { Row, Col, Space } from 'antd'
import { useState, useRef, useEffect } from 'react'
import { Input, Button } from 'antd'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import { getSpeak } from '@/services/hardware'
import dayjs from 'dayjs'
import ModuleHeader from '@/components/ModuleHeader'
import PayMode from '@/components/PayMode'
import BlueBgTitle from '@/components/BlueBgTitle'
import NumKeyboardDrawer from '@/components/NumKeyboardDrawer'
// import loading from '@/utils/loading'
import styles from './index.module.scss'

const Account = () => {
  const { currentUser } = useSelector((state: RootState) => state.userInfo)
  const [numPickerVisible, setNumPickerVisible] = useState<boolean>(false)
  const [money, setMoney] = useState(null)
  const inputRef = useRef(null)
  const handleCloseNumPickerDrawer = () => {
    setNumPickerVisible(false)
  }
  const handleConfirmNumPicker = data => {
    console.log(data)
    inputRef.current.focus()
    setMoney(data)
    setNumPickerVisible(false)
  }

  useEffect(() => {
    window.config.isSpeak && getSpeak({ content: '请输入要充值的金额并支付' })
    // loading.start('正在打印小票，请稍候...')
  }, [])

  return (
    <>
      <ModuleHeader title={'住院充值'} />
      <div className={styles['confirm-page-wrapper']}>
        <Row gutter={[0, 6]}>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
              <div className={styles['info-box']}>
                <BlueBgTitle title={'住院信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>姓名：</span>
                  {currentUser.patientName}
                </div>
                <div className={styles['item']}>
                  <span>住院号：</span>
                  {currentUser.inHospitalID}
                </div>
                <div className={styles['item']}>
                  <span>住院科室：</span>5楼心内科 23床
                </div>
                <div className={styles['item']}>
                  <span>入院日期：</span>
                  {dayjs().subtract(5, 'day').format('YYYY-MM-DD')}
                </div>
              </div>
              <div className={styles['detail-list']}>
                <BlueBgTitle title={'费用详情'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>费用总额：</span>6098.76
                </div>
                <div className={styles['item']}>
                  <span>预交款：</span>2000.00
                </div>
                <div className={styles['item']}>
                  <div className={styles['input-money']}>
                    <span>充值金额：</span>
                    <Space.Compact>
                      <Input prefix='￥' suffix='元' readOnly value={money} ref={inputRef}></Input>
                      <Button type='primary' onClick={() => setNumPickerVisible(true)}>
                        输入
                      </Button>
                    </Space.Compact>
                  </div>
                </div>
              </div>
            </div>
          </Col>
          <Col span={24}>
            <PayMode
              PAYMENT_TYPE={'recharge'}
              SUCCESS_MODAL_TITLE={'充值成功，请取走凭条'}
              money={money}
              payParams={{
                patientName: '张三'
              }}
            />
          </Col>
        </Row>
      </div>
      {numPickerVisible && (
        <NumKeyboardDrawer
          visible={numPickerVisible}
          title={'充值金额'}
          onCancel={handleCloseNumPickerDrawer}
          onConfirm={handleConfirmNumPicker}
          type='money'
          value={money}
        />
      )}
    </>
  )
}

export default Account
