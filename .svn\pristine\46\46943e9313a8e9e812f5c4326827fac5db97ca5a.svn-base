/*
 * @Description:
    选择弹窗
 */
import { useState, memo } from "react";
import { <PERSON><PERSON>, <PERSON>rid, <PERSON>up, CheckList, SearchBar, InfiniteScroll } from "antd-mobile";
import PageButtonNum from "@/components/PageButtonNum";
import styles from "./index.module.scss";

// 对比数组返回共有的数据
const getCommonElements = (arr1: any, arr2: any) =>
  [...new Set(arr1)].filter((item: any) => arr2.includes(item.value));

interface Props {
  visibleListPopup?: boolean;
  onCancel?: () => any;
  onConfirm?: (val: any) => any;
  onSearch?: (val: any) => any;
  onClear?: (val: any) => any;
  onSearchCancel?: () => any;
  searchValue?: any;
  title?: string;
  vipTypes?: any;
  isSearch?: boolean;
  isMultiple?: boolean;
  isPageBtn?: boolean;
  isLoadMore?: boolean;
  loadMore?: () => any;
  hasMore?: boolean;
}

const ChoiceModal = memo<Props>((props: any) => {
  const [checkItem, setCheckItem] = useState<any[string]>([]);
  return (
    <Popup
      visible={props?.visibleListPopup}
      bodyStyle={{
        borderTopRightRadius: "8px",
        borderTopLeftRadius: "8px",
      }}
      onMaskClick={() => props?.onCancel()}
      destroyOnClose={true}
    >
      <div className={styles.title}>{props?.title}</div>
      <Grid columns={2} gap={12} style={{ padding: "10px" }}>
        <Grid.Item style={{ textAlign: "left" }}>
          <Button
            fill="solid"
            size="middle"
            onClick={() => { props?.onCancel() }}
          >
            取消
          </Button>
        </Grid.Item>
        <Grid.Item style={{ textAlign: "right" }}>
          <Button
            color="primary"
            fill="solid"
            size="middle"
            onClick={() => {
              const commonElements = getCommonElements(
                props?.vipTypes,
                checkItem
              );
              props?.onConfirm(commonElements);
              // setCheckItem([]); //清空选中项
            }}
          >
            确认
          </Button>
        </Grid.Item>
      </Grid>
      {props?.isSearch && (
        <div style={{ padding: "0 5px 10px" }}>
          <SearchBar
            placeholder="请搜索"
            clearable
            showCancelButton
            style={{
              "--border-radius": "100px",
              "--background": "#f2f2f2",
              "--height": "32px",
              "--padding-left": "12px",
            }}
            onSearch={(val: any) => props?.onSearch(val)}
            onClear={() => props?.onClear()}
            onCancel={() => props?.onSearchCancel()}
          />
        </div>
      )}
      <div className={styles.check}>
        <CheckList
          multiple={props?.isMultiple} //设置多选
          // value={checkItem}//点击确定清空选中数据
          onChange={(val: any) => { setCheckItem(val) }}
        >
          {props?.vipTypes?.length > 0 ? (
            props?.vipTypes?.map((item: any) => {
              return (
                <CheckList.Item key={item?.value} value={item?.value}>
                  {item.label || item.content}
                </CheckList.Item>
              );
            })
          ) : (
            <div className={styles.tip}>
              暂无数据...
            </div>
          )}
        </CheckList>
        { props.isLoadMore && <InfiniteScroll loadMore={props.loadMore} hasMore={props.hasMore} />}
        {
          props?.isPageBtn &&
          <div style={{ padding: "10px 0 20px" }}>
            <PageButtonNum num={1} max={window.config.PAGE_MAX} min={1} />
          </div>
        }
      </div>
    </Popup>
  );
});

export default ChoiceModal;
