// 接口类型定义

// eslint-disable-next-line
export interface Result<T = any> {
  code: string
  data: T
  msg: string
}

// 用户管理
export namespace User {
  // 当前门诊登录用户
  export interface CurrentUser {
    patientName?: string // 姓名
    name?: string // 姓名
    idCard?: string // 身份证号
    patientGender?: string // 性别
    gender?: string // 性别
    patientCard?: string // 卡号
    patientIdCard?: string // 卡号
  }
  // 读卡信息
  export interface ReadCardData {
    cardNo?: string // 卡号
    cardType?: string // 读卡类型
    cardName?: string // 姓名
    cardSN?: string // 卡片SN号 社保卡用
    cardData?: string // 卡串信息
    mdtrtCertNo?: string // 医保电子凭证
    mdtrtCertType?: string // 医保凭证类型
    patientName?: string // 患者姓名
    patientIdCard?: string // 患者身份证
    address?: string // 地址
    payType?: string // 患者类型 0自费 1医保
  }
  // 住院信息
  export interface InHospitalInfo {}
}
