.login-wrapper{
  background-color: #fff;
  padding: 12px 12px 0;
  .label {
    .info {
      padding: 2px 5px;
      margin-left: 5px;
      color: #333;
      border-radius: 6px;
      font-size: 17px;
      font-weight: 500;
      border: 1px solid #d9d9d9;
      .num {
        display: inline-block;
        margin-right: 5px;
      }
    }
    .login-status{
      width: 24px;
      height: 24px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      &.loginIn{
        background-image: url('@/assets/images/btns/ysdl-o.png');
      }
      &.loginOut{
        background-image: url('@/assets/images/btns/ysdl.png');
      }
    }
  }
}
.menu-wrapper {
  width: 100%;

  // 大按钮
  .icon-box-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: calc(106px / $pixel);
    height: calc(106px / $pixel);

    >img {
      width: calc(52px / $pixel);
      height: calc(52px / $pixel);
    }
  }

  .tips {
    color: #666666;
    font-size: calc(26px / $pixel);
    line-height: calc(30px / $pixel);
    transform: scale(0.9);
    letter-spacing: 0.5px;
    text-align: center;
    margin-bottom: 0;
  }

  // 大号菜单按钮功能
  .big-button-wrapper {
    width: calc(665px / $pixel);
    height: calc(275px / $pixel);
    border-radius: calc(12px / $pixel);
    margin: 0 auto;
    padding: 0 calc(34px / $pixel);
    padding-right: calc(30px / $pixel);

    &.bggreen {
      background-color: #E9FFFD;

      .icon-box-1 {
        background: url("../../assets/images/icons/green-menu-icon-bg.png") no-repeat center;
        background-size: 100% 100%;

      }

      .right-btn {
        background: linear-gradient(to right, #81FBD3, #31D8DE);

      }
    }

    &.bgred {
      background-color: #FFF4F4;

      .icon-box-1 {
        background: url("../../assets/images/icons/red-menu-icon-bg.png") no-repeat center;
        background-size: 100% 100%;

      }

      .right-btn {
        background: linear-gradient(to right, #FF9B4E, #FF6B54);

      }
    }

    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: calc(184px / $pixel);
      padding: calc(18px / $pixel) 1px 0;
      border-bottom: 1px solid #D6D6D6;
      margin-bottom: calc(26px / $pixel);

      .title-box {
        
        display: flex;
        flex-direction: column;

        // height: calc(117px / $pixel);
        .title {
          color: #333333;
          font-size: calc(60px / $pixel);
          line-height: calc(74px / $pixel);
          font-weight: 500;
          margin-bottom: calc(0px / $pixel);
        }

      }

      .right-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        float: right;
        width: calc(155px / $pixel);
        height: calc(60px / $pixel);
        border-radius: calc(30px / $pixel);
        box-shadow: 0 2px 2px rgba(0, 0, 0, 0.25);
        cursor: pointer;

        >span {
          font-size: calc(34px / $pixel);
          color: #FFFFFF;
          line-height: calc(34px / $pixel);
          margin-right: calc(7px / $pixel);
        }

        >img {
          width: calc(16px / $pixel);
          height: calc(30px / $pixel);
        }
      }
    }

    .attention {
      font-size: calc(35px / $pixel);
      line-height: calc(44px / $pixel);
      color: #666666;
      text-align: center;
      letter-spacing: 0.5px;
    }
  }

  // 中号菜单按钮功能
  .mid-button-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    // width: 325px/$pixel;
    height: calc(280px / $pixel);
    border-radius: calc(12px / $pixel);
    padding-top: calc(25px / $pixel);

    .icon-box-1 {
      margin-bottom: calc(30px / $pixel);
    }

    .title {
      font-size: calc(60px / $pixel);
      line-height: calc(60px / $pixel);
      font-weight: 500;
      color: #333333;
      margin-bottom: calc(20px / $pixel);
    }
  }

  // 小号菜单按钮功能
  .sm-button-wrapper {
    height: calc(190px / $pixel);
    border-radius: calc(12px / $pixel);

    .top {
      display: flex;
      align-items: center;
      padding-top: calc(38px / $pixel);
      padding-left: calc(22px / $pixel);
      margin-bottom: calc(28px / $pixel);

      .icon-box-3 {
        display: flex;
        align-items: center;
        justify-content: center;
        width: calc(76px / $pixel);
        height: calc(76px / $pixel);
        margin-right: calc(24px / $pixel);

        >img {
          width: calc(38px / $pixel);
          height: calc(38px / $pixel);
        }
      }

      .title {
        font-size: calc(45px / $pixel);
        color: #333333;
        line-height: calc(56px / $pixel);
        margin-bottom: 0;
      }
    }

    .tips {
      letter-spacing: 0;

    }

    &.bggreen {
      background-color: #E9FFFD;

      .icon-box-3 {
        background: url("../../assets/images/icons/green-menu-icon-bg.png") no-repeat center;
        background-size: 100% 100%;

      }
    }

    &.bgblue {
      background-color: #E8F5FF;

      .icon-box-3 {
        background: url("../../assets/images/icons/blue-menu-icon-bg.png") no-repeat center;
        background-size: 100% 100%;

      }
    }

    &.bgred {
      background-color: #FFF4F4;

      .icon-box-3 {
        background: url("../../assets/images/icons/red-menu-icon-bg.png") no-repeat center;
        background-size: 100% 100%;

      }
    }
  }

  .menu1,
  .menu2,
  .menu3,
  .menu4,
  .menu5,
  .menu6 {
    padding-top: calc(24px / $pixel);

    .big-button-wrapper {
      &.bgred {
        background-color: #FFF4F4;
        margin-bottom: calc(20px / $pixel);

        .icon-box-1 {
          background: url("../../assets/images/icons/red-menu-icon-bg.png") no-repeat center;
          background-size: 100% 100%;
        }

        .right-btn {
          background: linear-gradient(to right, #FF9B4E, #FF6B54);
        }

      }

      &.bggreen {
        background-color: #E9FFFD;
        margin-bottom: calc(20px / $pixel);

        .icon-box-1 {
          background: url("../../assets/images/icons/green-menu-icon-bg.png") no-repeat center;
          background-size: 100% 100%;
        }

        .right-btn {
          background: linear-gradient(to right, #81FBD3, #31D8DE);
        }
      }

      &.bgblue {
        background-color: #E8F5FF;
        margin-bottom: calc(20px / $pixel);

        .icon-box-1 {
          background: url("../../assets/images/icons/blue-menu-icon-bg.png") no-repeat center;
          background-size: 100% 100%;
        }

        .right-btn {
          background: linear-gradient(to right, #5CB9FF, #3D81F5);
        }
      }
    }
  }

  .menu3,
  .menu4,
  .menu5,
  .menu6 {

    .menu3-mid-wrapper,
    .menu4-mid-wrapper,
    .menu5-mid-wrapper,
    .menu6-mid-wrapper {
      width: calc(665px / $pixel);
      margin: 0 auto;
      // border: 1px solid red;
    }

    .mid-button-wrapper {

      &.bg0,
      &.bg1 {
        background-color: #E9FFFD;

        .icon-box-1 {
          background: url("../../assets/images/icons/green-menu-icon-bg.png") no-repeat center;
          background-size: 100% 100%;

        }
      }

      &.bg2,
      &.bg3 {
        background-color: #E8F5FF;

        .icon-box-1 {
          background: url("../../assets/images/icons/blue-menu-icon-bg.png") no-repeat center;
          background-size: 100% 100%;

        }
      }

      &.bg4,
      &.bg5 {
        background-color: #FFF4F4;

        .icon-box-1 {
          background: url("../../assets/images/icons/red-menu-icon-bg.png") no-repeat center;
          background-size: 100% 100%;

        }
      }
    }
  }

  .menu1 {
    padding-top: calc(83px / $pixel) !important;

    // 温馨提示
    .warm-tips-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      // width: calc(620px / $pixel);
      margin: 0 auto;

      &.signin {
        width: calc(510px / $pixel);
      }

      &.nuclein {
        width: calc(570px / $pixel);
      }

      >img {
        width: calc(165px / $pixel);
        height: calc(38px / $pixel);
        margin: 0 auto calc(9px / $pixel);
      }

      .img-title-box {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: calc(38px / $pixel);
        margin-bottom: calc(9px / $pixel);

        >img {
          width: calc(16px / $pixel);
          height: calc(10px / $pixel);
        }

        span {
          margin: 0 calc(14px / $pixel);
          color: #FF3141;
          font-size: calc(28px / $pixel);
          line-height: calc(40px / $pixel);
        }
      }

      >p {
        margin: 0;
        margin-bottom: calc(5px / $pixel);
        padding-left: calc(64px / $pixel);
        font-size: calc(32px / $pixel);
        line-height: calc(30px / $pixel);
        color: #333333;
        letter-spacing: 1.5px;
        transform: scale(0.9);
        transform-origin: 0 0;
      }
    }
  }

  .menu6 {
    // padding-top: calc(56px / $pixel);

    .sm-button-wrapper {
      height: calc(220px / $pixel);

      .top {
        height: calc(220px / $pixel);
        display: flex;
        align-items: center;
        padding-top: calc(0px / $pixel);
        padding-left: calc(20px / $pixel);
        margin-bottom: calc(0px / $pixel);

        .icon-box-3 {
          display: flex;
          align-items: center;
          justify-content: center;
          width: calc(90px / $pixel);
          height: calc(90px / $pixel);
          margin-right: calc(10px / $pixel);

          >img {
            width: calc(43px / $pixel);
            height: calc(52px / $pixel);
          }
        }

        .title {
          font-size: calc(50px / $pixel);
          color: #333333;
          line-height: calc(56px / $pixel);
          margin-bottom: 0;
        }
      }

      &.bg0,
      &.bg1 {
        background-color: #E9FFFD;

        .icon-box-3 {
          background: url("../../assets/images/icons/green-menu-icon-bg.png") no-repeat center;
          background-size: 100% 100%;

        }
      }

      &.bg2,
      &.bg3 {
        background-color: #E8F5FF;

        .icon-box-3 {
          background: url("../../assets/images/icons/blue-menu-icon-bg.png") no-repeat center;
          background-size: 100% 100%;

        }
      }

      &.bg4,
      &.bg5 {
        background-color: #FFF4F4;

        .icon-box-3 {
          background: url("../../assets/images/icons/red-menu-icon-bg.png") no-repeat center;
          background-size: 100% 100%;

        }
      }
    }
  }

  &.zy-menu-wrapper {
    padding-top: calc(40px / $pixel);

    .zy-warm-tips-wrapper {
      text-align: center;
      margin-top: calc(20px / $pixel);

      p {
        color: #333333;
        font-size: calc(20px / $pixel);
        line-height: calc(30px / $pixel);
        transform: scale(0.9);
        letter-spacing: 1.5px;
        text-align: center;
        margin-bottom: 0;
        margin-top: calc(10px / $pixel);
      }
    }
  }

  .menu-new {
    background-color: #F6F6F6;
    border-radius: calc(32px/$pixel) calc(32px/$pixel) 0 0;
    padding: calc(20px/$pixel) calc(25px/$pixel);

    .top-menu {
      background-color: #fff;
      padding: calc(20px / $pixel);
      border-radius: calc(16px/$pixel);
    }

    .other-menu {
      margin-top: calc(15px/$pixel);
      background-color: #fff;
      padding: calc(20px / $pixel);
      border-radius: calc(16px/$pixel);

      .menu-title {
        font-size: calc(35px/$pixel);
        position: relative;
        padding-left: calc(20px/$pixel);
        font-weight: medium;

        &::before {
          position: absolute;
          left: 0;
          top: calc(10px / $pixel);
          content: '';
          width: calc(11px/$pixel);
          height: calc(33px/$pixel);

        }

        &.blue {
          &::before {
            background: linear-gradient(to right, #5CB8FF, #3D81F5);
          }
        }

        &.green {
          &::before {
            background: linear-gradient(to bottom, #21C46D, #46D488);
          }
        }

        &.orange {
          &::before {
            background: linear-gradient(to bottom, #FCDE65, #F7891B);
          }
        }
      }

      .menu-item {
        width: 25%;
        text-align: center;

        a {
          text-decoration: none;
          color: inherit;
          cursor: auto;
        }

        .menu-icon {
          width: calc(60px/$pixel);
          height: calc(60px/$pixel);
          margin: calc(20px/$pixel) auto;

          .icon-image {
            width: 100%;
            height: 100%;
          }
        }

        .menu-name {
          font-size: calc(25px/$pixel);
        }
      }
    }
  }

  .mini-button-wrapper {
    margin: 0 auto;
    text-align: center;
    min-width: calc(180px / $pixel);



    &.bggreen {
      .icon-box-3 {
        margin: 0 auto;
        background: url("../../assets/images/icons/green-menu-icon-bg.png") no-repeat center;
        background-size: 100% 100%;
        width: calc(100px/$pixel);
        height: calc(100px/$pixel);
        line-height: calc(90px/$pixel);

        .menu-icon {
          width: calc(45px/$pixel);
          height: calc(50px/$pixel);
        }
      }
    }

    &.bgblue {

      .icon-box-3 {
        margin: 0 auto;
        background: url("../../assets/images/icons/blue-menu-icon-bg.png") no-repeat center;
        background-size: 100% 100%;
        width: calc(100px/$pixel);
        height: calc(100px/$pixel);
        line-height: calc(90px/$pixel);

        .menu-icon {
          width: calc(50px/$pixel);
          height: calc(45px/$pixel);
        }
      }
    }

    &.bgorange {

      .icon-box-3 {
        margin: 0 auto;
        background: url("../../assets/images/icons/orange-menu-icon-bg.png") no-repeat center;
        background-size: 100% 100%;
        width: calc(100px/$pixel);
        height: calc(100px/$pixel);
        line-height: calc(90px/$pixel);

        .menu-icon {
          width: calc(55px/$pixel);
          height: calc(53px/$pixel);
        }
      }
    }

    .title {
      color: #565656;
      font-size: calc(35px/$pixel);
      margin-top: calc(10px/$pixel);
      margin-bottom: 0;
    }

    .tips {
      color: #969696;
      font-size: calc(23px/$pixel);
    }
  }
}
