.header-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: calc(100px / $pixel);
  padding: 0 calc(15px / $pixel);

  .logo-box {
    display: flex;
    align-items: center;
    height: calc(86px / $pixel);
    font-size: calc(30px / $pixel);
    color: #fff;
    flex: 1;

    >img {
      height: 100%;
    }
  }

  .title {
    margin-bottom: 0;
    font-size: calc(28px / $pixel);
    color: #FFFFFF;
    letter-spacing: 2px;
    line-height: calc(40px / $pixel);
    font-weight: 600;
    text-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
  }

  .right {
    display: flex;
    text-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
    margin-right: calc(150px / $pixel);
    align-items: center;

    p {
      margin-bottom: 0;
      text-align: right;
      color: #ffffff;

      &.time {
        font-size: calc(38px / $pixel);
        line-height: calc(44px / $pixel);
        font-weight: 600;
        letter-spacing: 1px;
      }

      &.date {
        font-size: calc(22px / $pixel);
        line-height: calc(40px / $pixel);
      }
    }

    :global(.ant-btn-sm) {
      padding: 0 calc(15px / $pixel);
      margin-left: calc(20px / $pixel);
    }
  }
}
