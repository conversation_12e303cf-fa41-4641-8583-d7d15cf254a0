import { useState, useEffect } from 'react'
import dayjs from 'dayjs'
import styles from './index.module.scss'
import { NavBar, JumboTabs } from 'antd-mobile'
import { useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/store'
import RegNumberDrawer from './components/RegNumberDrawer'
import { getSpeak } from '@/services/hardware'
import { scheduler } from 'timers/promises'

const doctor = [{}]

const DoctorList = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { selectDept } = useSelector((state: RootState) => state.register)
  const [daySchedule, setDaySchedule] = useState([])
  const [doctorList, setDoctorList] = useState([])
  const [selectDate, setSelectDate] = useState<string>(null)
  const [regNumberVisible, setRegNumberVisible] = useState<boolean>(false)

  const initDate = () => {
    const arr = [
      {
        date: dayjs().format('MM-DD'),
        key: dayjs().format('YYYY-MM-DD'),
        week: '今日'
      }
    ]
    // 查询14天
    for (let i = 1; i <= window.config.RESERVE_DAY_COUNT; i++) {
      arr.push({
        date: dayjs().add(i, 'day').format('MM-DD'),
        key: dayjs().add(i, 'day').format('YYYY-MM-DD'),
        week: `周${dayjs().add(i, 'day').locale('zh-cn').format('dd')}`
      })
    }

    setDaySchedule(arr)
    setSelectDate(arr[0].key)
  }

  const handelSelectDate = key => {
    setSelectDate(key)
  }

  const handleQueryInterval = val => {
    let doc
    if (val === 1) {
      doc = {
        doctorName: '普通医生',
        doctorID: '0101',
        doctorType: '1',
        scheduleType: '2',
        registerAmount: '15'
      }
    } else if (val === 2) {
      doc = {
        doctorName: '杜鹃',
        doctorID: '0103',
        doctorType: '2',
        scheduleType: '2',
        registerAmount: '80'
      }
    } else if (val === 3) {
      doc = {
        doctorName: '沈泽',
        doctorID: '0102',
        doctorType: '2',
        scheduleType: '2',
        registerAmount: '35'
      }
    }
    dispatch({
      type: 'register/setSchedule',
      payload: {
        ...doc
      }
    })
    setRegNumberVisible(true)
  }

  const hanleConfirmInterval = item => {
    console.log(item)
    setRegNumberVisible(false)
    dispatch({
      type: 'register/setSelectOrderNum',
      payload: {
        selectDate,
        ...item
      }
    })
    if (selectDate === dayjs().format('YYYY-MM-DD')) {
      navigate('/register/confirm')
    } else {
      navigate('/register/locknum')
    }
  }

  useEffect(() => {
    initDate()
    window.config.isSpeak && getSpeak({ content: '请选择医生' })
  }, [])

  return (
    <div className={styles['doctor-wrapper']}>
      <div className={styles['header']}>
        <NavBar back='返回' onBack={() => navigate(-1)} style={{ color: '#fff' }}>
          {selectDept?.deptName}
        </NavBar>
      </div>
      <div className={styles['date-wrapper']}>
        {daySchedule.length > 0 && (
          <JumboTabs defaultActiveKey={daySchedule[0].key} onChange={handelSelectDate}>
            {daySchedule.map(item => {
              return (
                <JumboTabs.Tab
                  title={item.date}
                  description={item.week}
                  key={item.key}
                ></JumboTabs.Tab>
              )
            })}
          </JumboTabs>
        )}
      </div>
      <div className={styles['pt-doctor-wrapper']}>
        <div className={styles.title}>普通号</div>
        <div className={`${styles.schdule} ${styles.stopped}`}>
          <div className={styles.item}>上午</div>
          <div className={styles.item}>普通门诊</div>
          <div className={styles.item}>15元</div>
          <div className={styles.button}>约满</div>
        </div>
        <div className={`${styles.schdule}`} onClick={() => handleQueryInterval(1)}>
          <div className={styles.item}>下午</div>
          <div className={styles.item}>普通门诊</div>
          <div className={styles.item}>15元</div>
          <div className={styles.button}>余号30</div>
        </div>
      </div>
      <div className={styles['zj-doctor-wrapper']}>
        <div className={styles.title}>专家号</div>
        <div className={styles['doctor-schdule']}>
          <div className={styles['doctor-info']}>
            <img
              src={require('@/assets/images/others/default-doctor.png')}
              alt='医生照片'
              className={styles['doctor-image']}
            />
            <div className={styles['right']}>
              <div className={styles['name-box']}>
                <span className={styles['name']}>杜鹃</span>
                <span className={styles['job']}>主任医师</span>
              </div>
              <div className={styles['content']}>
                擅长：<span>肿瘤、痔疮内镜治疗、炎...</span>
              </div>
            </div>
          </div>
          <div className={`${styles.schdule}`} onClick={() => handleQueryInterval(2)}>
            <div className={styles.item}>下午</div>
            <div className={styles.item}>专家门诊</div>
            <div className={styles.item}>80元</div>
            <div className={styles.button}>余号24</div>
          </div>
        </div>
        <div className={styles['doctor-schdule']}>
          <div className={styles['doctor-info']}>
            <img
              src={require('@/assets/images/others/default-doctor.png')}
              alt='医生照片'
              className={styles['doctor-image']}
            />
            <div className={styles['right']}>
              <div className={styles['name-box']}>
                <span className={styles['name']}>沈泽</span>
                <span className={styles['job']}>副主任医师</span>
              </div>
              <div className={styles['content']}>
                擅长：<span>消化道肿瘤、痔疮内镜治疗、炎症治疗...</span>
              </div>
            </div>
          </div>
          <div className={`${styles.schdule} ${styles.stopped}`}>
            <div className={styles.item}>上午</div>
            <div className={styles.item}>专家门诊</div>
            <div className={styles.item}>35元</div>
            <div className={styles.button}>约满</div>
          </div>
          <div className={styles.schdule} onClick={() => handleQueryInterval(3)}>
            <div className={styles.item}>下午</div>
            <div className={styles.item}>专家门诊</div>
            <div className={styles.item}>35元</div>
            <div className={styles.button}>余号24</div>
          </div>
        </div>
      </div>
      {regNumberVisible && (
        <RegNumberDrawer
          visible={regNumberVisible}
          title={`${dayjs(selectDate).format('MM月DD日')} 星期${dayjs(selectDate)
            .locale('zh-cn')
            .format('dd')}`}
          onConfirm={hanleConfirmInterval}
          onCancel={() => {
            setRegNumberVisible(false)
          }}
        ></RegNumberDrawer>
      )}
    </div>
  )
}

export default DoctorList
