import { useEffect, useState } from 'react'
import { List, CalendarPicker, Button } from 'antd-mobile'
import { useNavigate } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import {
  CalendarFilled,
  ContactsFilled,
  PlusCircleFilled,
  CheckCircleFilled,
  ExclamationCircleFilled
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { getPaymentList } from '@/services/pay'
import { getSpeak } from '@/services/hardware'
import { IdNoPrivate } from '@/utils/utils'
import { RootState } from '@/store'
import dayjs from 'dayjs'
import ModuleHeader from '@/components/ModuleHeader'
import Empty from '@/components/Empty'
import styles from './index.module.scss'

/**
 * 查询列表
 * @param fields
 */
const handleQueryPaymentList = async fields => {
  try {
    let response
    if (window.config.isDebug) {
      response = {
        msg: '请求成功！',
        code: '0',
        data: [
          {
            patientName: '完炳林',
            productID: '4200059288202408224395714421',
            sysType: '7',
            patientID: '6252753',
            subject: '完炳林的急诊收费',
            payMode: 1,
            idCard: '330821194710073811',
            businessID: 'BToC',
            remark: '132759861764686040',
            hospitalName: 'XXX医院',
            deviceCode: 'PH1423BT90013',
            medicalPrivateAmount: 0,
            payTypeName: '门诊支付',
            totalAmount: 280,
            payType: 1,
            createTime: '2024-08-22 18:50',
            stateName: '支付成功',
            outTradeNo: 'WXP202408220003824285',
            medicalAmount: 78,
            payModeName: '微信',
            privateAmount: 202,
            state: '1',
            saID: '180ac3a0-64a6-4922-ad08-4fc90c326fcc',
            key: 9
          },
          {
            patientName: '陈文忠',
            productID: '4200057879202408224372611143',
            sysType: '7',
            patientID: '6282669',
            subject: '陈文忠的急诊收费',
            payMode: 2,
            idCard: '632126199311133118',
            businessID: 'BToC',
            remark: '133658535413359326',
            hospitalName: 'XXX医院',
            deviceCode: 'PH1423BT90013',
            medicalPrivateAmount: 0,
            payTypeName: '门诊支付',
            totalAmount: 186,
            payType: 1,
            createTime: '2024-08-22 10:31',
            stateName: '支付成功',
            outTradeNo: 'WXP202408220003823016',
            medicalAmount: 0,
            payModeName: '支付宝',
            privateAmount: 186,
            state: '1',
            saID: '180ac3a0-64a6-4922-ad08-4fc90c326fcc',
            key: 8
          },
          {
            productID: '2024082023001417851400033106',
            sysType: '7',
            subject: '丁泉锋的急诊收费',
            idCard: '330282198502239176',
            remark: '288382606281854620',
            payType: 1,
            stateName: '退款',
            outTradeNo: 'ALI202408200003816852',
            medicalAmount: 134.16,
            state: '2',
            saID: '180ac3a0-64a6-4922-ad08-4fc90c326fcc',
            key: 7,
            refundAmount: -31.84,
            patientName: '丁泉锋',
            patientID: '9073936',
            payMode: 2,
            refundTime: '2024-08-20 11:05',
            businessID: 'BToC',
            hospitalName: 'XXX医院',
            deviceCode: 'PH1423BT90013',
            medicalPrivateAmount: 0,
            refundOutTradeNo: 'ALI202408200003816852',
            payTypeName: '门诊支付',
            totalAmount: 166,
            createTime: '2024-08-20 10:26',
            payModeName: '支付宝',
            privateAmount: 31.84
          },
          {
            productID: '2024082022001483611419156862',
            sysType: '7',
            subject: '胡嘉文的急诊收费',
            idCard: '330802199402285015',
            remark: '286702684877134397',
            payType: 1,
            stateName: '退款',
            outTradeNo: 'ALI202408200003816570',
            medicalAmount: 0,
            state: '2',
            saID: '180ac3a0-64a6-4922-ad08-4fc90c326fcc',
            key: 5,
            refundAmount: -166,
            patientName: '胡嘉文',
            patientID: '9073842',
            payMode: 2,
            refundTime: '2024-08-20 09:47',
            businessID: 'BToC',
            hospitalName: 'XXX医院',
            deviceCode: 'PH1423BT90013',
            medicalPrivateAmount: 0,
            refundOutTradeNo: 'ALI202408200003816570',
            payTypeName: '门诊支付',
            totalAmount: 166,
            createTime: '2024-08-20 09:46',
            payModeName: '支付宝',
            privateAmount: 166
          },
          {
            productID: '2024081622001483611401328688',
            sysType: '7',
            subject: '汪波的急诊收费',
            idCard: '330106199201112713',
            remark: '285059603124069099',
            payType: 1,
            stateName: '退款',
            outTradeNo: 'ALI202408160003805515',
            medicalAmount: 0,
            state: '2',
            saID: '180ac3a0-64a6-4922-ad08-4fc90c326fcc',
            key: 4,
            refundAmount: -12,
            patientName: '汪波',
            patientID: '9073939',
            payMode: 2,
            refundTime: '2024-08-16 17:32',
            businessID: 'BToC',
            hospitalName: 'XXX医院',
            deviceCode: 'PH1423BT90013',
            medicalPrivateAmount: 0,
            refundOutTradeNo: 'ALI202408160003805515',
            payTypeName: '门诊支付',
            totalAmount: 12,
            createTime: '2024-08-16 17:07',
            payModeName: '支付宝',
            privateAmount: 12
          },
          {
            productID: '2024081622001483611401320589',
            sysType: '7',
            subject: '周瑜的急诊收费',
            idCard: '522633202401010816',
            remark: '284923212098956450',
            payType: 1,
            stateName: '退款',
            outTradeNo: 'ALI202408160003804572',
            medicalAmount: 0,
            state: '2',
            saID: '180ac3a0-64a6-4922-ad08-4fc90c326fcc',
            key: 2,
            refundAmount: -14,
            patientName: '周瑜',
            patientID: '9073878',
            payMode: 2,
            refundTime: '2024-08-16 11:31',
            businessID: 'BToC',
            hospitalName: 'XXX医院',
            deviceCode: 'PH1423BT90013',
            medicalPrivateAmount: 0,
            refundOutTradeNo: 'ALI202408160003804572',
            payTypeName: '门诊支付',
            totalAmount: 14,
            createTime: '2024-08-16 11:20',
            payModeName: '支付宝',
            privateAmount: 14
          }
        ]
      }
    } else {
      response = await getPaymentList({
        ...fields
      })
    }
    if (response.code == '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

const Details = () => {
  const navigate = useNavigate()
  const [data, setData] = useState<any>([])
  const [dateVisible, setDateVisible] = useState<boolean>(false)
  const [selectRangeDate, setSelectRangeDate] = useState<[Date, Date]>([
    dayjs().toDate(),
    dayjs().toDate()
  ])
  const { readCardData } = useSelector((state: RootState) => state.userInfo)
  const dispatch = useDispatch()

  useEffect(() => {
    handleQuery(selectRangeDate, readCardData)
    window.config.isSpeak && getSpeak({ content: '查询急救收费记录' })
    return () => {
      dispatch({
        type: `userInfo/setReadCard`,
        payload: {}
      })
    }
  }, [readCardData])

  const handleQuery = async (val, userInfo) => {
    const res = await handleQueryPaymentList({
      beginTime: dayjs(val[0]).format('YYYY-MM-DD'),
      endTime: dayjs(val[1]).format('YYYY-MM-DD'),
      idCard: userInfo?.certNo
    })
    if (res && res.data.length > 0) {
      setData(res.data)
    }
  }

  // 跳转详情
  const handleToSelectDiag = () => {
    navigate('/identity?nextpage=/emergency/details')
  }

  // 跳转确认页面
  const handleConfirm = () => {
    navigate('/emergency/confirm')
  }

  const handleDateConfirm = val => {
    setSelectRangeDate(val)
    handleQuery(val, readCardData)
  }

  const min = new Date()
  min.setMonth(min.getMonth() - 2)
  const max = new Date()
  max.setDate(max.getDate())

  return (
    <>
      <ModuleHeader title={'急救记录'} />
      <div className={styles['emergency-list-wrapper']}>
        <div className={styles['date-box']}>
          <div className={styles['label']}>
            <CalendarFilled
              style={{ fontSize: `${35 / window.config.minPixelValue}`, color: '#1677FF' }}
            />
            <span>日期选择</span>
          </div>
          <div
            className={styles['content']}
            onClick={() => {
              setDateVisible(true)
            }}
          >
            <span>{dayjs(selectRangeDate[0]).format('YYYY-MM-DD')}</span>
            <span>——</span>
            <span>{dayjs(selectRangeDate[1]).format('YYYY-MM-DD')}</span>
          </div>
        </div>
        <div className={styles['patient-box']}>
          <div className={styles['label']}>
            <ContactsFilled
              style={{ fontSize: `${35 / window.config.minPixelValue}`, color: '#1677FF' }}
            />
            <span>患者证件搜索</span>
          </div>
          <div className={styles['content']} onClick={handleToSelectDiag}>
            {readCardData.certNo ? (
              `${IdNoPrivate(readCardData.certNo)}`
            ) : (
              <>
                <PlusCircleFilled
                  style={{ fontSize: `${35 / window.config.minPixelValue}`, color: '#1677FF' }}
                />
                <span>查询</span>
              </>
            )}
          </div>
        </div>
        <List
          style={{
            '--border-top': 'none',
            '--border-inner': 'none'
          }}
          className={[styles['info-list'], styles['scroll-style']].join(' ')}
        >
          {data.length > 0 ? (
            data.map((item, index) => (
              <List.Item
                key={index}
                children={
                  <>
                    <div className={styles['info-box']}>
                      <div className={styles['item']}>
                        <span>姓名：</span>
                        {item.patientName}
                      </div>
                      <div className={styles['item']}>
                        <span>证件号：</span>
                        {IdNoPrivate(item.idCard)}
                      </div>
                      <div className={styles['item']}>
                        <span>缴费金额：</span>
                        {item.totalAmount}元<span style={{ marginLeft: 40 }}>自费金额：</span>
                        {item.privateAmount}元
                      </div>
                      <div className={styles['item']}>
                        <span>交易时间：</span>
                        {item.createTime}
                      </div>
                    </div>
                  </>
                }
                extra={
                  <div className={styles['status-icon']}>
                    {item.state === '2' ? (
                      <ExclamationCircleFilled style={{ marginRight: 5, color: '#fa541c' }} />
                    ) : (
                      <CheckCircleFilled style={{ marginRight: 5, color: '#52c41a' }} />
                    )}
                    <span style={{ color: `${item.state === '2' ? '#fa541c' : '#52c41a'}` }}>
                      {item.stateName}
                    </span>
                  </div>
                }
              ></List.Item>
            ))
          ) : (
            <Empty></Empty>
          )}
        </List>
      </div>
      <CalendarPicker
        visible={dateVisible}
        selectionMode='range'
        min={min}
        max={max}
        defaultValue={selectRangeDate}
        onClose={() => setDateVisible(false)}
        onMaskClick={() => setDateVisible(false)}
        onConfirm={handleDateConfirm}
      />
    </>
  )
}

export default Details
