import { useState, useEffect, useRef} from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate, useSearchParams, useLocation } from "react-router-dom";
import { Form, Input, Button, Dialog } from "antd-mobile";
import ETipModal from "@/components/ETipModal";
import ModuleHeader from "@/components/ModuleHeader";
import TypeModal from '../Identity/components/TypeModal';
import { getDoctorInfo } from "@/services/user";
import { getSpeak } from "@/services/hardware";
import { RootState } from "@/store/index";
import gotoMenu from "@/utils/gotoMenu";
import loading from '@/utils/loading'
import styles from "./index.module.scss";
import storage from '@/utils/storage';
import { Flex } from "antd";

// 获取医生列表
const handleGetDoctor = async (fields: any) => {
  console.log("获取医生信息", fields);
  try {
    let response;
    if (window.config.isDebug) {
      response = {
        "code": "0",
        "msg": "请求成功！",
        "data": {
          "ZHENGJIANHM": "330683198709133311",
          "YILIAOJGDM": "33",
          "YONGHUXM": "潘昊",
          "YONGHUID": "332"
        }
      }
    } else {
      response = await getDoctorInfo({
        ...fields,
      });
    }
    if (response.code === "0") {
      return response.data;
    }
    return false;
  } catch (error) {
    return false;
  }
};

const PatientCreate = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [typeModalVisible, setTypeModalVisible] = useState(false)
  const [typeModalType, setTypeModalType] = useState('') // 介质读取弹窗类型
  const timeoutInt = useRef(null)
  // const [search] = useSearchParams();
  // const nextpage = search.get("nextpage");
  // const singleDate: Date = new Date();
  // const [value, setValue] = useState<(string | null)[]>(["M"]);
  // const [visible, setVisible] = useState(false);
  const { loginDocData }: any = useSelector((state: RootState) => state.userInfo);

  const onFinish = async (values: any) => {
    // console.log("onFinish:", values);
    const { cardNo } = values;
    if (cardNo) {
      // // const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
      // // if (phone.length === 0 || !reg.test(phone)) {
      // //   ETipModal("error", "请输入正确的手机号码");
      // //   return;
      // // }
      const res = await handleGetDoctor({
        cardNo,
        saID: window.config.SAID
      });
      if (!res) return;
      console.log("获取到的医生信息", res);
      // 存缓存
      storage.set('loginDocData', res)
      // 存redux
      dispatch({
        type: "userInfo/setLoginDocData",
        payload: res,
      });
      ETipModal("医生登录成功", "success", ()=>{
        gotoMenu(navigate)
      });
    } 
  };

  const handleBrushTheFace = async () => {
    // 调用YW.getUserInfoByFace 引导用户刷脸，返回bizNo, authNo
    console.log('拉起刷脸支付方法')
    loading.start('刷脸启动中，请稍候...')
    if (window.config.isOpenRealReadId) {
      window.YW.getUserInfoByFace({
        success: async (res: any) => {
          console.log(res)
          loading.end()
          const { idNo, userName } = res || {}
          // 按身份证方式查询医生信息
          const info = await handleGetDoctor({
            cardNo: idNo,
            saID: window.config.SAID
          });
          if (!info) return;
          console.log("获取到的医生信息", info);
          // 存缓存
          storage.set('loginDocData', info)
          // 存redux
          dispatch({
            type: "userInfo/setLoginDocData",
            payload: info,
          });
          ETipModal("医生登录成功", "success", ()=>{
            gotoMenu(navigate)
          });
        },
        fail: err => {
          loading.end()
          // 刷脸获取信息失败
          console.log('fail')
          console.log(err)
          ETipModal(JSON.stringify(err), 'error')
        }
      })
    } else {
      // 模拟刷脸
      loading.end()
      setTypeModalType('face')
      setTypeModalVisible(true)
      timeoutInt.current = setTimeout( async () => {
        setTypeModalVisible(false)
        const info = await handleGetDoctor({
          cardNo: '332601194810043316',
          saID: window.config.SAID
        });
        if (!info) return;
        console.log("获取到的医生信息", info);
        // 存缓存
        storage.set('loginDocData', info)
        dispatch({
          type: "userInfo/setLoginDocData",
          payload: info,
        });
        ETipModal("医生登录成功", "success", ()=>{
          gotoMenu(navigate)
        });
      }, 3000)
    }
  }

  const handleCloseTypeModal = () => {
    clearTimeout(timeoutInt.current)
    setTypeModalVisible(false)
  }

  useEffect(() => {
    form.setFieldsValue({
      cardNo: loginDocData?.ZHENGJIANHM || "",
    });
    window.config.isSpeak && getSpeak({ content: "请输入医生证件号或刷脸登录" });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <div className={styles.container}>
        <ModuleHeader title="医生登录" />
        <div className={styles.wrapper}>
          <div className={styles.way}>
            以下两种方式选择一种登录：
          </div>
          <div className={styles.facewrapper}>
            <Button
              className={styles['face-button']}
              block
              type="submit"
              color="primary"
              size="middle"
              onClick={handleBrushTheFace}
            >
              <Flex>
                <img src={require('@/assets/images/pay/face-bold.png')} alt='刷脸图标' className={styles['face-image']}/>
                <span className={styles['btn-text']}>刷脸登录</span>
              </Flex>
            </Button>
          </div>
          <div className={styles.confirm}>
            <Form
              name="form"
              form={form}
              layout="horizontal"
              onFinish={onFinish}
              footer={
                <Button
                  block
                  type="submit"
                  color="primary"
                  size="middle"
                >
                  登录
                </Button>
              }
            >
              <Form.Item
                name="cardNo"
                label="证件号"
              >
                <Input type="number" placeholder="请输入医生证件号" clearable />
              </Form.Item>
              <div className={styles.tips}>
                <span
                  onClick={() =>
                    Dialog.alert({
                      content: "请联系信息科",
                      onConfirm: () => {
                        console.log("Confirmed");
                      },
                    })
                  }
                >
                  登录遇到问题？
                </span>
              </div>
            </Form>
          </div>
        </div>
      </div>
      {typeModalVisible && (
        <TypeModal
          modalVisible={typeModalVisible}
          modalType={typeModalType}
          onCancel={handleCloseTypeModal}
        />
      )}
    </>
  );
};

export default PatientCreate;
