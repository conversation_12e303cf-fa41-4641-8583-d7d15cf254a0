import {
  IdNoPrivate
} from '@/utils/utils';

export default function getPayPrintData(currentUser, listData, preSettlementData) {
  // 打印参数字段
  let itemList = []
  listData.forEach((item, index) => {
    itemList.push({
      "text": `项目${index + 1}`,
      "size": 30,
      "isBold": true,
      "isUnderLine": false,
      "align": 1,
    })
    itemList.push(`项目名称: ${item.name}`)
    itemList.push(`项目单价: ${item.price}`)
    itemList.push(`项目数量: ${item?.quantity} ${item?.unit}`)
  })
  const data = [{
      "text": '急救收费凭条',
      "size": 30,
      "isBold": true,
      "isUnderLine": false,
      "align": 1,
    }, {
      "text": window.config.HOS_NAME,
      "size": 18,
      "isBold": true,
      "isUnderLine": false,
      "align": 1,
    },
    "--------------------------",
    `患者姓名: ${currentUser?.patientName ?? ""}`,
    `患者性别: ${currentUser.patientGender === "1" || currentUser.patientGender === "男"  ? "男" : currentUser.patientGender === "2" || currentUser.patientGender === "女"  ? "女" : "未知"}`,
    `身份证号: ${IdNoPrivate(currentUser.patientIDCard)}`,
    "--------------------------",
  ]
  const newData = data.concat(itemList).concat([
    "--------------------------",
    `总金额: ${preSettlementData?.totalAmount ?? '0'}元`,
    `个人现金支付: ${preSettlementData?.privateAmount ?? '0'}元`,
    `个人账户支付: ${preSettlementData?.medicalPrivateAmount ?? '0'}元`,
    `统筹基金支付: ${preSettlementData?.medicalFound ?? '0'}元`,
    "--------------------------",
  ])

  return newData;
}
