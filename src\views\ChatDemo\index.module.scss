@import '@/assets/scss/base.scss';

.chatDemo {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.demoCard,
.doctorCard,
.instructionCard {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  :global {
    .adm-card-header {
      padding: 16px 16px 8px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
    
    .adm-card-body {
      padding: 8px 16px 16px;
    }
  }
}

.doctorList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.doctorItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.doctorInfo {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.info {
  flex: 1;
  
  .name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }
  
  .specialty {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }
  
  .status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
    
    &.online {
      background-color: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }
    
    &.busy {
      background-color: #fff2e8;
      color: #fa8c16;
      border: 1px solid #ffd591;
    }
  }
}

.actions {
  display: flex;
  gap: 8px;
}

.instructions {
  color: #333;
  line-height: 1.6;
  
  h4 {
    margin: 16px 0 8px 0;
    color: #1890ff;
    font-size: 14px;
    font-weight: 600;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  ul {
    margin: 8px 0 16px 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
      font-size: 14px;
      
      code {
        background-color: #f5f5f5;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        color: #d63384;
      }
      
      strong {
        color: #1890ff;
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .content {
    padding: 12px;
    gap: 12px;
  }
  
  .doctorItem {
    padding: 10px;
  }
  
  .avatar {
    width: 40px;
    height: 40px;
  }
  
  .info {
    .name {
      font-size: 15px;
    }
    
    .specialty {
      font-size: 13px;
    }
  }
  
  .instructions {
    h4 {
      font-size: 13px;
    }
    
    ul li {
      font-size: 13px;
      
      code {
        font-size: 11px;
      }
    }
  }
}

// 卡片悬停效果
.demoCard,
.doctorCard,
.instructionCard {
  transition: box-shadow 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// 医生项目悬停效果
.doctorItem {
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #f0f8ff;
    border-color: #1890ff;
    transform: translateY(-1px);
  }
}

// 按钮样式优化
:global {
  .adm-button {
    border-radius: 8px;
    font-weight: 500;
    
    &.adm-button-large {
      height: 48px;
      font-size: 16px;
    }
    
    &.adm-button-small {
      height: 32px;
      font-size: 14px;
      padding: 0 12px;
    }
  }
}

// 动画效果
.doctorItem {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 状态指示器动画
.status.online {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #52c41a;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1.2);
  }
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}
