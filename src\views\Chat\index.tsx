import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Input, Button, Image, Toast, PullToRefresh } from 'antd-mobile';
import { PictureOutline, SendOutline } from 'antd-mobile-icons';
import ModuleHeader from '@/components/ModuleHeader';
import {
  ChatMessage,
  Conversation,
  sendTextMessage,
  sendImageMessage,
  getChatHistory,
  createOrGetConversation,
  markMessageAsRead
} from '@/services/chat';
import styles from './index.module.scss';

interface ChatProps {
  doctorId?: string;
  patientId?: string;
  doctorName?: string;
}

const Chat: React.FC<ChatProps> = ({
  doctorId = 'device002',
  patientId = 'device001',
  doctorName = '医生'
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [lastMessageId, setLastMessageId] = useState<string>('');
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const pollingRef = useRef<NodeJS.Timeout>();
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();

  // 滚动到底部
  const scrollToBottom = useCallback((smooth = true) => {
    if (!isUserScrolling) {
      messagesEndRef.current?.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto'
      });
    }
  }, [isUserScrolling]);

  // 创建或获取会话
  const initConversation = async () => {
    try {
      const response = await createOrGetConversation({
        user1Id: patientId,
        user2Id: doctorId
      });
      if (response && response.data) {
        setConversation(response.data);
        return response.data.conversationId;
      }
    } catch (error) {
      console.error('创建会话失败:', error);
      Toast.show('连接失败，请重试');
    }
    return null;
  };

  // 加载聊天历史记录
  const loadChatHistory = async (page = 1, isLoadMore = false) => {
    if (!conversation?.conversationId) return;

    if (!isLoadMore) {
      setIsLoadingMore(true);
    }

    try {
      const response = await getChatHistory({
        conversationId: conversation.conversationId,
        page,
        size: 20
      });

      if (response && response.data && response.data.records) {
        const newMessages = response.data.records;

        if (isLoadMore) {
          // 加载更多历史记录，添加到顶部
          setMessages(prev => [...newMessages, ...prev]);
        } else {
          // 首次加载或刷新
          setMessages(newMessages);
          // 标记消息为已读
          markMessageAsRead({
            conversationId: conversation.conversationId,
            userId: patientId
          });
        }

        setHasMore(response.data.hasNext);
        setCurrentPage(page);
      }
    } catch (error) {
      console.error('加载聊天历史失败:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  // 轮询获取新消息
  const pollNewMessages = useCallback(async () => {
    if (!conversation?.conversationId || !isPolling) return;

    try {
      const response = await getChatHistory({
        conversationId: conversation.conversationId,
        page: 1,
        size: 20
      });

      if (response && response.data && response.data.records) {
        const latestMessages = response.data.records;

        if (latestMessages.length > 0) {
          const latestMessageId = latestMessages[0].messageId;

          // 只有当有新消息时才更新
          if (latestMessageId !== lastMessageId) {
            setMessages(latestMessages);
            setLastMessageId(latestMessageId);

            // 如果用户没有在滚动，自动滚动到底部
            if (!isUserScrolling) {
              setTimeout(() => scrollToBottom(), 100);
            }

            // 标记消息为已读
            markMessageAsRead({
              conversationId: conversation.conversationId,
              userId: patientId
            });
          }
        }
      }
    } catch (error) {
      console.error('轮询消息失败:', error);
    }
  }, [conversation?.conversationId, isPolling, lastMessageId, isUserScrolling, scrollToBottom, patientId]);

  // 发送文本消息
  const handleSendText = async () => {
    if (!inputValue.trim() || !conversation?.conversationId) return;

    const tempMessageId = `temp_${Date.now()}`;
    const newMessage: ChatMessage = {
      messageId: tempMessageId,
      conversationId: conversation.conversationId,
      senderId: patientId,
      receiverId: doctorId,
      messageType: 1,
      content: inputValue,
      sendStatus: 1, // 发送中
      readStatus: 0,
      createTime: new Date().toISOString(),
      senderName: '我',
    };

    setMessages(prev => [...prev, newMessage]);
    const messageContent = inputValue;
    setInputValue('');
    setLoading(true);

    try {
      const response = await sendTextMessage({
        senderId: patientId,
        receiverId: doctorId,
        content: messageContent
      });

      if (response && response.data) {
        // 更新消息状态为已发送，使用服务器返回的消息数据
        setMessages(prev =>
          prev.map(msg =>
            msg.messageId === tempMessageId
              ? { ...response.data, sendStatus: 2 }
              : msg
          )
        );

        // 滚动到底部
        setTimeout(() => scrollToBottom(), 100);
      } else {
        // 发送失败
        setMessages(prev =>
          prev.map(msg =>
            msg.messageId === tempMessageId
              ? { ...msg, sendStatus: 3 }
              : msg
          )
        );
        Toast.show('消息发送失败，请重试');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev =>
        prev.map(msg =>
          msg.messageId === tempMessageId
            ? { ...msg, sendStatus: 3 }
            : msg
        )
      );
      Toast.show('消息发送失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 选择图片
  const handleImageSelect = () => {
    fileInputRef.current?.click();
  };

  // 发送图片
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !conversation?.conversationId) return;

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      Toast.show('请选择图片文件');
      return;
    }

    // 检查文件大小（限制5MB）
    if (file.size > 5 * 1024 * 1024) {
      Toast.show('图片大小不能超过5MB');
      return;
    }

    const imageUrl = URL.createObjectURL(file);
    const tempMessageId = `temp_img_${Date.now()}`;
    const newMessage: ChatMessage = {
      messageId: tempMessageId,
      conversationId: conversation.conversationId,
      senderId: patientId,
      receiverId: doctorId,
      messageType: 2,
      content: '[图片]',
      imageUrl,
      fileSize: file.size,
      sendStatus: 1, // 发送中
      readStatus: 0,
      createTime: new Date().toISOString(),
      senderName: '我',
    };

    setMessages(prev => [...prev, newMessage]);
    setLoading(true);

    try {
      const response = await sendImageMessage({
        senderId: patientId,
        receiverId: doctorId,
        imageFile: file
      });

      if (response && response.data) {
        setMessages(prev =>
          prev.map(msg =>
            msg.messageId === tempMessageId
              ? { ...response.data, sendStatus: 2 }
              : msg
          )
        );

        // 滚动到底部
        setTimeout(() => scrollToBottom(), 100);
      } else {
        setMessages(prev =>
          prev.map(msg =>
            msg.messageId === tempMessageId
              ? { ...msg, sendStatus: 3 }
              : msg
          )
        );
        Toast.show('图片发送失败，请重试');
      }
    } catch (error) {
      console.error('发送图片失败:', error);
      setMessages(prev =>
        prev.map(msg =>
          msg.messageId === tempMessageId
            ? { ...msg, sendStatus: 3 }
            : msg
        )
      );
      Toast.show('图片发送失败，请重试');
    } finally {
      setLoading(false);
    }

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    if (!messagesContainerRef.current) return;

    const container = messagesContainerRef.current;
    const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 50;

    // 如果用户滚动到顶部，加载更多历史记录
    if (container.scrollTop === 0 && hasMore && !isLoadingMore) {
      const nextPage = currentPage + 1;
      loadChatHistory(nextPage, true);
    }

    // 设置用户滚动状态
    setIsUserScrolling(!isAtBottom);

    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 设置定时器，如果用户停止滚动一段时间后重置状态
    scrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrolling(false);
    }, 2000);
  }, [hasMore, isLoadingMore, currentPage]);

  // 格式化时间
  const formatTime = (createTime: string) => {
    const date = new Date(createTime);
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取消息状态文本
  const getMessageStatusText = (sendStatus: number) => {
    switch (sendStatus) {
      case 1: return '发送中...';
      case 2: return '已送达';
      case 3: return '发送失败';
      default: return '';
    }
  };

  // 下拉刷新
  const handleRefresh = async () => {
    if (conversation?.conversationId) {
      await loadChatHistory(1, false);
      setCurrentPage(1);
      setTimeout(() => scrollToBottom(false), 100);
    }
  };

  // 初始化
  useEffect(() => {
    const init = async () => {
      const conversationId = await initConversation();
      if (conversationId) {
        await loadChatHistory(1, false);
        setTimeout(() => scrollToBottom(false), 100);
      }
    };
    init();
  }, [doctorId, patientId]);

  // 设置轮询
  useEffect(() => {
    if (conversation?.conversationId && isPolling) {
      pollingRef.current = setInterval(pollNewMessages, 3000); // 每3秒轮询一次
    }

    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, [conversation?.conversationId, isPolling, pollNewMessages]);

  // 页面可见性变化时控制轮询
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsPolling(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={styles.chatContainer}>
      <ModuleHeader title={`与${doctorName}的对话`} />

      {/* 聊天消息区域 */}
      <PullToRefresh onRefresh={handleRefresh}>
        <div
          className={styles.messagesContainer}
          ref={messagesContainerRef}
          onScroll={handleScroll}
        >
          {isLoadingMore && (
            <div className={styles.loadingMore}>
              加载更多历史记录...
            </div>
          )}

          {messages.map((message) => (
            <div
              key={message.messageId}
              className={`${styles.messageItem} ${
                message.senderId === patientId ? styles.userMessage : styles.doctorMessage
              }`}
            >
              <div className={styles.messageContent}>
                {message.messageType === 1 ? (
                  <div className={styles.textMessage}>
                    {message.content}
                  </div>
                ) : (
                  <div className={styles.imageMessage}>
                    <Image
                      src={message.imageUrl}
                      alt="聊天图片"
                      fit="cover"
                      style={{ maxWidth: '200px', maxHeight: '200px' }}
                    />
                  </div>
                )}
                <div className={styles.messageTime}>
                  {formatTime(message.createTime)}
                  {message.senderId === patientId && (
                    <span className={styles.messageStatus}>
                      {getMessageStatusText(message.sendStatus)}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </PullToRefresh>

      {/* 输入区域 */}
      <div className={styles.inputContainer}>
        <div className={styles.inputWrapper}>
          <Button
            fill="none"
            size="small"
            onClick={handleImageSelect}
            className={styles.imageButton}
            disabled={loading}
          >
            <PictureOutline />
          </Button>

          <Input
            placeholder="请输入消息..."
            value={inputValue}
            onChange={setInputValue}
            onEnterPress={handleSendText}
            className={styles.textInput}
            disabled={loading}
          />

          <Button
            color="primary"
            size="small"
            onClick={handleSendText}
            loading={loading}
            disabled={!inputValue.trim() || loading || !conversation}
            className={styles.sendButton}
          >
            <SendOutline />
          </Button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          style={{ display: 'none' }}
        />
      </div>
    </div>
  );
};

export default Chat;
