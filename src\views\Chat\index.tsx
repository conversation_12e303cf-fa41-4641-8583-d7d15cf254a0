import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Input, Button, Image, Toast, PullToRefresh } from 'antd-mobile';
import { PictureOutline } from 'antd-mobile-icons';
import ModuleHeader from '@/components/ModuleHeader';
import {
  ChatMessage,
  Conversation,
  sendTextMessage,
  sendImageMessage,
  getChatHistory,
  createOrGetConversation,
  markMessageAsRead,
  fileToBase64,
  getFileExtension
} from '@/services/chat';
import { validateImageFile, compressImage } from '@/utils/imageUtils';
import styles from './index.module.scss';

interface ChatProps {
  doctorId?: string;
  patientId?: string;
  doctorName?: string;
}

const Chat: React.FC<ChatProps> = ({
  doctorId = 'device002',
  patientId = 'device001',
  doctorName = '医生'
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [lastMessageId, setLastMessageId] = useState<string>('');
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [pollingCount, setPollingCount] = useState(0); // 轮询计数器，用于调试

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const pollingRef = useRef<NodeJS.Timeout>();
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();

  // 滚动到底部
  const scrollToBottom = useCallback((smooth = true) => {
    if (!isUserScrolling) {
      messagesEndRef.current?.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto'
      });
    }
  }, [isUserScrolling]);

  // 创建或获取会话
  const initConversation = async () => {
    try {
      const response = await createOrGetConversation({
        user1Id: doctorId,
        user2Id: patientId,
      });
      if (response && response.data) {
        setConversation(response.data);
        return response.data.conversationId;
      }
    } catch (error) {
      console.error('创建会话失败:', error);
      Toast.show('连接失败，请重试');
    }
    return null;
  };

  // 加载聊天历史记录
  const loadChatHistory = async (page = 1, isLoadMore = false) => {
    if (!conversation?.conversationId) return;

    if (!isLoadMore) {
      setIsLoadingMore(true);
    }

    try {
      console.log('正在加载聊天历史:', {
        conversationId: conversation.conversationId,
        page,
        size: 20
      });

      const response = await getChatHistory({
        conversationId: conversation.conversationId,
        page,
        size: 20
      });

      console.log('getChatHistory响应:', response);

      if (response && response.data) {
        // 根据接口文档，data直接是消息数组
        const newMessages = Array.isArray(response.data) ? response.data : [];

        console.log('解析的消息数组:', newMessages);

        if (isLoadMore) {
          // 加载更多历史记录，添加到顶部
          setMessages(prev => [...newMessages, ...prev]);
        } else {
          // 首次加载或刷新
          setMessages(newMessages);

          // 设置最新消息ID用于轮询比较
          if (newMessages.length > 0) {
            const latestMessageId = newMessages[newMessages.length - 1].messageId;
            setLastMessageId(latestMessageId);
          }

          // 标记消息为已读
          markMessageAsRead({
            conversationId: conversation.conversationId,
            userId: patientId
          });
        }

        // 如果返回的消息数量少于请求的size，说明没有更多数据了
        setHasMore(newMessages.length >= 20);
        setCurrentPage(page);
      } else {
        console.log('getChatHistory返回空数据或格式错误');
      }
    } catch (error) {
      console.error('加载聊天历史失败:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  // 轮询获取新消息
  const pollNewMessages = useCallback(async () => {
    if (!conversation?.conversationId || !isPolling) return;

    try {
      setPollingCount(prev => prev + 1);
      console.log('轮询获取新消息:', {
        conversationId: conversation.conversationId,
        currentMessageCount: messages.length,
        lastMessageId,
        pollingCount: pollingCount + 1
      });

      const response = await getChatHistory({
        conversationId: conversation.conversationId,
        page: 1,
        size: 50 // 增加获取数量，确保能获取到新消息
      });

      if (response && response.data) {
        // 根据接口文档，data直接是消息数组
        const latestMessages = Array.isArray(response.data) ? response.data : [];

        console.log('轮询获取到的消息:', latestMessages);

        if (latestMessages.length > 0) {
          // 检查是否有新消息
          const currentMessageIds = new Set(messages.map((msg: ChatMessage) => msg.messageId));
          const newMessages = latestMessages.filter((msg: ChatMessage) => !currentMessageIds.has(msg.messageId));

          console.log('发现新消息:', newMessages);

          if (newMessages.length > 0) {
            // 合并新消息到现有消息列表
            setMessages(prev => {
              // 创建消息ID的Set用于去重
              const existingIds = new Set(prev.map(msg => msg.messageId));
              const uniqueNewMessages = newMessages.filter((msg: ChatMessage) => !existingIds.has(msg.messageId));

              // 按时间排序合并
              const allMessages = [...prev, ...uniqueNewMessages].sort((a, b) =>
                new Date(a.createTime).getTime() - new Date(b.createTime).getTime()
              );

              return allMessages;
            });

            // 更新最新消息ID
            const latestMessageId = latestMessages[latestMessages.length - 1].messageId;
            setLastMessageId(latestMessageId);

            // 如果用户没有在滚动，自动滚动到底部
            if (!isUserScrolling) {
              setTimeout(() => scrollToBottom(), 100);
            }

            // 标记消息为已读
            markMessageAsRead({
              conversationId: conversation.conversationId,
              userId: patientId
            });
          }
        }
      }
    } catch (error) {
      console.error('轮询消息失败:', error);
    }
  }, [conversation?.conversationId, isPolling, lastMessageId, isUserScrolling, scrollToBottom, patientId, messages, pollingCount]);

  // 发送文本消息
  const handleSendText = async () => {
    if (!inputValue.trim() || !conversation?.conversationId) return;

    const tempMessageId = `temp_${Date.now()}`;
    const newMessage: ChatMessage = {
      messageId: tempMessageId,
      conversationId: conversation.conversationId,
      senderId: patientId,
      receiverId: doctorId,
      messageType: 1,
      content: inputValue,
      sendStatus: 1, // 发送中
      readStatus: 0,
      createTime: new Date().toISOString(),
      senderName: '我',
    };

    setMessages(prev => [...prev, newMessage]);
    const messageContent = inputValue;
    setInputValue('');
    setLoading(true);

    try {
      const response = await sendTextMessage({
        senderId: patientId,
        receiverId: doctorId,
        content: messageContent
      });

      if (response && response.data) {
        // 更新消息状态为已发送，使用服务器返回的消息数据
        setMessages(prev =>
          prev.map(msg =>
            msg.messageId === tempMessageId
              ? { ...response.data, sendStatus: 2 }
              : msg
          )
        );

        // 滚动到底部
        setTimeout(() => scrollToBottom(), 100);
      } else {
        // 发送失败
        setMessages(prev =>
          prev.map(msg =>
            msg.messageId === tempMessageId
              ? { ...msg, sendStatus: 3 }
              : msg
          )
        );
        Toast.show('消息发送失败，请重试');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev =>
        prev.map(msg =>
          msg.messageId === tempMessageId
            ? { ...msg, sendStatus: 3 }
            : msg
        )
      );
      Toast.show('消息发送失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 选择图片
  const handleImageSelect = () => {
    fileInputRef.current?.click();
  };

  // 发送图片
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !conversation?.conversationId) return;

    // 验证图片文件
    const validation = validateImageFile(file);
    if (!validation.valid) {
      Toast.show(validation.message || '图片文件无效');
      return;
    }

    let processedFile = file;

    // 如果文件大于5MB，进行压缩
    if (file.size > 5 * 1024 * 1024) {
      Toast.show('图片较大，正在压缩...');
      try {
        processedFile = await compressImage(file, 5 * 1024 * 1024);
        Toast.show('图片压缩完成');
      } catch (error) {
        console.error('图片压缩失败:', error);
        Toast.show('图片压缩失败，请选择更小的图片');
        return;
      }
    }

    const fileExtension = getFileExtension(processedFile);

    const imageUrl = URL.createObjectURL(processedFile);
    const tempMessageId = `temp_img_${Date.now()}`;
    const newMessage: ChatMessage = {
      messageId: tempMessageId,
      conversationId: conversation.conversationId,
      senderId: patientId,
      receiverId: doctorId,
      messageType: 2,
      content: '[图片]',
      imageUrl,
      fileSize: processedFile.size,
      sendStatus: 1, // 发送中
      readStatus: 0,
      createTime: new Date().toISOString(),
      senderName: '我',
    };

    setMessages(prev => [...prev, newMessage]);
    setLoading(true);

    try {
      // 将文件转换为base64
      const imageBase64 = await fileToBase64(processedFile);
      const imageType = fileExtension;

      const response = await sendImageMessage({
        senderId: patientId,
        receiverId: doctorId,
        imageBase64,
        imageType
      });

      if (response && response.data) {
        setMessages(prev =>
          prev.map(msg =>
            msg.messageId === tempMessageId
              ? { ...response.data, sendStatus: 2 }
              : msg
          )
        );

        // 滚动到底部
        setTimeout(() => scrollToBottom(), 100);
      } else {
        setMessages(prev =>
          prev.map(msg =>
            msg.messageId === tempMessageId
              ? { ...msg, sendStatus: 3 }
              : msg
          )
        );
        Toast.show('图片发送失败，请重试');
      }
    } catch (error) {
      console.error('发送图片失败:', error);
      setMessages(prev =>
        prev.map(msg =>
          msg.messageId === tempMessageId
            ? { ...msg, sendStatus: 3 }
            : msg
        )
      );

      // 根据错误类型显示不同的提示
      if (error instanceof Error && error.message.includes('base64')) {
        Toast.show('图片处理失败，请重试');
      } else {
        Toast.show('图片发送失败，请重试');
      }
    } finally {
      setLoading(false);
    }

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    if (!messagesContainerRef.current) return;

    const container = messagesContainerRef.current;
    const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 50;

    // 如果用户滚动到顶部，加载更多历史记录
    if (container.scrollTop === 0 && hasMore && !isLoadingMore) {
      const nextPage = currentPage + 1;
      loadChatHistory(nextPage, true);
    }

    // 设置用户滚动状态
    setIsUserScrolling(!isAtBottom);

    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 设置定时器，如果用户停止滚动一段时间后重置状态
    scrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrolling(false);
    }, 2000);
  }, [hasMore, isLoadingMore, currentPage]);

  // 格式化时间
  const formatTime = (createTime: string) => {
    const date = new Date(createTime);
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取消息状态文本
  const getMessageStatusText = (sendStatus: number) => {
    switch (sendStatus) {
      case 1: return '发送中...';
      case 2: return '已送达';
      case 3: return '发送失败';
      default: return '';
    }
  };

  // 下拉刷新
  const handleRefresh = async () => {
    if (conversation?.conversationId) {
      await loadChatHistory(1, false);
      setCurrentPage(1);
      setTimeout(() => scrollToBottom(false), 100);
    }
  };

  // 初始化
  useEffect(() => {
    const init = async () => {
      const conversationId = await initConversation();
      if (conversationId) {
        await loadChatHistory(1, false);
        setTimeout(() => scrollToBottom(false), 100);
      }
    };
    init();
  }, [doctorId, patientId]);

  // 设置轮询
  useEffect(() => {
    if (conversation?.conversationId && isPolling) {
      pollingRef.current = setInterval(pollNewMessages, 3000); // 每3秒轮询一次
    }

    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, [conversation?.conversationId, isPolling, pollNewMessages]);

  // 页面可见性变化时控制轮询
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsPolling(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={styles.chatContainer}>
      <ModuleHeader title={`与${doctorName}的对话`} />

      {/* 聊天消息区域 */}
      <PullToRefresh onRefresh={handleRefresh}>
        <div
          className={styles.messagesContainer}
          ref={messagesContainerRef}
          onScroll={handleScroll}
        >
          {isLoadingMore && (
            <div className={styles.loadingMore}>
              加载更多历史记录...
            </div>
          )}

          {messages.map((message) => (
            <div
              key={message.messageId}
              className={`${styles.messageItem} ${
                message.senderId === patientId ? styles.userMessage : styles.doctorMessage
              }`}
            >
              <div className={styles.messageContent}>
                {message.messageType === 1 ? (
                  <div className={styles.textMessage}>
                    {message.content}
                  </div>
                ) : (
                  <div className={styles.imageMessage}>
                    <Image
                      src={message.imageUrl}
                      alt="聊天图片"
                      fit="cover"
                      style={{ maxWidth: '200px', maxHeight: '200px' }}
                    />
                  </div>
                )}
                <div className={styles.messageTime}>
                  {formatTime(message.createTime)}
                  {message.senderId === patientId && (
                    <span className={styles.messageStatus}>
                      {getMessageStatusText(message.sendStatus)}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </PullToRefresh>

      {/* 输入区域 */}
      <div className={styles.inputContainer}>
        <div className={styles.inputWrapper}>
          <Button
            fill="none"
            size="small"
            onClick={handleImageSelect}
            className={styles.imageButton}
            disabled={loading}
          >
            <PictureOutline />
          </Button>

          <Input
            placeholder="请输入消息..."
            value={inputValue}
            onChange={setInputValue}
            onEnterPress={handleSendText}
            className={styles.textInput}
            disabled={loading}
          />

          <Button
            color="primary"
            size="small"
            onClick={handleSendText}
            loading={loading}
            disabled={!inputValue.trim() || loading || !conversation}
            className={styles.sendButton}
          >
            发送
          </Button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          style={{ display: 'none' }}
        />
      </div>
    </div>
  );
};

export default Chat;
