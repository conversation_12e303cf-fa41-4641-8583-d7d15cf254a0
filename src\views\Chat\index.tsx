import React, { useState, useEffect, useRef } from 'react';
import { Input, Button, Image, Toast } from 'antd-mobile';
import { PictureOutline, SendOutline } from 'antd-mobile-icons';
import ModuleHeader from '@/components/ModuleHeader';
import { ChatMessage, sendTextMessage, sendImageMessage, getChatHistory } from '@/services/chat';
import styles from './index.module.scss';

interface ChatProps {
  doctorId?: string;
  sessionId?: string;
}

const Chat: React.FC<ChatProps> = ({ doctorId = '1', sessionId = 'session_001' }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 加载聊天历史
  const loadChatHistory = async () => {
    try {
      const response = await getChatHistory({ sessionId });
      if (response && response.data) {
        setMessages(response.data);
      }
    } catch (error) {
      console.error('加载聊天历史失败:', error);
    }
  };

  // 发送文本消息
  const handleSendText = async () => {
    if (!inputValue.trim()) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue,
      type: 'text',
      sender: 'user',
      timestamp: Date.now(),
      status: 'sending'
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue('');
    setLoading(true);

    try {
      const response = await sendTextMessage({
        content: inputValue,
        receiverId: doctorId,
        sessionId
      });

      if (response) {
        // 更新消息状态为已发送
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id 
              ? { ...msg, status: 'sent' }
              : msg
          )
        );
        
        // 模拟医生回复（实际项目中应该通过WebSocket接收）
        setTimeout(() => {
          const doctorReply: ChatMessage = {
            id: (Date.now() + 1).toString(),
            content: '我已经收到您的消息，请稍等，我会尽快回复您。',
            type: 'text',
            sender: 'doctor',
            timestamp: Date.now(),
            status: 'sent'
          };
          setMessages(prev => [...prev, doctorReply]);
        }, 1000);
      } else {
        // 发送失败
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id 
              ? { ...msg, status: 'failed' }
              : msg
          )
        );
        Toast.show('消息发送失败，请重试');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: 'failed' }
            : msg
        )
      );
      Toast.show('消息发送失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 选择图片
  const handleImageSelect = () => {
    fileInputRef.current?.click();
  };

  // 发送图片
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      Toast.show('请选择图片文件');
      return;
    }

    // 检查文件大小（限制5MB）
    if (file.size > 5 * 1024 * 1024) {
      Toast.show('图片大小不能超过5MB');
      return;
    }

    const imageUrl = URL.createObjectURL(file);
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      content: '',
      type: 'image',
      sender: 'user',
      timestamp: Date.now(),
      imageUrl,
      status: 'sending'
    };

    setMessages(prev => [...prev, newMessage]);
    setLoading(true);

    try {
      const response = await sendImageMessage({
        imageFile: file,
        receiverId: doctorId,
        sessionId
      });

      if (response) {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id 
              ? { ...msg, status: 'sent', imageUrl: response.data?.imageUrl || imageUrl }
              : msg
          )
        );
      } else {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id 
              ? { ...msg, status: 'failed' }
              : msg
          )
        );
        Toast.show('图片发送失败，请重试');
      }
    } catch (error) {
      console.error('发送图片失败:', error);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, status: 'failed' }
            : msg
        )
      );
      Toast.show('图片发送失败，请重试');
    } finally {
      setLoading(false);
    }

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  useEffect(() => {
    loadChatHistory();
  }, [sessionId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className={styles.chatContainer}>
      <ModuleHeader title="在线咨询" />
      
      {/* 聊天消息区域 */}
      <div className={styles.messagesContainer}>
        {messages.map((message) => (
          <div 
            key={message.id} 
            className={`${styles.messageItem} ${
              message.sender === 'user' ? styles.userMessage : styles.doctorMessage
            }`}
          >
            <div className={styles.messageContent}>
              {message.type === 'text' ? (
                <div className={styles.textMessage}>
                  {message.content}
                </div>
              ) : (
                <div className={styles.imageMessage}>
                  <Image
                    src={message.imageUrl}
                    alt="聊天图片"
                    fit="cover"
                    style={{ maxWidth: '200px', maxHeight: '200px' }}
                  />
                </div>
              )}
              <div className={styles.messageTime}>
                {formatTime(message.timestamp)}
                {message.sender === 'user' && (
                  <span className={styles.messageStatus}>
                    {message.status === 'sending' && '发送中...'}
                    {message.status === 'sent' && '已送达'}
                    {message.status === 'failed' && '发送失败'}
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className={styles.inputContainer}>
        <div className={styles.inputWrapper}>
          <Button
            fill="none"
            size="small"
            onClick={handleImageSelect}
            className={styles.imageButton}
          >
            <PictureOutline />
          </Button>
          
          <Input
            placeholder="请输入消息..."
            value={inputValue}
            onChange={setInputValue}
            onEnterPress={handleSendText}
            className={styles.textInput}
          />
          
          <Button
            color="primary"
            size="small"
            onClick={handleSendText}
            loading={loading}
            disabled={!inputValue.trim() || loading}
            className={styles.sendButton}
          >
            <SendOutline />
          </Button>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          style={{ display: 'none' }}
        />
      </div>
    </div>
  );
};

export default Chat;
