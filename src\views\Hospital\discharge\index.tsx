import { Row, Col } from 'antd'
import { useState, useEffect, useRef} from 'react'
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom'
import ModuleHeader from '@/components/ModuleHeader'
import PayMode from '@/components/PayMode'
import BlueBgTitle from '@/components/BlueBgTitle'
import { useSelector } from 'react-redux'
import { getSpeak } from '@/services/hardware'
import {
  queryConfirmFee,
  outHospitalPreSettlement,
  outHospitalSettlement
} from '@/services/hospital'
import { RootState } from '@/store'
import { IdNoPrivate } from '@/utils/utils'
import loading from '@/utils/loading'
import styles from './index.module.scss'
import gotoMenu from '@/utils/gotoMenu'
import getDischargePrintData from './dischargePrintData'

/**
 * 费用上传
 * @param fields
 */
const handleConfirmFee = async (fields: any, isLoading) => {
  console.log('费用上传fields', fields)
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        data: '',
        msg: '成功'
      }
    } else {
      response = await queryConfirmFee(
        {
          ...fields
        },
        isLoading
      )
    }
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * 费用预结算
 * @param fields
 */
const handlePreSettlement = async (fields: any) => {
  console.log('费用预结算fields', fields)
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        msg: '请求成功！',
        data: {
          YiBaoBXJE: 15,
          PATIENT_NO: '222272',
          WeiChaJE: 0,
          YouHuiJE: 0,
          YingFuJE: 5,
          FeiYongZE: 20
        }
      }
    } else {
      response = await outHospitalPreSettlement({
        ...fields
      })
    }
    if (response.code === '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

const Discharge = () => {
  const { currentUser, readCardData, medicalInfo, loginDocData } = useSelector(
    (state: RootState) => state.userInfo
  )
  const [isCancelLock, setIsCancelLock] = useState(true)   //是否可以取消
  const [preSettlementData, setPreSettlementData] = useState(null)
  const [printResult, setPrintResult] = useState([])
  const [money, setMoney] = useState<any>(0)
  const navigate = useNavigate()
  const isCancelFlag = useRef(null);

  useEffect(() => {
    handelQuery()
    return () => {
      // 返回页面取消费用
      if(isCancelFlag.current){
        deleteFee()
      }
    }
  }, [])

  const handelQuery = async () => {
    // 1.先做费用上传
    const upload = await handleConfirmFee(
      {
        yonghuID: loginDocData?.YONGHUID,
        saID: window.config.SAID,
        patientID: currentUser.patientID,
        type: 1
      },
      false
    )
    if (!upload) return gotoMenu(navigate)
    // 2.预结算
    const preRes = await handlePreSettlement({
      yonghuID: loginDocData?.YONGHUID,
      saID: window.config.SAID,
      patientID: currentUser.patientID
    })
    if (!preRes) return gotoMenu(navigate)
    setPreSettlementData({ ...preRes })
    setMoney(preRes.YingFuJE)
    window.config.isSpeak && getSpeak({ content: '请确认出院结算信息并支付' })
  }

  const deleteFee = async () => {
    // 取消费用
    const delRes = await handleConfirmFee(
      {
        yonghuID: loginDocData?.YONGHUID,
        saID: window.config.SAID,
        patientID: currentUser.patientID,
        type: 0
      },
      true
    )
  }

  useEffect(()=>{
    setPrintResult(getDischargePrintData(currentUser, preSettlementData))
  }, [currentUser, preSettlementData])

  useEffect(() => {
    isCancelFlag.current = isCancelLock;
  }, [isCancelLock]);

  return (
    <>
      <ModuleHeader title={'出院结算'} />
      <div className={styles['confirm-page-wrapper']}>
        <Row gutter={[0, 6]}>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
              <div className={styles['info-box']}>
                <BlueBgTitle title={'患者信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <div className={styles.title}>
                    姓名<i></i>
                  </div>
                  <span>{currentUser?.patientName}</span>
                </div>
                <div className={styles['item']}>
                  <div className={styles.title}>
                    证件号<i></i>
                  </div>
                  <span>{IdNoPrivate(currentUser?.idCard)}</span>
                </div>
                <div className={styles['item']}>
                  <div className={styles.title}>
                    病案号<i></i>
                  </div>
                  <span>{currentUser?.patientID}</span>
                </div>
              </div>
            </div>
          </Col>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['pay']].join(' ')}>
              <div className={styles['detail-list']}>
                <BlueBgTitle title={'结算信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>费用总额：</span>
                  {preSettlementData?.FeiYongZE ?? 0}元
                </div>
                <div className={styles['item']}>
                  <span>医保报销金额：</span>
                  {preSettlementData?.YiBaoBXJE ?? '0'}元
                </div>
                <div className={styles['item']}>
                  <span>优惠金额：</span>
                  {preSettlementData?.YouHuiJE ?? '0'}元
                </div>
                <div className={styles['pay-money']}>
                  应付金额：<span>{money}</span>元
                </div>
              </div>
            </div>
          </Col>
          <Col span={24}>
            <PayMode
              PAYMENT_TYPE={'discharge'}
              SUCCESS_MODAL_TITLE={'出院结算成功，请取走凭条'}
              onCanceLockNum = {() => setIsCancelLock(false)}
              money={money}
              payParams={{
                patientName: currentUser.patientName,
                patientID: currentUser.patientID,
                patientIDCard: currentUser.idCard,
                patientId: currentUser.patientID,
                payBusinessType: "4",
                subject: `${currentUser.patientName}_居家护理支付`,
                saID: window.config.SAID,
                totalAmount: preSettlementData?.FeiYongZE,
                privateAmount: preSettlementData?.YingFuJE,
                medicalPrivateAmount: "0",
                medicalAmount: preSettlementData?.YiBaoBXJE,
                preferentialAmount: preSettlementData?.YouHuiJE,
                yonghuID: loginDocData?.YONGHUID,
              }}
              settlementParams={{
                feiYongZE: preSettlementData?.FeiYongZE,
                yingFuJE: preSettlementData?.YingFuJE,
                yiBaoBXJE: preSettlementData?.YiBaoBXJE,
                shiShouJE: money,
                weiChaJE: preSettlementData?.WeiChaJE,
                youHuiJE: preSettlementData?.YouHuiJE,
                patientID: currentUser.patientID,
                saID: window.config.SAID,
                yonghuID: loginDocData?.YONGHUID,
              }}
              printParams={{  // 打印参数
                PrintType: "6",
                result: printResult
              }}
              savePrintParams={{
                printType: '6',
                printTypeName: '出院结算凭条',
                yymc: window.config.HOS_NAME,
                cardType: '出院结算',
                totalAmount: preSettlementData?.FeiYongZE ?? 0
              }}
            />
          </Col>
        </Row>
      </div>
    </>
  )
}

export default Discharge
