.room-wrapper {

  .search-wrapper {
    width: calc(670px / $pixel);
    margin: calc(20px / $pixel) auto;
  }

  .container {
    height: calc(100vh - 350px / $pixel);
    background-color: #ffffff;
    display: flex;
    justify-content: flex-start;
    align-items: stretch;

    [data-prefers-color='dark'] & {
      background-color: unset;
    }
  }

  .side {
    flex: none;
    font-size: 38px;
  }

  .main {
    flex: auto;
  }

  .content {
    height: 100%;
    overflow-y: scroll;
    justify-content: center;
    align-items: center;
    font-size: 32px;
  }
}
