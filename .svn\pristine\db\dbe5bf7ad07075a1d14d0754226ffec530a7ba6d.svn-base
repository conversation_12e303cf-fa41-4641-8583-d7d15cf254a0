import request from '@/utils/request'

//签到列表
export async function getPatientInfoToHis(params: any) {
  return request({
    url: '/div/divisional/getPatientInfoToHis.np',
    method: 'GET',
    params,
    loadingText: '正在加载，请稍后...'
  })
}

//5.4.1.获取签约医生基本信息
export async function getPubEmp(params: any) {
  return request({
    //zjgw/getPubEmp
    url: '/zjgw/getPubEmp',
    method: 'POST',
    data: { ...params, deviceCode: window.config.DEVICE_CODE }
  })
}

//获取医生列表
export async function getTeamByCurEmp(params: any) {
  return request({
    url: '/zjgw/getTeamByCurEmp',
    method: 'POST',
    data: { ...params, deviceCode: window.config.DEVICE_CODE }
  })
}
