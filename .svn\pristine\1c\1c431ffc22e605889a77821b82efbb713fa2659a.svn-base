import styles from './index.module.scss'

type WarmTipsType = {
  content: string[]
  type?: string
}

const WarmTipsWrap: React.FC<WarmTipsType> = ({ type, content }) => {
  return (
    <div className={[styles['warm-tips-wrapper'], styles[`${type || ''}`]].join(' ')}>
      <div className={styles['warm-img-title-box']}>
        <img src={require('@/assets/images/others/two-red-circle.png')} alt='温馨提示小图标' />
        <span>温馨提示</span>
        <img src={require('@/assets/images/others/two-red-circle.png')} alt='温馨提示小图标' />
      </div>
      {content.length > 0 &&
        content.map((item, index) => {
          return (
            <p className='warm-title' key={index}>
              {item}
            </p>
          )
        })}
    </div>
  )
}

export default WarmTipsWrap
