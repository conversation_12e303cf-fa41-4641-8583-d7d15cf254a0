import { Modal, Result, Config<PERSON><PERSON><PERSON>, But<PERSON> } from 'antd'
import ModalCloseItem from '../ModalCloseItem'
import { useTheme } from 'antd-style'
import { PositionType } from 'antd/es/image/style'

type ModelProps = {
  modalVisible: boolean
  onCancel: () => void
  modalCloseNum?: number
}

const EModal: React.FC<ModelProps> = ({ modalVisible, onCancel, modalCloseNum }) => {
  const token = useTheme()
  const modalStyles = {
    header: {},
    body: {
      paddingTop: 80,
      minHeight: '540px'
    },
    mask: {
      position: 'absolute' as PositionType
    },
    wrapper: {
      position: 'absolute' as PositionType
    },
    footer: {
      display: 'flex',
      justifyContent: 'space-around',
      paddingBottom: '40px'
    },
    content: {
      borderRadius: 32,
      minHeight: '680px'
    },
    button: {
      width: '280px'
    }
  }

  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            contentFontSizeLG: 42,
            contentLineHeightLG: 1,
            controlHeightLG: 100,
            borderRadiusLG: 100,
            paddingInlineLG: 80,
            defaultBorderColor: token.colorPrimary,
            defaultColor: token.colorPrimary,
            algorithm: true // 启用算法
          },
          Result: {
            extraMargin: '80px 0 0 0',
            iconFontSize: 300,
            subtitleFontSize: 36,
            titleFontSize: 42,
            padding: 55,
            algorithm: true // 启用算法
          }
        }
      }}
    >
      <Modal
        destroyOnClose
        open={modalVisible}
        title={null} // 关闭标题
        footer={null}
        onCancel={onCancel}
        maskClosable={false} // 点击遮罩层的关闭
        closeIcon={false}
        centered
        width={`${660 / window.config.minPixelValue}px`}
        getContainer={document.getElementById('_module') as HTMLElement}
        styles={modalStyles}
      >
        <ModalCloseItem onCancel={onCancel} num={modalCloseNum ?? window.config.TIP_OVER_TIME} />
        <Result
          status='warning'
          title='链接超时'
          icon={<img src={require('@/assets/images/modal/success.png')}></img>}
          extra={[
            <Button size='large' style={{ width: '280px', marginRight: '50px' }} onClick={onCancel}>
              返回首页
            </Button>,
            <Button type='primary' size='large' style={{ width: '280px' }} onClick={onCancel}>
              确认
            </Button>
          ]}
        />
      </Modal>
    </ConfigProvider>
  )
}

export default EModal
