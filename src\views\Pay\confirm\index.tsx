import { useEffect, useState } from 'react'
import { Row, Col } from 'antd'
import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import { RightOutlined } from '@ant-design/icons'
import ModuleHeader from '@/components/ModuleHeader'
import PayMode from '@/components/PayMode'
import BlueBgTitle from '@/components/BlueBgTitle'
import { IdNoPrivate } from '@/utils/utils'
import { getSpeak } from '@/services/hardware'
import styles from './index.module.scss'
import loading from '@/utils/loading'

const Confirm = () => {
  const navigate = useNavigate()
  const { currentUser, readCardData } = useSelector((state: RootState) => state.userInfo)
  const [preSettlementData, setPreSettlementData] = useState(null)
  const [money, setMoney] = useState<any>(0)

  const handleToDetail = () => {
    navigate('/pay/list')
  }

  useEffect(() => {
    loading.start('正在预结算中，请稍候...')
    setTimeout(() => {
      window.config.isSpeak && getSpeak({ content: '请确认缴费信息并支付' })
      loading.end()
    }, 3000)
    const medicalPrivateAmount = readCardData.mdtrt_cert_type === '1' ? '0' : '300'
    const privateAmount = readCardData.mdtrt_cert_type === '1' ? '445' : '145'
    setPreSettlementData({
      medicalFound: '0',
      medicalPrivateAmount,
      privateAmount
    })
    setMoney(privateAmount)
  }, [])
  return (
    <>
      <ModuleHeader title={'门诊缴费'} />
      <div className={styles['confirm-page-wrapper']}>
        <Row gutter={[0, 6]}>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
              <div className={styles['info-box']}>
                <BlueBgTitle title={'就诊人信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>姓名：</span>
                  {currentUser.patientName}
                </div>
                <div className={styles['item']}>
                  <span>证件号：</span>
                  {IdNoPrivate(currentUser.patientIDCard)}
                </div>
                <div className={styles['item']}>
                  <span>手机号：</span>
                  {currentUser.phone}
                </div>
              </div>
              <div className={styles['detail-list']}>
                <div className={styles['item']}>
                  <div>
                    <span className={styles.label}>开单科室：</span>消化内科
                  </div>
                </div>
                <div className={styles['item']}>
                  <div>
                    <span className={styles.label}>西药费：</span>200元
                  </div>
                  <RightOutlined onClick={handleToDetail} />
                </div>
                <div className={styles['item']}>
                  <div>
                    <span className={styles.label}>治疗费：</span>100元
                  </div>
                  <RightOutlined onClick={handleToDetail} />
                </div>
                <div className={styles['item']}>
                  <div>
                    <span className={styles.label}>检查费：</span>145元
                  </div>
                  <RightOutlined onClick={handleToDetail} />
                </div>
              </div>
            </div>
          </Col>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['pay']].join(' ')}>
              <BlueBgTitle title={'支付信息'}></BlueBgTitle>
              <div className={styles['pay-info']}>
                <div className={styles['item']}>
                  <span>总金额：</span>445元
                </div>
                <div className={styles['item']}>
                  <span>医保统筹：</span>
                  {preSettlementData?.medicalFound ?? '0'}元
                </div>
                <div className={styles['item']}>
                  <span>医保个账：</span>
                  {preSettlementData?.medicalPrivateAmount ?? '0'}元
                </div>
              </div>
              <div className={styles['pay-money']}>
                需支付金额：<span>{preSettlementData?.privateAmount ?? '0'}</span>元
              </div>
            </div>
          </Col>
          <Col span={24}>
            <PayMode
              PAYMENT_TYPE={'pay'}
              SUCCESS_MODAL_TITLE={'缴费成功，请取走凭条'}
              money={money}
              payParams={{
                patientName: currentUser.patientName
              }}
            />
          </Col>
        </Row>
      </div>
    </>
  )
}

export default Confirm
