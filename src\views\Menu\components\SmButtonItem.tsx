import { NavLink } from 'react-router-dom'
import Config from '@/common'
import styles from '../index.module.scss'

const SmButtonItem = ({ item, findUrl, class_name }) => {
  return (
    <NavLink to={findUrl(item.name)}>
      <div className={[styles['sm-button-wrapper'], styles[`${class_name}`]].join(' ')}>
        <div className={styles['top']}>
          <div className={styles['icon-box-3']}>
            {item.icon ? (
              <img
                className={styles['menu-icon']}
                src={require(`../../../assets/images/icons/${item.icon}.png`)}
                alt='图标'
              />
            ) : (
              <img className={styles['menu-icon']} src='' alt='图标' />
            )}
          </div>
          <h2 className={styles['title']}>{item.name}</h2>
        </div>
        <p className='tips'>{item.tips}</p>
      </div>
    </NavLink>
  )
}

export default SmButtonItem
