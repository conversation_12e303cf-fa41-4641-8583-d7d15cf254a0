import { createBrowserRouter, Navigate, createHashRouter } from 'react-router-dom'
import Layout from '@/layout' // 主界面
import Menu from '@/views/Menu' // 菜单
import Login from '@/views/Login' // 医生登录
import Register from '@/views/Register' // 挂号
import RegRoomList from '@/views/Register/roomlist' // 挂号-科室
import RegDocList from '@/views/Register/doctorlist' // 挂号-医生排班
import RegLockNum from '@/views/Register/locknum' // 挂号-预约锁号
import RegConfirm from '@/views/Register/confirm' // 挂号-挂号支付
import Pay from '@/views/Pay' // 缴费
import PayList from '@/views/Pay/list' // 缴费-详情列表
import PayConfrim from '@/views/Pay/confirm' // 缴费-支付确认
import Hospital from '@/views/Hospital' // 住院
import HospitalCheckin from '@/views/Hospital/checkin' // 住院-入院登记
import HospitalAccount from '@/views/Hospital/account' // 住院-充值
import HospitalOutconfirm from '@/views/Hospital/outconfirm' // 住院-结算前确认
import HospitalDischarge from '@/views/Hospital/discharge' // 住院-结算
import HospitalQuery from '@/views/Hospital/hosquery' // 住院-清单查询
import SignIn from '@/views/Signin' // 门诊签到
import SignInList from '@/views/Signin/list' // 门诊签到-列表
import YJSignInList from '@/views/Signin/yjlist' // 医技签到-列表
import YFSignInList from '@/views/Signin/yflist' // 药房签到-列表
import TakeNum from '@/views/Takenum' // 预约取号
import TakeNumList from '@/views/Takenum/list' // 预约取号-列表
import TakeNumConfirm from '@/views/Takenum/confirm' // 预约取号-确认支付
import Emergency from '@/views/Emergency' // 急救
import EmergencyList from '@/views/Emergency/list' // 急救-项目列表
import EmergencyQuery from '@/views/Emergency/query' // 急救-查询诊断
import EmergencyConfirm from '@/views/Emergency/confirm' // 急救-确认支付
import EmergencyDetail from '@/views/Emergency/details' // 急救-支付记录
import Delivery from '@/views/Delivery' // 送药上门
import DeliveryList from '@/views/Delivery/list' // 送药上门-项目列表
import DeliveryQuery from '@/views/Delivery/query' // 送药上门-查询诊断
import DeliveryConfirm from '@/views/Delivery/confirm' // 送药上门-确认支付
import Patient from '@/views/Patients/create' // 创建档案
import DocSign from '@/views/DocSign' // 家庭医生签约
import HomeDischarge from '@/views/HomeDischarge' // 家庭病床结算
import Identity from '@/views/Identity' // 登录页面
import Ticket from '@/views/Ticket/list' // 凭条补打
import Chat from '@/views/Chat' // 在线聊天
import ChatDemo from '@/views/ChatDemo' // 聊天演示
import Error404 from '@/views/404' // 404
import Error403 from '@/views/403' // 403

const router = [
  {
    path: '/',
    element: <Navigate to='/menu' />
  },
  {
    id: 'layout',
    element: <Layout />,
    children: [
      {
        path: '/menu',
        element: <Menu />
      },
      {
        path: '/menu/:doctorCode',
        element: <Menu />
      },
      {
        path: '/identity',
        element: <Identity />
      },
      {
        path: '/login',
        element: <Login />
      },
      {
        path: '/register',
        element: <Register />,
        children: [
          {
            path: 'roomlist',
            element: <RegRoomList />
          },
          {
            path: 'doctorlist',
            element: <RegDocList />
          },
          {
            path: 'locknum',
            element: <RegLockNum />
          },
          {
            path: 'confirm',
            element: <RegConfirm />
          }
        ]
      },
      {
        path: '/pay',
        element: <Pay />,
        children: [
          {
            path: 'list',
            element: <PayList />
          },
          {
            path: 'confirm',
            element: <PayConfrim />
          }
        ]
      },
      {
        path: '/patient/create',
        element: <Patient />
      },
      {
        path: '/emergency',
        element: <Emergency />,
        children: [
          {
            path: 'list',
            element: <EmergencyList />
          },
          { path: 'query', element: <EmergencyQuery /> },
          { path: 'confirm', element: <EmergencyConfirm /> },
          { path: 'details', element: <EmergencyDetail /> }
        ]
      },
      {
        path: '/delivery',
        element: <Delivery />,
        children: [
          {
            path: 'list',
            element: <DeliveryList />
          },
          { path: 'query', element: <DeliveryQuery /> },
          { path: 'confirm', element: <DeliveryConfirm /> }
        ]
      },
      {
        path: '/signin',
        element: <SignIn />,
        children: [
          {
            path: 'list',
            element: <SignInList />
          },
          {
            path: 'yjlist',
            element: <YJSignInList />
          },
          {
            path: 'yflist',
            element: <YFSignInList />
          }
        ]
      },
      {
        path: '/takenum',
        element: <TakeNum />,
        children: [
          {
            path: 'list',
            element: <TakeNumList />
          },
          {
            path: 'confirm',
            element: <TakeNumConfirm />
          }
        ]
      },
      {
        path: '/hospital',
        element: <Hospital />,
        children: [
          {
            path: 'account',
            element: <HospitalAccount />
          },
          {
            path: 'discharge',
            element: <HospitalDischarge />
          },
          {
            path: 'checkin',
            element: <HospitalCheckin />
          },
          {
            path: 'hosquery',
            element: <HospitalQuery />
          },
          {
            path: 'outconfirm',
            element: <HospitalOutconfirm />
          }
        ]
      },
      {
        path: '/docsign',
        element: <DocSign />
      },
      {
        path: '/homedischarge',
        element: <HomeDischarge />
      },
      {
        path: '/ticket/list',
        element: <Ticket />
      },
      {
        path: '/chat',
        element: <Chat />
      },
      {
        path: '/chat-demo',
        element: <ChatDemo />
      }
    ]
  },
  {
    path: '*',
    element: <Navigate to='/404' />
  },
  {
    path: '/404',
    element: <Error404 />
  },
  {
    path: '/403',
    element: <Error403 />
  }
]

// export default createBrowserRouter(router, {
//   basename: import.meta.env.BASE_URL
// })

export default createHashRouter(router)
