.confirm-page-wrapper {
  width: calc(660px / $pixel);
  margin: calc(20px / $pixel) auto 0;
  overflow-x: hidden;
}

.confirm-info-list-wrapper {
  position: relative;
  width: 100%;
  padding: calc(0px / $pixel) calc(40px / $pixel);
  background-color: #FFF;
  border-radius: calc(22px / $pixel);
  border: 1px solid #99C4FF;

  &::before {
    position: absolute;
    content: '';
    left: calc(-15px / $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &::after {
    position: absolute;
    content: '';
    right: calc(-15px/ $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &.info {

    &::before,
    &::after {
      top: calc(40px/ $pixel);
    }
  }

  &.pay {
    padding: 0 calc(40px / $pixel) calc(20px / $pixel);

    &::before,
    &::after {
      top: calc(35px/ $pixel);
    }
  }

  .info-box {
    padding: calc(70px / $pixel) 0 calc(20px / $pixel);
    font-size: calc(36px / $pixel);
    line-height: calc(60px / $pixel);
    color: #383838;
    font-weight: 500;

    .item {
      display: flex;
      justify-content: flex-start;
      font-size: 19px;
      .title {
        width: 100px;
        height: 20px;
        padding-right: 15px;
        text-align: justify;
        color: #979797;
        position: relative;
        & i {
          display: inline-block;
          width: 100%;
        }
        &::after {
          position: absolute;
          top: 1px;
          left: 90px;
          content: ':';
          color: #666;
          display: inline-block;
        }
      }
    }
  }

  .detail-list {
    padding: calc(80px / $pixel) 0 calc(30px / $pixel);
    font-size: calc(38px / $pixel);
    line-height: calc(60px / $pixel);
    color: #383838;
    font-weight: medium;
    position: relative;

    .item {
      >span {
        display: inline-block;
        min-width: calc(130px / $pixel);
        margin-right: calc(30px / $pixel);
        color: #666;
        font-weight: bold;
      }
    }

    .pay-money {
      margin-top: calc(20px / $pixel);
      text-align: center;
      font-size: calc(38px / $pixel);
      font-weight: 600;
      color: #383838;

      span {
        color: #FF8D1A;
      }
    }
  }

  .pay-info {
    padding: 0;
    font-size: calc(28px / $pixel);
    line-height: calc(56px / $pixel);
    color: #383838;
    font-weight: 500;

    .item {
      span {
        display: inline-block;
        min-width: calc(150px / $pixel);
        margin-right: calc(30px / $pixel);
        color: #666;
        font-weight: bold;
      }
    }
  }

  .pay-money {
    margin-top: calc(20px / $pixel);
    text-align: center;
    font-size: calc(35px / $pixel);
    font-weight: 600;
    color: #383838;

    span {
      color: #FF8D1A;
    }
  }
}
