@import '@/assets/scss/base.scss';

.chatWindow {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: white;
  border-bottom: 1px solid #e8e8e8;
  border-radius: 16px 16px 0 0;
}

.doctorInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.info {
  .name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
  }
  
  .status {
    font-size: 12px;
    color: #52c41a;
  }
}

.closeButton {
  color: #666;
  border: none;
  background: none;
  padding: 4px;
  min-width: auto;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #f5f5f5;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.messageItem {
  margin-bottom: 12px;
  display: flex;
  
  &.userMessage {
    justify-content: flex-end;
    
    .messageContent {
      background-color: #1890ff;
      color: white;
      border-radius: 16px 16px 4px 16px;
      max-width: 70%;
    }
  }
  
  &.doctorMessage {
    justify-content: flex-start;
    
    .messageContent {
      background-color: white;
      color: #333;
      border-radius: 16px 16px 16px 4px;
      max-width: 70%;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }
}

.messageContent {
  padding: 10px 14px;
  position: relative;
  word-wrap: break-word;
  word-break: break-all;
}

.textMessage {
  font-size: 14px;
  line-height: 1.4;
}

.imageMessage {
  img {
    border-radius: 6px;
    display: block;
  }
}

.messageTime {
  font-size: 11px;
  margin-top: 4px;
  opacity: 0.7;
  display: flex;
  align-items: center;
  gap: 4px;
}

.messageStatus {
  font-size: 10px;
}

.inputContainer {
  background-color: white;
  border-top: 1px solid #e8e8e8;
  padding: 12px 16px;
}

.inputWrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f7f7f7;
  border-radius: 20px;
  padding: 6px 10px;
}

.imageButton {
  color: #666;
  border: none;
  background: none;
  padding: 4px;
  min-width: auto;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.textInput {
  flex: 1;
  border: none;
  background: none;
  font-size: 14px;
  
  :global {
    .adm-input-element {
      background: none;
      border: none;
      outline: none;
      font-size: 14px;
    }
  }
}

.sendButton {
  border-radius: 50%;
  width: 32px;
  height: 32px;
  min-width: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:disabled {
    background-color: #ccc;
    border-color: #ccc;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .messageItem {
    &.userMessage .messageContent,
    &.doctorMessage .messageContent {
      max-width: 80%;
    }
  }
  
  .textMessage {
    font-size: 13px;
  }
  
  .header {
    padding: 12px 16px;
  }
  
  .inputWrapper {
    padding: 5px 8px;
  }
  
  .sendButton {
    width: 28px;
    height: 28px;
    min-width: 28px;
  }
}

// 动画效果
.messageItem {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 消息状态指示器
.messageStatus {
  &:before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    margin-right: 3px;
    background-color: currentColor;
  }
}

// 图片预览优化
.imageMessage {
  img {
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:hover {
      transform: scale(1.02);
    }
  }
}

// 输入框焦点状态
.inputWrapper:focus-within {
  background-color: #f0f0f0;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

// 头部样式优化
.header {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 16px;
    right: 16px;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e8e8e8, transparent);
  }
}

// 在线状态指示器
.status {
  position: relative;
  padding-left: 12px;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #52c41a;
  }
}
