/*
 * @Description: 一个按钮的功能的页面
 */
import BigButtonItem from './BigButtonItem'
import WarmTipsWrap from '@/components/WarmTipsWrap'
import styles from '../index.module.scss'

const MenuOne = ({ data, findUrl }) => {
  return (
    <div className={styles['menu1']}>
      <BigButtonItem item={data?.[0] ?? {}} findUrl={findUrl} class_name='bggreen' />
      {data[0].type === 'signin' ? (
        <WarmTipsWrap
          type='signin'
          content={['1.复诊患者请先签到后就诊', '2.过号延后两个', '3.请保持安静有序的环境']}
        />
      ) : data[0].type === 'nuclein' ? (
        <WarmTipsWrap
          type='nuclein'
          content={[
            '1.使用有问题请联系导医服务台',
            '2.开单缴费完成即可采集核酸',
            '3.请保持安静有序的环境'
          ]}
        />
      ) : (
        ''
      )}
    </div>
  )
}

export default MenuOne
