//详解： https://blog.csdn.net/u014708123/article/details/135269153

import { configureStore } from '@reduxjs/toolkit'
import { register } from './register'
import { userInfo } from './userInfo'
import { list } from './list'

const store = configureStore({
  reducer: {
    register: register.reducer,
    userInfo: userInfo.reducer,
    list: list.reducer
  },
  devTools: true
})
// 定义 ts types
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
export default store
