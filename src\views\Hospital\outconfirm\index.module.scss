.wrapper{
  height: calc(100vh - 130px);
}

.confirm-page-wrapper {
  width: calc(660px / $pixel);
  margin: calc(20px / $pixel) auto 0;
  overflow-x: hidden;
}

.confirm-info-list-wrapper {
  position: relative;
  width: 100%;
  padding: calc(40px / $pixel) calc(40px / $pixel) calc(10px / $pixel);
  background-color: #FFF;
  border-radius: calc(22px / $pixel);
  border: 1px solid #99C4FF;

  &::before {
    position: absolute;
    content: '';
    top: calc(40px / $pixel);
    left: calc(-15px / $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &::after {
    position: absolute;
    content: '';
    top: calc(40px/ $pixel);
    right: calc(-15px/ $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &.info {

    &::before,
    &::after {
      top: calc(40px/ $pixel);
    }
  }

  &.pay {
    padding: calc(50px / $pixel) calc(10px / $pixel) calc(20px / $pixel) calc(20px / $pixel);

    &::before,
    &::after {
      top: calc(35px/ $pixel);
    }
  }

  .info-box {
    padding: calc(40px / $pixel) 0 calc(20px / $pixel);
    font-size: calc(36px / $pixel);
    line-height: calc(60px / $pixel);
    color: #383838;
    font-weight: 500;

    .item {
      display: flex;
      justify-content: flex-start;
      font-size: 19px;
      .title {
        width: 100px;
        height: 20px;
        padding-right: 15px;
        text-align: justify;
        color: #979797;
        position: relative;
        & i {
          display: inline-block;
          width: 100%;
        }
        &::after {
          position: absolute;
          top: 1px;
          left: 90px;
          content: ':';
          color: #666;
          display: inline-block;
        }
      }
    }
  }

  .pay-info-wrapper {
    // height: calc(768px / $pixel);
    // overflow-y: scroll;

    .pay-item {
      padding: calc(20px / $pixel) 0;
      border-bottom: 1px solid #CFCFCF;

      &:last-child {
        border-bottom: none;
      }

      .pay-summary {
        font-size: calc(35px / $pixel);
      }

      .title {
        text-align: center;
        font-size: calc(32px / $pixel);
        line-height: calc(42px / $pixel);
        margin-bottom: calc(10px / $pixel);
        margin-top: calc(25px / $pixel);
        &.name{
          text-align: left;
        }
      }

      .content {
        text-align: center;
        font-size: calc(28px / $pixel);
        line-height: calc(38px / $pixel);
        margin-bottom: calc(10px / $pixel);
        &.name{
          text-align: left;
        }
      }

      .action{
        color: #84B5FF;
        font-size: calc(28px / $pixel);
        text-align: center;
        line-height: calc(38px / $pixel);
        margin-bottom: calc(10px / $pixel);
      }
    }
  }

  .clear-btn{
    text-align: right;
    color: #1677ff;
    font-size:16px;
    padding: 0 10px 10px;
  }
}

.date-select-wrapper {
  display: flex;
  justify-content: space-between;
  background-color: #FFF;
  border-radius: calc(12px / $pixel);
  padding: calc(5px/ $pixel) calc(40px/ $pixel);
  border: 1px solid #99C4FF;
}
