import { Row, Col, Card } from 'antd'
import { Button, Form, Input, Toast, Radio, Space, TextArea} from 'antd-mobile'
import { Flex } from 'antd'
import { useState, useEffect, useCallback} from 'react'
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import { getSpeak } from '@/services/hardware'
import { checkInHospital } from '@/services/hospital'
import ModuleHeader from '@/components/ModuleHeader'
import { IdNoPrivate, IdCard } from '@/utils/utils'
import BlueBgTitle from '@/components/BlueBgTitle'
import ETipModal from '@/components/ETipModal'
import ChooseModal from '@/components/ChooseModal'
import gotoMenu from '@/utils/gotoMenu'
import styles from './index.module.scss'

/**
 * 入院登记
 * @param fields
 */
const handleCheckIn = async (fields: any) => {
  console.log("入院登记fields", fields);
  try {
    let response;
    if (window.config.isDebug) {
      response = {
        code: '0',
        data: '',
        msg: '成功'
      }
    } else {
      response = await checkInHospital({
        ...fields,
      });
    }
    if (response.code === "0") {
      return response;
    }
    return false;
  } catch (error) {
    return false;
  }
};

const Checkin = () => {
  const [form] = Form.useForm();
  const { currentUser, readCardData, medicalInfo, loginDocData} = useSelector((state: RootState) => state.userInfo)
  const [selectDiagnosis, setSelectDiagnosis] = useState<any[string]>([]); // 选中的诊断信息
  const [selectTypes, setSelectTypes] = useState<any[string]>(""); //弹出框数据类型
  const [multipleModalVisible, setMultipleModalVisible] = useState(false); //弹出框
  const navigate = useNavigate()

  useEffect(() => {
    window.config.isSpeak && getSpeak({ content: '请填写信息确认入院登记' })
  }, [])

  // 表单提交失败的方法
  const onFinishFailed = (errorInfo: any) => {
    // 可以在这里处理验证失败的情况
    console.log("Failed:", errorInfo);
    Toast.show({
      content: errorInfo.errorFields[0].errors[0],
      afterClose: () => {
        console.log("after");
      },
    });
  };

  const onFinish = async (values) => {
    console.log('入院登记数据', values)
    const res = await handleCheckIn({
      name: readCardData.certName,
      apiPar: JSON.stringify(medicalInfo),
      diagCode: selectDiagnosis[0].code,
      jiatingdz: values.jiatingdz,
      lianxidh: values.lianxidh,
      hunyinzk: values.hunyinzk,
      yonghuID: loginDocData?.YONGHUID,
      saID: window.config.SAID
    });
    if(!res) return
    ETipModal('入院登记成功', 'success', () => {
      gotoMenu(navigate)
    })
  }

  return (
    <>
      <ModuleHeader title={'入院登记'} />
      <div className={[styles['wrapper'], styles['scroll-style']].join(' ')}>
        <div className={styles['confirm-page-wrapper']}>
          <Row gutter={[0, 30]}>
            <Col span={24}>
              <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
                <div className={styles['info-box']}>
                  <BlueBgTitle title={'住院信息'}></BlueBgTitle>
                  <div className={styles['item']}>
                    <span>患者姓名：</span>
                    {readCardData.certName}
                  </div>
                  <div className={styles['item']}>
                    <span>患者性别：</span>
                    {IdCard(readCardData.certNo, 2) === '1'
                      ? '男'
                      : IdCard(readCardData.certNo, 2) === '2'
                        ? '女'
                        : '未知'}
                  </div>
                  <div className={styles['item']}>
                    <span>出生日期：</span>
                    {IdCard(readCardData.certNo, 1)}
                  </div>
                  <div className={styles['item']}>
                    <span>身份证号：</span>
                    {IdNoPrivate(readCardData.certNo)}
                  </div>
                  <div className={styles['item']}>
                    <span>入院日期：</span>
                    {dayjs().format('YYYY-MM-DD')}
                  </div>
                </div>
                <Form
                  name='form'
                  form={form}
                  onFinish={onFinish}
                  onFinishFailed={onFinishFailed}
                  footer={
                    <Button block type='submit' color='primary' size='middle' shape='rounded'>
                      提交
                    </Button>
                  }
                >
                  <Form.Header>
                    <Flex justify='space-between' align='center' className={styles.label}>
                      <Flex justify='space-between' align='center'>
                        <img src={require('@/assets/images/others/diagnosis.png')}></img>
                        <span className={styles.text}>信息录入</span>
                      </Flex>
                    </Flex>
                  </Form.Header>
                  <Form.Item
                    name="diagnosis"
                    label="病情诊断"
                    rules={[{ required: true, message: "请选择病情诊断" }]}
                  >
                    <TextArea placeholder="请选择病情诊断" readOnly rows={2} onClick={(event) => {
                      event.stopPropagation();
                      event.preventDefault(); // 阻止表单的默认提交行为
                      setSelectTypes("cbzd");
                      setMultipleModalVisible(true);
                    }} />
                  </Form.Item>
                  { selectDiagnosis.length > 0 && <div className={styles['clear-btn']}><Button size='mini' shape='rounded' onClick={()=>{
                    setSelectDiagnosis('')
                    form.setFieldsValue({
                      diagnosis: ''
                    });
                  }}>清除已选诊断</Button></div> }
                  <Form.Item name="jiatingdz" label="家庭地址">
                    <Input type="text" placeholder="请输入家庭地址" clearable />
                  </Form.Item>
                  <Form.Item name="lianxidh" label="联系电话">
                    <Input type="number" placeholder="请输入联系电话" clearable />
                  </Form.Item>
                  <Form.Item
                    name="hunyinzk"
                    label="婚姻状况"
                    rules={[{ required: true, message: "请选择婚姻状况" }]}
                  >
                    <Radio.Group>
                      <Space direction="horizontal" block={true}>
                        <Radio value="0">已婚</Radio>
                        <Radio value="1">未婚</Radio>
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                </Form>
              </div>
            </Col>
          </Row>
        </div>
      </div>
     
      <ChooseModal
        visibleListPopup={multipleModalVisible}
        onCancel={() => {
          setMultipleModalVisible(false)
        }}
        onConfirm={(val: any) => {
          setMultipleModalVisible(false)
          // 诊断信息
          if (selectTypes === "cbzd") {
            // 改成单选
            // const _arr = [...selectDiagnosis, ...val]
            // // 数组去重
            // const map = new Map()
            // const _nwearr = _arr.filter((v: any) => !map.has(v.code) && map.set(v.code, v))
            setSelectDiagnosis(val)
            // const diagnosisStr = _nwearr.map(item => item.label).join("，");
            form.setFieldsValue({
              diagnosis: val[0].label
            });
          }
        }}
        selectType = {'cbzd'}  // 选择的类型
        isSearch={true} //是否显示搜索框
        isMultiple= {false} // 支持多选
      ></ChooseModal>
    </>
  )
}

export default Checkin
