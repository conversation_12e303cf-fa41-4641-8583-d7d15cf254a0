/*
 * @Description:
    选择弹窗
 */
import React, { useState, memo, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>rid, <PERSON>up, CheckList, SearchBar, InfiniteScroll } from "antd-mobile";
import { getPrescription, getDiagnosis } from '@/services/hospital'
import styles from "./index.module.scss";

/**
 * 诊疗项目信息
 * @param fields
 */
const handlePrescription = async (fields: any) => {
  try {
    let response
    if (window.config.isDebug) {
      response = {
        code: '0',
        msg: '查询成功',
        data: [
          {
            id: '85153',
            type: '2',
            code: '20283226',
            name: '免疫球蛋白IgE定量测定',
            unit: '项',
            price: '10.0',
            pyCode: 'myqdbIgEdlcd',
            createTime: 1743588243000,
            updateTime: 1743588243000,
          },
          {
            id: '85154',
            type: '2',
            code: '20283227',
            name: '冷球蛋白测定',
            unit: '项',
            price: '10.0',
            pyCode: 'lqdbcd',
            createTime: 1743588243000,
            updateTime: 1743588243000,
          },
          {
            id: '85155',
            type: '2',
            code: '20283228',
            name: 'C—反应蛋白测定(CRP)',
            unit: '项',
            price: '15.0',
            pyCode: 'C—fydbcd(CRP)',
            createTime: 1743588243000,
            updateTime: 1743588243000,
          },
          {
            id: '85156',
            type: '2',
            code: '20283229',
            name: '纤维结合蛋白测定(Fn)',
            unit: '项',
            price: '15.0',
            pyCode: 'xwjhdbcd(Fn)',
            createTime: 1743588243000,
            updateTime: 1743588243000,
          },
          {
            id: '85157',
            type: '2',
            code: '20283230',
            name: '轻链KAPPA、LAMBDA定量(K-LC，λ-LC)',
            unit: '项',
            price: '25.0',
            pyCode: 'qlKAPPA、LAMBDAdl(K-LC，λ-LC)',
            createTime: 1743588243000,
            updateTime: 1743588243000,
          },
          {
            id: '85158',
            type: '2',
            code: '20283231',
            name: '铜蓝蛋白测定',
            unit: '项',
            price: '25.0',
            pyCode: 'tldbcd',
            createTime: 1743588243000,
            updateTime: 1743588243000,
          },
        ],
        current: 1,
        pageSize: 10,
        total: 2919
      }
    } else {
      response = await getPrescription({
        ...fields
      })
    }
    if (response.code === '0') {
      return response
    }
    return false
  } catch (error) {
    return false
  }
}

/**
 * 获取诊断
 * @param fields
 */
const handleDiagnosis = async (fields: any) => {
  try {
    let response;
    if (window.config.isDebug) {
      response = {
        "code": "0",
        "msg": "查询成功！",
        "data": [
          {
            "id": 202,
            "content": "高血压"
          },
          {
            "id": 203,
            "content": "高脂血症"
          },
          {
            "id": 204,
            "content": "糖尿病"
          },
          {
            "id": 205,
            "content": "支气管炎"
          },
          {
            "id": 206,
            "content": "上呼吸道感染"
          },
          {
            "id": 207,
            "content": "急性咽炎"
          },
          {
            "id": 208,
            "content": "慢性胃炎"
          },
          {
            "id": 209,
            "content": "冠状动脉粥样硬化性心脏病"
          },
          {
            "id": 210,
            "content": "急性上呼吸道感染"
          },
          {
            "id": 211,
            "content": "急性支气管炎"
          }
        ]
      }
    } else {
      response = await getDiagnosis({
        ...fields,
      });
    }
    if (response.code === "0") {
      return response;
    }
    return false;
  } catch (error) {
    return false;
  }
};

// 对比数组返回共有的数据
const getCommonElements = (arr1: any, arr2: any) =>
  [...new Set(arr1)].filter((item: any) => arr2.includes(item.value));

interface Props {
  visibleListPopup?: boolean;  // 显示
  onCancel?: () => any;        // 取消
  onConfirm?: (val: any) => any;  // 确认
  selectType?: string;  // 选择的类型
  isSearch?: boolean;  // 支持搜索
  isMultiple?: boolean; // 支持多选
}

const ChooseModal = memo<Props>((props: any) => {
  const [pagenum, setPagenum] = useState<number>(1);
  const [checkItem, setCheckItem] = useState<any[string]>([]);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [data, setData] = useState([])
  const [searchValue, setSearchValue] = useState<string>('')

  useEffect(()=>{
    handleSearch()
  }, [])

  // 诊疗项目
  const onPrescription = async (pagenum: number, searchKey?: string) => {
    const fields = {
      current: pagenum, // 页码
      pageSize: window.config.PAGE_SIZE, // 条数
      saID: window.config.SAID,
      queryCode: searchKey,
    }
    const res = await handlePrescription({ ...fields })
    if (res && res?.data?.length > 0) {
      const _arr = res?.data?.map((item: any, index: number) => {
        return {
          key: item?.code,
          label: `${item?.name} / ${item?.price}元/每${item?.unit}${item.meal === 0 ? '': ' / 套餐'}`,
          value: item?.code,
          ...item,
          num: 1
        }
      })
      setData(val => [...val, ..._arr])
      setHasMore(res?.total > window.config.PAGE_SIZE)
      return _arr
    } else {
      return false
    }
  }

  // 获取诊断
  const onDiagnosis = async (pagenum: number, searchKey?: string) => {
    const fields = {
      current: pagenum, // 页码
      pageSize: window.config.PAGE_SIZE, // 条数
      saID: window.config.SAID,
      pyCode: searchKey,
    }
    const res = await handleDiagnosis(fields);
    if (res && res?.data?.length > 0) {
      const _arr = res.data.map((item: any, i: number) => {
        return {
          label: item.name,
          value: item.code,
          ...item,
        };
      });
      setData(val => [...val, ..._arr])
      setHasMore(res?.total > window.config.PAGE_SIZE)
      return _arr
    } else {
      return false
    }
  }

  const loadMore = async (num?: any, val?: any) => {
    setPagenum(num)
    if(props.selectType === 'cfxx'){
      await onPrescription(num, val)
    }else if(props.selectType === 'cbzd'){
      await onDiagnosis(num, val)
    }
  }

  const handleSearch = (val?: string) =>{
    setPagenum(1)
    setData([])
    setHasMore(false)
    loadMore(1, val)
  }

  const handleSearchClear = () =>{
    setPagenum(1)
    setData([])
    setSearchValue('')
    setHasMore(false)
    loadMore(1)
  }

  const handleSearchCancel = () => {
    setPagenum(1)
    setData([])
    setSearchValue('')
    setHasMore(false)
    loadMore(1)
  }

  return (
    <Popup
      visible={props?.visibleListPopup}
      bodyStyle={{
        borderTopRightRadius: "8px",
        borderTopLeftRadius: "8px",
      }}
      onMaskClick={() => props?.onCancel()}
      destroyOnClose={true}
    >
      <div className={styles.title}>{
            props?.selectType === "bqms"
              ? "请选择病情描述" :
              props?.selectType === "cbzd" ?
                "请选择诊断信息" :
                "请选择诊疗项目"
          }</div>
      <Grid columns={2} gap={12} style={{ padding: "10px" }}>
        <Grid.Item style={{ textAlign: "left" }}>
          <Button
            fill="solid"
            size="middle"
            onClick={() => { props?.onCancel() }}
          >
            取消
          </Button>
        </Grid.Item>
        <Grid.Item style={{ textAlign: "right" }}>
          <Button
            color="primary"
            fill="solid"
            size="middle"
            onClick={() => {
              const commonElements = getCommonElements(
                data,
                checkItem
              );
              props?.onConfirm(commonElements);
            }}
          >
            确认
          </Button>
        </Grid.Item>
      </Grid>
      {props?.isSearch && (
        <div style={{ padding: "0 5px 10px" }}>
          <SearchBar
            value={searchValue}
            onChange={(val) => setSearchValue(val)}
            placeholder="请搜索"
            clearable
            showCancelButton
            style={{
              "--border-radius": "100px",
              "--background": "#f2f2f2",
              "--height": "32px",
              "--padding-left": "12px",
            }}
            onSearch={handleSearch}
            onClear={handleSearchClear}
            onCancel={handleSearchCancel}
          />
        </div>
      )}
      <div className={styles.check}>
        <CheckList
          multiple={props?.isMultiple} //设置多选
          // value={checkItem}//点击确定清空选中数据
          onChange={(val: any) => { setCheckItem(val) }}
        >
          {data?.length > 0 ? (
            data?.map((item: any) => {
              return (
                <CheckList.Item key={item?.value} value={item?.value}>
                  {item.label || item.content}
                </CheckList.Item>
              );
            })
          ) : (
            <div className={styles.tip}>
              暂无数据...
            </div>
          )}
        </CheckList>
        <InfiniteScroll loadMore={()=>loadMore(pagenum + 1)} hasMore={hasMore} />
      </div>
    </Popup>
  );
});

export default ChooseModal;
