import { Row, Col } from 'antd'
import BigButtonItem from './BigButtonItem'
import MidButtonItem from './MidButtonItem'
import styles from '../index.module.scss'

const MenuFive = ({ data, findUrl }) => {
  const data1 = data?.[0] ?? {}
  return (
    <div className={styles['menu6']}>
      <BigButtonItem item={data1} findUrl={findUrl} class_name='bgred' />
      <div className={styles['menu6-mid-wrapper']}>
        <Row justify='space-between' gutter={[7, 10]}>
          {data.slice(1).map((item, index) => {
            return (
              <Col span={12} key={index}>
                <MidButtonItem item={item} class_name={'bg' + index} findUrl={findUrl} />
              </Col>
            )
          })}
        </Row>
      </div>
    </div>
  )
}

export default MenuFive
