import { Row, Col } from 'antd'
import MidButtonItem from './MidButtonItem'
import styles from '../index.module.scss'

const MenuSix = ({ data, findUrl }) => {
  return (
    <div className={styles['menu6']}>
      <div className={styles['menu6-mid-wrapper']}>
        <Row justify='space-between' gutter={[7, 12]}>
          {data.map((item, index) => {
            return (
              <Col span={12} key={index}>
                <MidButtonItem item={item} class_name={'bg' + index} findUrl={findUrl} />
              </Col>
            )
          })}
        </Row>
      </div>
    </div>
  )
}

export default MenuSix
