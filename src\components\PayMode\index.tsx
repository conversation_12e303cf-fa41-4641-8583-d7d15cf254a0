// 支付选项组件 包含支付完的后续结算打印操作
import React, { useState, useRef, useEffect } from 'react'
import { Row, Col } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  handleCreatePay,
  handleAcquiringPay,
  handleOrderPay,
  handleClosedPay,
  handleHisPre,
  handlePrint,
  handleSave,
  handleRegisterSettlement,
  handleRegisterPreSettlement,
  handlePaymentSettlement,
  handleRefund,
  handleRecharge,
  handleOutHospital,
  handleGetInvoice,
  handleGetInvoiceDetail,
  handleAddPayInfo
} from './promise'
import dayjs from 'dayjs'
import PayModal from '../PayModal'
import ETipModal from '../ETipModal'
import gotoMenu from '@/utils/gotoMenu'
import styles from './index.module.scss'
import { rand } from '@/utils/utils'
import loading from '@/utils/loading'

interface PayModeType {
  money: string | number // 金额
  payParams?: any // 创建订单数据
  settlementParams?: any // 结算数据
  printParams?: any // 打印数据
  savePrintParams?: any // 保存打印数据
  PAYMENT_TYPE: string // 结算类型
  SUCCESS_MODAL_TITLE: string // 办理成功提示
  onCanceLockNum?: () => void // 取消锁号
}

const PayMode: React.FC<PayModeType> = props => {
  const { onCanceLockNum, SUCCESS_MODAL_TITLE } = props

  const navigate = useNavigate()
  const [modalType, setModalType] = useState(0)
  const [payType, setPayType] = useState(undefined)
  const [showType, setShowType] = useState<'1' | '2'>('1') // 1 支付二维码 2 被扫
  const [modalVisible, setModalVisible] = useState(false)
  const [scModalVisible, setScModalVisible] = useState(false)
  const [scModalState, setScModalState] = useState('loading')
  const [isClosedPay, setIsClosedPay] = useState(true) //是否可以关闭订单
  const [isCancelHisPre, setIsCancelHisPre] = useState(true) //是否撤销HIS预结算
  const [createPayData, setCreatePayData] = useState<{ outTradeNo: string }>(null)

  const isFlag = useRef(false)
  const timerLoop = useRef(null)

  const clearTimerLoop = () => {
    if (timerLoop.current) {
      clearTimeout(timerLoop.current)
      timerLoop.current = null
    }
  }

  useEffect(() => {
    return () => {
      clearTimerLoop()
    }
  }, [])

  useEffect(() => {
    isFlag.current = modalVisible
  }, [modalVisible])

  // 微信/支付宝处理
  const handleModalVisible = async (type, payType, showType) => {
    clearTimerLoop()
    setModalType(type)
    setPayType(payType)
    setShowType(showType)
    // 充值为0判断
    if (props.PAYMENT_TYPE === 'recharge' && !props.money) {
      ETipModal('请输入充值金额', 'error')
      return
    }
    // 结算金额为0直接出院结算
    if (
      props.money === '0' ||
      props.money === 0 ||
      props.money === '0.0' ||
      props.money === '0.00'
    ) {
      payedSuccess({ outTradeNo: '' }, '') // 直接结算
      // loading.start('正在结算中，请稍候...')
      // setTimeout(() => {
      //   loading.end()
      //   ETipModal(SUCCESS_MODAL_TITLE, 'finish', () => {
      //     gotoMenu(navigate)
      //   })
      // }, 3000)
      return
    }
    if (showType === '1') {
      // 非医保需要支付的创建支付
      setTimeout(() => {
        createPay(payType) // 支付类型 1、微信 2、支付宝 3、银联
      }, 300)
    } else if (showType === '2') {
      setTimeout(() => {
        createBtoCPay(payType) // 支付类型 1、微信 2、支付宝 3、银联
      }, 300)
    }
  }

  // 创建
  const createBtoCPay = async value => {
    console.log('拉起普通扫码方法')
    // setModalVisible(true)
    // setTimeout(() => {
    //   setModalVisible(false)
    //   loading.start('正在结算中，请稍候...')
    // }, 3000)
    // setTimeout(() => {
    //   loading.end()
    //   ETipModal(SUCCESS_MODAL_TITLE, 'finish', () => {
    //     gotoMenu(navigate)
    //   })
    // }, 4500)
    // const fields = { ...props.payParams, payCode: '133009658342679898' }
    // const payResult = await handleAcquiringPay(fields)
    // if (!payResult || payResult.code !== '0') {
    //   // 支付失败再查询下 outTradeNo
    //   const fields = {
    //     patientID: props.payParams.patientID,
    //     apiVersion: window.config.API_VERSION,
    //     deviceCode: window.config.DEVICE_CODE,
    //     outTradeNo: payResult.data?.outTradeNo
    //   }
    //   // 如果失败 查询支付状态 查询成功再进行支付 失败则结束
    //   const queryRes = await handleOrderPay(fields)
    //   if(!queryRes) return 
    //   setCreatePayData(queryRes.data)
    //   payedSuccess(queryRes.data, value)
    //   return
    // }
    // setCreatePayData(payResult.data)
    // payedSuccess(payResult.data, value)
    window.YW.getScanCode({
      success: async res => {
        console.log(res)
        const { scanCode } = res || {}
        const fields = { ...props.payParams, payCode: scanCode }
        const payResult = await handleAcquiringPay(fields)
        if (!payResult || payResult.code !== '0') {
         // 支付失败再查询下 outTradeNo
          const fields = {
            patientID: props.payParams.patientID,
            apiVersion: window.config.API_VERSION,
            deviceCode: window.config.DEVICE_CODE,
            outTradeNo: payResult.data?.outTradeNo
          }
          // 如果失败 查询支付状态 查询成功再进行支付 失败则结束
          const queryRes = await handleOrderPay(fields)
          if(!queryRes) return 
          setCreatePayData(queryRes.data)
          payedSuccess(queryRes.data, value)
          return
        }
        setCreatePayData(payResult.data)
        payedSuccess(payResult.data, value)
      },
      fail: err => {
        // 扫码失败
        console.log('fail', JSON.stringify(err))
        setModalVisible(false)
      }
    })
  }

  // 创建支付订单二维码
  const createPay = async value => {
    const fields = { ...props.payParams, payType: value }
    const res = await handleCreatePay(fields)
    if (!res) return
    setCreatePayData(res.data)
    setModalVisible(true)
    setTimeout(() => {
      _loop(res.data, value)
    }, 1000)
  }

  const _loop = async (createPayData, value) => {
    if (!isFlag.current) {
      clearTimerLoop()
      return
    }
    // 支付类型 1、微信 2、支付宝 3、工行支付 4、支付宝刷脸 5、微信刷脸 6支付宝小程序
    const fields = {
      patientID: props.payParams.patientID,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE,
      outTradeNo: createPayData?.outTradeNo,
      payType: value
    }
    // 查询支付状态
    const res = await handleOrderPay(fields)
    if (res && res.data && res.data.state === '1') {
      // 支付成功
      clearTimerLoop()
      payedSuccess(res.data, value)
    } else {
      // if (!res) {
      //     handleModalHide();
      //     return;
      // }
      timerLoop.current = setTimeout(() => {
        _loop(createPayData, value)
      }, 1000)
    }
  }

  // 支付成功后的处理逻辑 进行结算 结算成功后再打印小票

  /**
   * @description:
   * @param {*} data  支付成功返参
   * @param {*} payType  支付方式，1微信，2支付宝
   * @return {*}
   */
  const payedSuccess = (data, payType) => {
    setModalVisible(false)
    setIsClosedPay(false)
    setIsCancelHisPre(false)
    // 结算
    if (props.PAYMENT_TYPE === 'register') {
      //挂号结算
      onRegister(data, payType)
    } else if (props.PAYMENT_TYPE === 'pay') {
      //自助缴费结算
      onPay(data, payType)
    } else if (props.PAYMENT_TYPE === 'recharge') {
      // 住院充值
      onRecharge(data, payType)
    } else if (props.PAYMENT_TYPE === 'discharge') {
      // 出院结算
      onDischarge(data, payType)
    } else if (props.PAYMENT_TYPE === 'reserve') {
      // 预约挂号
      onRegister(data, payType)
    } else if (props.PAYMENT_TYPE === 'emergency')
      // 急救
      onEmergency(data, payType)
  }

  /**
   * @description: 急救结算
   * @param {*} data 支付成功 返参数据
   * @return {*}
   */
  const onEmergency = async (data, payType) => {
    const fields = {
      ...props.settlementParams,
      outTradeNo: data.outTradeNo
    }
    const res = await handlePaymentSettlement(fields)
    console.log(res)
    if (!res) {
      gotoMenu(navigate)
      return
    }
    const settleData = { ...res }
    if (!payType) {
      await handleAddPayInfo({
        ...props.payParams,
        payType: '5',
        outTradeNo: `YB${dayjs().format('YYYYMMDDHHmmss')}${rand(6)}`,
        state: '1'
      })
    }
    onPrint({
      settleData, // 结算数据
      payedData: data, // 支付数据
      payType: payType || 0 // 支付方式
    })
  }

  /**
   * @description: 挂号结算
   * @param {*} data 支付成功 返参数据
   * @return {*}
   */
  const onRegister = async (data, payType) => {
    const fields = {
      ...props.settlementParams,
      outTradeNo: data.outTradeNo
    }
    const res = await handleRegisterSettlement(fields)
    console.log(res)
    if (res === false) {
      gotoMenu(navigate)
      return
    }
    onCanceLockNum()
    const settleData = { ...res }
    onPrint({
      settleData, // 结算数据
      payedData: data, // 支付数据
      payType: payType || 0 // 支付方式
    })
  }

  /**
   * @description: 多单据结算
   * @param {*} data 支付成功 返参数据
   * @return {*}
   */
  const onPay = async (data, payType) => {
    const fields = {
      ...props.settlementParams,
      outTradeNo: data.outTradeNo
    }
    const res = await handlePaymentSettlement(fields)
    if (!res) {
      gotoMenu(navigate)
      return
    }
    const settleData = { ...res }
    onPrint({
      settleData, // 结算数据
      payedData: data, // 支付数据
      payType: payType || 0 // 支付方式
    })
  }

  /**
   * @description: 住院充值
   * @param {*} data 支付成功 返参数据
   * @return {*}
   */
  const onRecharge = async (data, payType) => {
    const fields = {
      ...props.settlementParams,
      outTradeNo: data.outTradeNo
    }
    const res = await handleRecharge(fields)
    if (!res) {
      gotoMenu(navigate)
      return
    }
    const settleData = { ...res }
    onPrint({
      settleData, // 结算数据
      payedData: data, // 支付数据
      payType: payType || 0 // 支付方式
    })
  }

  /**
   * @description: 出院结算
   * @param {*} data 支付成功 返参数据
   * @return {*}
   */
  const onDischarge = async (data, payType) => {
    const fields = {
      ...props.settlementParams,
      dingDanH: data.productID,
      shangHuDDH: data.outTradeNo,
    }
    const res = await handleOutHospital(fields)
    if (!res) {
      gotoMenu(navigate)
      return
    }
    // const printRes = await handleGetInvoice({
    //   invoiceNub: res.data.fphm,
    //   invoiceType: '2',
    //   patientID: props.payParams.patientID
    // })
    // const printDetail = await handleGetInvoiceDetail({
    //   invoiceNub: res.data.fphm,
    //   patientID: props.payParams.patientID
    // })
    // let settleData = {
    //   fphm: res.data.DingDanH,
    //   pdfUrl: printRes?.data?.pdfUrl,
    //   printDetail,
    //   fphm2: null,
    //   pdfUrl2: null
    // }
    // // 如果返回了发票号2再调用一遍查询
    // if (res.data.fphm2) {
    //   const printRes = await handleGetInvoice({
    //     invoiceNub: res.data.fphm2,
    //     invoiceType: '2',
    //     patientID: props.payParams.patientID
    //   })
    //   settleData = { ...settleData, fphm2: res.data.fphm2, pdfUrl2: printRes?.data?.pdfUrl }
    // }
    onCanceLockNum()
    let settleData = {
      ...res.data
    }
    onPrint({
      settleData, // 结算数据
      payedData: data, // 支付数据
      payType: payType || 0 // 支付方式
    })
    // ETipModal(SUCCESS_MODAL_TITLE, 'finish', () => {
    //   gotoMenu(navigate)
    // })
  }

  /**
   * @description: 打印函数
   * @param {*} settleData  结算数据
   * @param {*} payedData  支付数据
   * @param {*} payType    支付方式
   * @return {*}
   */
  const onPrint = async ({ settleData, payedData, payType }) => {
    loading.start('正在打印小票，请稍候...')
    const fields = { ...props.printParams }
    // 结算
    if (props.PAYMENT_TYPE === 'register') {
      // 挂号
      fields.result.push(`打印时间: ${payedData?.payTime ?? dayjs().format('YYYY-MM-DD HH:mm:ss')}`)
      fields.result.push(`设备号: ${window.config.DEVICE_CODE}`)
      fields.result.push({
        text: '诊区报到码',
        size: 30,
        isBold: true,
        isUnderLine: false,
        align: 1
      })
      fields.result.push({
        type: 'barcode',
        value: settleData.guidedID,
        barType: 4
      })
    } else if (props.PAYMENT_TYPE === 'pay') {
      //自助缴费
      // 缴费小票补充参数
    } else if (props.PAYMENT_TYPE === 'emergency') {
      fields.result.push(`打印时间: ${payedData?.payTime ?? dayjs().format('YYYY-MM-DD HH:mm:ss')}`)
      fields.result.push('设备号:' + window.config.DEVICE_CODE)
      fields.result.push(``)
    } else if (props.PAYMENT_TYPE === 'recharge') {
      // 住院充值
      fields.result.push('支付方式: 银联聚合')
      fields.result.push(`打印时间: ${payedData?.payTime ?? dayjs().format('YYYY-MM-DD HH:mm:ss')}`)
      fields.result.push(`设备号: ${window.config.DEVICE_CODE}`)
      fields.result.push({
        text: '住院号二维码',
        size: 30,
        isBold: false,
        isUnderLine: false,
        align: 1
      })
      fields.result.push({
        type: 'qrcode',
        value: props.payParams.patientID
      })
    } else if (props.PAYMENT_TYPE === 'discharge') {
      // 出院结算
      if (settleData.printDetail) {
        fields.result.splice(6)
        fields.result.push(`入院日期: ${settleData.printDetail?.ryrq}`)
        fields.result.push(`出院日期: ${settleData.printDetail?.cyrq}`)
        fields.result.push('--------------------------')
        fields.result.push(`住院费用: ${settleData.printDetail?.fyhj}`)
        fields.result.push(`个人现金支付: ${settleData.printDetail?.jkhj}`)
        fields.result.push(`个人账户支付: ${settleData.printDetail?.zhzc}`)
        fields.result.push(`统筹基金支付: ${settleData.printDetail?.tczc}`)
        fields.result.push({
          text: '费用信息以结算发票为准',
          size: 18,
          isBold: true,
          isUnderLine: false,
          align: 1
        })
        fields.result.push('--------------------------')
      }
      fields.result.push(`打印时间: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`)
      fields.result.push(`设备号: ${window.config.DEVICE_CODE}`)
      fields.result.push('--------------------------')
      // if (settleData.fphm2) {
      //   fields.result.push({
      //     text: '本次结算共计两张发票',
      //     size: 18,
      //     isBold: true,
      //     isUnderLine: false,
      //     align: 1
      //   })
      //   fields.result.push({
      //     text: '当前为第一张发票',
      //     size: 18,
      //     isBold: true,
      //     isUnderLine: false,
      //     align: 1
      //   })
      // }
      // fields.result.push(`发票号码: ${settleData.fphm}`)
      // fields.result.push({
      //   text: '电子发票二维码',
      //   size: 30,
      //   isBold: false,
      //   isUnderLine: false,
      //   align: 1
      // })
      // fields.result.push({
      //   type: 'qrcode',
      //   value: settleData.pdfUrl,
      //   size: 20,
      //   align: 1
      // })
      // if (settleData.fphm2) {
      //   fields.result.push({
      //     text: '当前为第二张发票',
      //     size: 18,
      //     isBold: true,
      //     isUnderLine: false,
      //     align: 1
      //   })
      //   fields.result.push(`发票号码: ${settleData.fphm2}`)
      //   fields.result.push({
      //     text: '电子发票二维码',
      //     size: 30,
      //     isBold: false,
      //     isUnderLine: false,
      //     align: 1
      //   })
      //   fields.result.push({
      //     type: 'qrcode',
      //     value: settleData.pdfUrl2,
      //     size: 20,
      //     align: 1
      //   })
      // }
    }
    // 保存凭条
    onSave({
      ...props.savePrintParams,
      print: JSON.stringify(fields?.result ?? [])
    })
    window.YW.print({
      type: 0,
      data: fields.result,
      success: res => {
        // 充值再打印一遍
        if (props.PAYMENT_TYPE === 'recharge') {
          window.YW.print({
            type: 0,
            data: fields.result,
            success: res => {
              loading.end()
              ETipModal(SUCCESS_MODAL_TITLE, 'finish', () => {
                gotoMenu(navigate)
              })
            },
            fail: err => {
              loading.end()
              ETipModal(err.message, 'error', () => {
                gotoMenu(navigate)
              })
            }
          })
        } else {
          loading.end()
          ETipModal(SUCCESS_MODAL_TITLE, 'finish', () => {
            gotoMenu(navigate)
          })
        }
      },
      fail: err => {
        loading.end()
        ETipModal(err.message, 'error', () => {
          gotoMenu(navigate)
        })
      }
    })
  }

  /**
   * @description: 保存凭条
   * @return {*}
   */
  const onSave = async content => {
    const params = {
      content: content,
      patientID: props.payParams.patientID,
      voucherID: window.config.DEVICE_CODE + dayjs().format('YYYYMMDDHHmmss'),
      saID: window.config.SAID
    }
    await handleSave(params)
  }

  // 关闭订单
  const closedPay = async () => {
    // payType 支付类型 1、微信 2、支付宝 3、工行支付 4、支付宝刷脸 5、微信刷脸;6、支付宝小程序;7、支付宝二维码;8、微信二维码
    const fields = {
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE,
      outTradeNo: createPayData?.outTradeNo
    }
    await handleClosedPay(fields)
    // setModalVisible(false)
    // setCreatePayData({})
  }

  // 撤销HIS预结算
  // const cancelHisPre = async () => {
  //     const fields = {...props.hisPreParams}
  //     await handleCancelHisPre(fields);
  // }

  // 关闭支付界面弹窗
  const handleModalHide = () => {
    clearTimerLoop()
    setModalVisible(false)
    setCreatePayData(null)
    if (isClosedPay && createPayData?.outTradeNo) {
      closedPay()
    }
    // if (props.ishisPreSettlement && isCancelHisPre && hisCreditsStr) {
    //     cancelHisPre();  //撤销HIS预结算
    // }
  }

  return (
    <>
      <div className={styles['pay-mode-wrapper']}>
        {props.money === '0' ||
        props.money === 0 ||
        props.money === '0.0' ||
        props.money === '0.00' ? (
          <div className={styles['all-pay-wrap']}>
            <div className={styles['all-btn']} onClick={() => handleModalVisible('', '', '1')}>
              确认
            </div>
          </div>
        ) : (
          <div className={styles['others-pay-wrapper']}>
            <div className={styles['title-box']}>
              <img src={require('../../assets/images/others/line.png')} alt='' />
              <span>请选择支付方式</span>
              <img src={require('../../assets/images/others/line.png')} alt='' />
            </div>
            <Row gutter={[0, 32]}>
              {/* <Col span={12}>
                <div
                  className={[styles['item-pay'], styles['pay']].join(' ')}
                  onClick={() => handleModalVisible(2, '2', '2')}
                >
                  <img alt='支付宝图片' src={require('../../assets/images/pay/zfb-pay.png')} />
                  <p>支付宝一码付</p>
                </div>
              </Col>
              <Col span={12}>
                <div className={styles['item-pay']} onClick={() => handleModalVisible(1, '1', '2')}>
                  <img alt='微信图片' src={require('../../assets/images/pay/wechat-pay.png')} />
                  <p>微信扫码</p>
                </div>
              </Col> */}
              <Col span={24}>
                <div className={[styles['item-pay'], styles['pay']].join(' ')} onClick={() => handleModalVisible(0, '3', '2')}>
                  <img alt='微信图片' src={require('../../assets/images/pay/ylzs-pay.png')} />
                  <p>银联聚合扫码</p>
                </div>
              </Col>
              {/* <Col span={12}>
                <div className={[styles['item-pay'], styles['pay']].join(' ')} onClick={()=>handleModalVisible(0, "3", '1')}>
                  <img alt="聚合支付图片" src={require("../../assets/images/pay/jh-pay.png")} />
                  <p>银联聚合被扫</p>
                </div>
              </Col> */}
            </Row>
          </div>
        )}
      </div>
      {modalVisible && (
        <PayModal
          onCancel={() => handleModalHide()}
          modalVisible={modalVisible}
          modalType={modalType} //
          payModeType={showType} // 主扫or被扫
          payParams={{
            ...props.payParams,
            money: props.money,
            ...createPayData
          }}
        />
      )}
    </>
  )
}

export default PayMode
