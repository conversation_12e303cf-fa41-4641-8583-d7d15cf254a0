/*
 * @Author: hjw
 * @Date: 2024-07-18 09:05:17
 * @Description: 硬件相关接口
 */

import request from '@/utils/request'
import { Base64 } from 'js-base64'

// 读社保卡 调用安卓服务
export async function getCardInfo(params: any) {
  return request({
    url: `/jwsGetCardInfo`,
    method: 'POST',
    data: { ...params },
    isState: true,
    isHideError: true,
    isReturnErrorRes: true,
    loadingText: '正在读卡中，请稍候...'
  })
}

// 语音 调用安卓服务
export async function getSpeak(params: any) {
  console.log('播放语音内容', JSON.stringify(params))
  const data = Base64.encode(JSON.stringify(params))
  return request({
    url: `/jwsSpeak`,
    method: 'POST',
    data,
    isState: true,
    isHideError: true,
    isReturnErrorRes: true,
    isHideLoading: true,
    headers: {
      'Content-Type': 'text/plain'
    }
  })
}

// GPS定位
export async function jwsGetGps(params: any) {
  return request({
    url: `/jwsGetGps`,
    method: 'POST',
    data: { ...params },
    isState: true,
    isHideLoading: true,
    isHideError: true,
    isReturnErrorRes: true
  })
}
