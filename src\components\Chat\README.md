# 移动端聊天组件

本项目实现了三种移动端风格的聊天组件，支持一对一图文聊天功能。

## 组件概览

### 1. ChatModal - 聊天模态框
位置：`src/components/ChatModal/index.tsx`

带倒计时自动关闭功能的模态框形式聊天组件，适合快速咨询场景。

**特性：**
- 模态框形式展示
- 自动倒计时关闭
- 支持文本和图片消息
- 移动端优化界面

**使用示例：**
```tsx
import ChatModal from '@/components/ChatModal';

const [visible, setVisible] = useState(false);

<ChatModal
  visible={visible}
  onClose={() => setVisible(false)}
  doctorId="1"
  doctorName="张医生"
  sessionId="session_001"
  modalCloseNum={30} // 30秒后自动关闭
/>
```

### 2. ChatWindow - 聊天窗口
位置：`src/components/ChatWindow/index.tsx`

窗口形式的聊天组件，可手动关闭，适合需要持续对话的场景。

**特性：**
- 窗口形式展示
- 手动关闭控制
- 医生信息展示
- 在线状态显示

**使用示例：**
```tsx
import ChatWindow from '@/components/ChatWindow';

const [visible, setVisible] = useState(false);

<ChatWindow
  visible={visible}
  onClose={() => setVisible(false)}
  doctorId="1"
  doctorName="张医生"
  sessionId="session_001"
/>
```

### 3. Chat - 全屏聊天页面
位置：`src/views/Chat/index.tsx`

全屏页面形式的聊天组件，提供完整的聊天体验。

**特性：**
- 全屏页面展示
- 完整聊天功能
- 消息历史记录
- 图片预览功能

**使用示例：**
```tsx
import Chat from '@/views/Chat';

<Chat 
  doctorId="1" 
  sessionId="session_001" 
/>
```

## API 接口

### 聊天服务
位置：`src/services/api/chat.ts`

提供完整的聊天相关接口：

```tsx
// 发送文本消息
sendTextMessage({
  content: string,
  receiverId: string,
  sessionId: string
})

// 发送图片消息
sendImageMessage({
  imageFile: File,
  receiverId: string,
  sessionId: string
})

// 获取聊天历史
getChatHistory({
  sessionId: string,
  page?: number,
  pageSize?: number
})

// 创建聊天会话
createChatSession({
  doctorId: string,
  patientId: string
})

// 获取在线医生列表
getOnlineDoctors()
```

### 消息类型定义
```tsx
interface ChatMessage {
  id: string;
  content: string;
  type: 'text' | 'image';
  sender: 'user' | 'doctor';
  timestamp: number;
  imageUrl?: string;
  status?: 'sending' | 'sent' | 'failed';
}
```

## 功能特性

### 1. 消息发送
- **文本消息**：支持多行文本，自动换行
- **图片消息**：支持图片上传，最大5MB
- **消息状态**：发送中、已送达、发送失败状态显示

### 2. 界面设计
- **移动端优化**：适配不同屏幕尺寸
- **响应式布局**：支持横竖屏切换
- **深色模式**：自动适配系统主题
- **动画效果**：消息滑入动画，状态变化动画

### 3. 用户体验
- **自动滚动**：新消息自动滚动到底部
- **图片预览**：点击图片可预览
- **输入优化**：支持回车发送，输入框自适应
- **状态反馈**：加载状态、错误提示

## 样式定制

所有组件都使用 SCSS 模块化样式，支持主题定制：

```scss
// 主色调
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;

// 消息气泡
$user-message-bg: #1890ff;
$doctor-message-bg: #ffffff;
$message-border-radius: 16px;

// 间距
$message-padding: 10px 14px;
$container-padding: 16px;
```

## 集成指南

### 1. 安装依赖
确保项目已安装以下依赖：
```json
{
  "antd-mobile": "^5.37.1",
  "antd-mobile-icons": "latest",
  "antd-style": "^3.6.2"
}
```

### 2. 引入组件
```tsx
// 根据需要引入对应组件
import ChatModal from '@/components/ChatModal';
import ChatWindow from '@/components/ChatWindow';
import Chat from '@/views/Chat';
```

### 3. 配置路由（可选）
如果使用全屏聊天页面，需要配置路由：
```tsx
// src/router/index.tsx
import Chat from '@/views/Chat';

{
  path: '/chat',
  element: <Chat />
}
```

### 4. 接口配置
修改 `src/services/api/chat.ts` 中的接口地址，匹配你的后端API：
```tsx
// 示例：修改API基础路径
const API_BASE = '/api/v1/chat';

export async function sendTextMessage(params) {
  return request({
    url: `${API_BASE}/sendText`,
    method: 'POST',
    data: params,
  });
}
```

## 演示页面

查看 `src/views/ChatDemo/index.tsx` 了解完整的使用示例和功能演示。

## 注意事项

1. **图片上传**：需要后端支持 multipart/form-data 格式
2. **实时通信**：当前为模拟实现，生产环境建议使用 WebSocket
3. **消息持久化**：需要后端提供消息存储和历史记录接口
4. **权限控制**：建议添加用户身份验证和权限检查
5. **错误处理**：已包含基础错误处理，可根据需要扩展

## 扩展功能

可以基于现有组件扩展以下功能：
- 语音消息支持
- 文件传输功能
- 消息撤回功能
- 消息转发功能
- 表情包支持
- 消息搜索功能
- 聊天记录导出
