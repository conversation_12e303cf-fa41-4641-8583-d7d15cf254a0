# 移动端聊天组件

本项目实现了移动端风格的聊天组件，支持一对一图文聊天功能，基于1对1聊天接口文档实现。

## 组件概览

### Chat - 全屏聊天页面
位置：`src/views/Chat/index.tsx`

全屏页面形式的聊天组件，提供完整的聊天体验。

**特性：**

- 全屏页面展示
- 轮询获取新消息
- 下拉刷新和上拉加载更多历史记录
- 支持文本和图片消息
- 消息状态显示
- 滚动位置保持

**使用示例：**

```tsx
import Chat from '@/views/Chat';

<Chat
  doctorId="device002"
  patientId="device001"
  doctorName="张医生"
/>
```

## API 接口

### 聊天服务

位置：`src/services/chat.ts`

基于1对1聊天接口文档实现的完整聊天服务：

```tsx
// 创建或获取会话
createOrGetConversation({
  user1Id: string,
  user2Id: string
})

// 发送文本消息
sendTextMessage({
  senderId: string,
  receiverId: string,
  content: string
})

// 发送图片消息（base64格式）
sendImageMessage({
  senderId: string,
  receiverId: string,
  imageBase64: string,
  imageType: string
})

// 文件转base64工具函数
fileToBase64(file: File): Promise<string>

// 获取文件扩展名工具函数
getFileExtension(file: File): string

// 获取聊天历史
getChatHistory({
  conversationId: string,
  page?: number,
  size?: number
})

// 标记消息已读
markMessageAsRead({
  conversationId: string,
  userId: string
})

// 获取会话列表
getConversationList({
  userId: string,
  page?: number,
  size?: number
})
```

### 消息类型定义

```tsx
interface ChatMessage {
  messageId: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  messageType: number; // 1-文字，2-图片
  content: string;
  imageUrl?: string;
  fileSize?: number;
  sendStatus: number; // 1-发送中，2-发送成功，3-发送失败
  readStatus: number; // 0-未读，1-已读
  createTime: string;
  senderName?: string;
  senderAvatar?: string;
}
```

## 功能特性

### 1. 消息发送

- **文本消息**：支持多行文本，自动换行
- **图片消息**：支持图片上传，使用base64格式传输，最大5MB
- **支持格式**：jpg、jpeg、png、gif、bmp、webp
- **消息状态**：发送中、发送成功、发送失败状态显示

### 2. 轮询机制

- **实时更新**：每3秒轮询获取新消息
- **智能轮询**：页面不可见时停止轮询，节省资源
- **滚动保持**：轮询刷新不影响用户翻阅历史记录

### 3. 历史记录管理

- **分页加载**：支持上拉加载更多历史记录
- **下拉刷新**：下拉刷新获取最新消息
- **滚动位置**：智能保持滚动位置，不干扰用户浏览

### 4. 界面设计

- **移动端优化**：适配不同屏幕尺寸
- **响应式布局**：支持横竖屏切换
- **动画效果**：消息滑入动画，状态变化动画

### 5. 用户体验

- **智能滚动**：新消息自动滚动到底部（用户未滚动时）
- **图片预览**：点击图片可预览
- **输入优化**：支持回车发送，输入框自适应
- **状态反馈**：加载状态、错误提示

## 样式定制

所有组件都使用 SCSS 模块化样式，支持主题定制：

```scss
// 主色调
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;

// 消息气泡
$user-message-bg: #1890ff;
$doctor-message-bg: #ffffff;
$message-border-radius: 16px;

// 间距
$message-padding: 10px 14px;
$container-padding: 16px;
```

## 集成指南

### 1. 安装依赖

确保项目已安装以下依赖：

```json
{
  "antd-mobile": "^5.37.1",
  "antd-mobile-icons": "latest"
}
```

### 2. 引入组件

```tsx
import Chat from '@/views/Chat';
```

### 3. 配置路由

配置聊天页面路由：

```tsx
// src/router/index.tsx
import Chat from '@/views/Chat';

{
  path: '/chat',
  element: <Chat />
}
```

### 4. 接口配置

修改 `src/services/chat.ts` 中的接口地址，匹配你的后端API：

```tsx
// 示例：修改API基础路径
const API_BASE = '/chat';

export async function sendTextMessage(params) {
  return request({
    url: `${API_BASE}/message/text`,
    method: 'POST',
    data: params,
  });
}
```

## 演示页面

查看 `src/views/ChatDemo/index.tsx` 了解完整的使用示例和功能演示。

## 注意事项

1. **图片上传**：使用base64格式传输，支持jpg、jpeg、png、gif、bmp、webp格式
2. **文件大小限制**：图片文件最大5MB
3. **实时通信**：使用轮询机制获取新消息，建议生产环境使用WebSocket
4. **消息持久化**：基于1对1聊天接口文档实现，支持完整的消息存储
5. **权限控制**：建议添加用户身份验证和权限检查
6. **错误处理**：已包含基础错误处理，支持网络错误和文件格式错误提示

## 扩展功能

可以基于现有组件扩展以下功能：
- 语音消息支持
- 文件传输功能
- 消息撤回功能
- 消息转发功能
- 表情包支持
- 消息搜索功能
- 聊天记录导出
