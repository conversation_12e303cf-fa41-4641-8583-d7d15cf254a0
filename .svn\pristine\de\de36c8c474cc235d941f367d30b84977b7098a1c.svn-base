import { Row, Col } from 'antd'
import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { RootState } from '@/store'
import { getSpeak } from '@/services/hardware'
import { IdNoPrivate, keepTwoDecimalFull, getMoneyCost } from '@/utils/utils'
import loading from '@/utils/loading'
import dayjs from 'dayjs'
import ModuleHeader from '@/components/ModuleHeader'
import PayMode from '@/components/PayMode'
import BlueBgTitle from '@/components/BlueBgTitle'
import styles from './index.module.scss'

const Confirm = () => {
  const { currentUser, readCardData } = useSelector((state: RootState) => state.userInfo)
  const { listData } = useSelector((state: RootState) => state.list)
  const [money, setMoney] = useState<any>(0)
  const [preSettlementData, setPreSettlementData] = useState(null)
  const [printResult, setPrintResult] = useState(null)

  useEffect(() => {
    handlePreSettlement()
  }, [])

  const handlePreSettlement = async () => {
    loading.start('正在预结算中，请稍候...')
    setTimeout(() => {
      loading.end()
      window.config.isSpeak && getSpeak({ content: '请确认收费信息并支付' })
    }, 3000)
    console.log(listData)
    const itemList = listData.map(item => {
      return {
        code: item.code,
        name: item.name,
        price: item.price,
        prescriptionType: 'F',
        quantity: item.quantity,
        total: keepTwoDecimalFull(parseFloat(item.price) * parseInt(item.quantity))
      }
    })
    const medicalPrivateAmount =
      readCardData.mdtrt_cert_type === '1' ? '0.00' : getMoneyCost(itemList, 'total')
    const privateAmount =
      readCardData.mdtrt_cert_type === '1' ? getMoneyCost(itemList, 'total') : '0.00'
    const preRes = {
      msg: '请求成功！',
      code: '0',
      data: {
        apiPar: '{}',
        billDetailNo: 'H330803002152024083018324702dd',
        medicalFound: '0.00',
        medicalPrivateAmount: medicalPrivateAmount,
        privateAmount: privateAmount,
        totalAmount: getMoneyCost(itemList, 'total')
      }
    }
    setPreSettlementData(preRes.data)
    setMoney(preRes.data?.privateAmount)
  }

  return (
    <>
      <ModuleHeader title={'确认支付'} />
      <div className={[styles['confirm-page-wrapper'], styles['scroll-style']].join(' ')}>
        <Row gutter={[0, 15]}>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
              <BlueBgTitle title={'单据信息'}></BlueBgTitle>
              <div className={styles['info-box']}>
                <div className={styles['item']}>
                  <span>姓名：</span>
                  {currentUser.patientName}
                </div>
                <div className={styles['item']}>
                  <span>证件号：</span>
                  {IdNoPrivate(currentUser.patientIDCard)}
                </div>
                <div className={styles['item']}>
                  <span>手机号：</span>
                  {currentUser.phone}
                </div>
              </div>
              <div className={styles['detail-list']}>
                <div className={styles['item']}>
                  <span>时间：</span>
                  {dayjs().format('YYYY-MM-DD HH:mm:ss')}
                </div>
                <div className={styles['item']}>
                  <span>西药费：</span>
                  {preSettlementData?.totalAmount}
                </div>
              </div>
            </div>
          </Col>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['pay']].join(' ')}>
              <BlueBgTitle title={'支付信息'}></BlueBgTitle>
              <div className={styles['pay-info']}>
                <div className={styles['item']}>
                  <span>总金额：</span>
                  {preSettlementData?.totalAmount}元
                </div>
                <div className={styles['item']}>
                  <span>医保统筹金额：</span>
                  {preSettlementData?.medicalFound ?? '0'}元
                </div>
                <div className={styles['item']}>
                  <span>医保个账金额：</span>
                  {preSettlementData?.medicalPrivateAmount ?? '0'}元
                </div>
              </div>
              <div className={styles['pay-money']}>
                需支付金额：<span>{preSettlementData?.privateAmount ?? '0'}</span>元
              </div>
            </div>
          </Col>
          <Col span={24}>
            <PayMode
              PAYMENT_TYPE={'delivery'}
              SUCCESS_MODAL_TITLE={'支付成功，请取走凭条'}
              money={money}
              payParams={{
                patientID: currentUser.patientID,
                patientName: currentUser.patientName,
                patientIDCard: currentUser.patientIDCard,
                payBusinessType: '1', // 1门诊支付 2挂号支付 3市民卡充值 4住院预交 6 住院结算
                subject: currentUser.patientName + '的急诊收费',
                spbillCreateIp: window.config.SPBILL_CREATE_IP,
                sysType: '7', // 7 医保终端
                privateAmount: money,
                totalAmount: preSettlementData?.totalAmount,
                medicalPrivateAmount: preSettlementData?.medicalPrivateAmount ?? 0,
                medicalAmount: preSettlementData?.medicalFound ?? 0
              }}
              settlementParams={{
                // 结算参数
                apiPar: preSettlementData?.apiPar,
                patientID: currentUser?.patientID,
                payType: readCardData.mdtrt_cert_type === '1' ? '0' : '1' // 0 自费 1 医保
              }}
              printParams={{
                // 打印参数
                PrintType: '1', // 缴费
                result: printResult
              }}
            />
          </Col>
        </Row>
      </div>
    </>
  )
}

export default Confirm
