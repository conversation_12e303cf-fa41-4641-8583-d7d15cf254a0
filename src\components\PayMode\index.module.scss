.face-pay-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(660px / $pixel);
  // height: calc(120px / $pixel);
  // background: url("../../assets/images/pay/pay-face-bg.png") no-repeat center;
  // background-size: 100% 100%;
  background: linear-gradient(to right, #168AFF, #1764FF);
  border-radius: calc(80px / $pixel);
  box-shadow: 0 6px 15px 0 #A3CBF4;
  margin: 0 auto calc(30px / $pixel);

  // margin-bottom: calc(30px / $pixel) !important;
  >img {
    width: calc(66px / $pixel);
    height: calc(66px / $pixel);
    margin-right: calc(40px / $pixel);
  }

  .text {
    font-size: calc(56px / $pixel);
    line-height: calc(80px / $pixel);
    font-weight: 500;
    letter-spacing: calc(7px / $pixel);
    color: #FFFFFF;
  }
}

.all-pay-wrap {

  // position: absolute;
  // bottom: 0;
  // width: 100%;
  // left: 0;
  // right: 0;
  .all-btn {
    width: calc(260px / $pixel);
    height: calc(80px / $pixel);
    background-color: #1677FF;
    border-radius: calc(50px / $pixel);
    margin: calc(50px / $pixel) auto calc(30px / $pixel);
    text-align: center;
    color: #ffffff;
    line-height: calc(80px / $pixel);
    font-size: calc(42px / $pixel);
  }
}

.others-pay-wrapper {
  background-color: #fff;
  border-radius: calc(17px / $pixel);
  padding: calc(20px / $pixel) calc(30px / $pixel);

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: calc(36px / $pixel);

    >img {
      width: calc(170px / $pixel);
      height: 1px;
    }

    >span {
      font-size: calc(32px / $pixel);
      color: #999;
      line-height: calc(36px / $pixel);
      font-weight: 600;
      // margin: 0 calc(40px / $pixel);
    }
  }

  .item-pay {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    >img {
      margin: calc(50px / $pixel) auto calc(20px / $pixel);
      width: calc(120px / $pixel);
      height: calc(120px / $pixel);
    }

    >p {
      margin: 0;
      margin-top: calc(10px / $pixel);
      font-size: calc(36px / $pixel);
      line-height: calc(36px / $pixel);
      color: #000000;
    }

    &.pay {
      // >img {
      -webkit-animation: free_download 0.8s linear alternate infinite;
      animation: free_download 0.8s linear alternate infinite;
      // }
    }
  }
}

@-webkit-keyframes free_download {
  0% {
    -webkit-transform: scale(0.96);
  }

  100% {
    -webkit-transform: scale(1.1);
  }
}

@keyframes free_download {
  0% {
    transform: scale(0.96);
  }

  100% {
    transform: scale(1.1);
  }
}
