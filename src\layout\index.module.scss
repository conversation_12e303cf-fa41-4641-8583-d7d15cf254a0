.home-wrapper {
  position: relative;
  height: 100vh;
  // background: linear-gradient(#4EA2FC, #4EA2FC, #4e88f6, #4F6AF0, #4F6AF0, #4d60ec, #4e59eb);
  background: linear-gradient(to bottom, #4EA2FC, #4F6AF0);
  padding: 0;
  overflow: hidden;

  .module-wrapper {
    width: 100%;
    background: #FFFFFF;
    border-radius: calc(60px / $pixel) calc(60px / $pixel) 0 0;
    // position: relative;

    // 主页
    &.menu {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: calc(1040px / $pixel);
      border-radius: calc(40px / $pixel) calc(40px /$pixel) 0 0;
    }

    // 其他
    &.others,
    &.identity {
      height: calc(100vh - 50px);
      border-radius: calc(50px / $pixel) calc(50px / $pixel) 0 0;

      &#_module {
        position: relative;
      }
    }

    &.others {
      background-color: #F6F6F6;
    }
  }
}
