@import '@/assets/scss/base.scss';

.chatExample {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

// 通用卡片样式
.doctorCard,
.consultationCard,
.tipsCard,
.faqCard {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  :global {
    .adm-card-header {
      padding: 16px 16px 8px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
    
    .adm-card-body {
      padding: 8px 16px 16px;
    }
  }
}

// 医生信息卡片
.doctorInfo {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.avatar {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.statusBadge {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #52c41a;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  border: 2px solid white;
}

.info {
  flex: 1;
  
  .name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }
  
  .specialty {
    font-size: 14px;
    color: #1890ff;
    margin-bottom: 8px;
  }
  
  .introduction {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
  }
}

// 咨询按钮
.consultationButton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 0 20px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  
  span:first-child {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .price {
    font-size: 14px;
    font-weight: 600;
  }
}

// 温馨提示
.tips {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tipItem {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
}

.tipIcon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

// 常见问题
.faqList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.faqItem {
  .question {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }
  
  .answer {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
    padding-left: 12px;
    border-left: 3px solid #1890ff;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .content {
    padding: 12px;
    gap: 12px;
  }
  
  .doctorInfo {
    gap: 12px;
  }
  
  .avatar {
    width: 60px;
    height: 60px;
  }
  
  .info {
    .name {
      font-size: 16px;
    }
    
    .specialty {
      font-size: 13px;
    }
    
    .introduction {
      font-size: 12px;
    }
  }
  
  .consultationButton {
    height: 48px;
    padding: 0 16px;
    font-size: 15px;
    
    .price {
      font-size: 13px;
    }
  }
  
  .tipItem {
    font-size: 13px;
  }
  
  .faqItem {
    .question {
      font-size: 13px;
    }
    
    .answer {
      font-size: 12px;
    }
  }
}

// 卡片悬停效果
.doctorCard,
.consultationCard,
.tipsCard,
.faqCard {
  transition: box-shadow 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// 按钮悬停效果
.consultationButton {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 动画效果
.doctorCard,
.consultationCard,
.tipsCard,
.faqCard {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 状态徽章动画
.statusBadge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 图标样式优化
:global {
  .antd-mobile-icon {
    font-size: 18px;
  }
}

// 价格标签样式
.price {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  backdrop-filter: blur(4px);
}
