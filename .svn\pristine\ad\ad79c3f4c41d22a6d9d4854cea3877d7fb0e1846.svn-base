import { Row, Col } from 'antd'
import BigButtonItem from './BigButtonItem'
import MidButtonItem from './MidButtonItem'
import styles from '../index.module.scss'

const MenuThree = ({ data, findUrl }) => {
  return (
    <div className={styles['menu3']}>
      <BigButtonItem item={data?.[0] ?? {}} findUrl={findUrl} class_name={'bgred'} />
      <div className={styles['menu3-mid-wrapper']}>
        <Row justify='space-between' gutter={[7, 0]}>
          {data.slice(1, 3).map((item, index) => {
            return (
              <Col span={12} key={index}>
                <MidButtonItem item={item} class_name={'bg' + index} findUrl={findUrl} />
              </Col>
            )
          })}
        </Row>
      </div>
    </div>
  )
}

export default MenuThree
