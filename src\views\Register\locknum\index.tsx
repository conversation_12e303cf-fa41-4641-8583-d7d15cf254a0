import { Row, Col } from 'antd'
import { Button } from 'antd-mobile'
import dayjs from 'dayjs'
import { useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/store'
import ModuleHeader from '@/components/ModuleHeader'
import PayMode from '@/components/PayMode'
import BlueBgTitle from '@/components/BlueBgTitle'
import styles from './index.module.scss'
import { IdNoPrivate } from '@/utils/utils'
import { useEffect } from 'react'
import { getSpeak } from '@/services/hardware'

const Confirm = () => {
  const handleConfirm = () => {}
  const { currentUser, readCardData } = useSelector((state: RootState) => state.userInfo)
  const { selectDept, selectSchedule, selectOrderNum } = useSelector(
    (state: RootState) => state.register
  )
  useEffect(() => {
    window.config.isSpeak && getSpeak({ content: '请确认预约信息' })
  }, [])
  return (
    <>
      <ModuleHeader title={'确认预约'} />
      <div className={styles['confirm-page-wrapper']}>
        <Row gutter={[0, 15]}>
          <Col span={24}>
            <div className={[styles['confirm-info-list-wrapper'], styles['info']].join(' ')}>
              <div className={styles['info-box']}>
                <BlueBgTitle title={'就诊人信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>姓名：</span>
                  {currentUser.patientName}
                </div>
                <div className={styles['item']}>
                  <span>证件号：</span>
                  {IdNoPrivate(currentUser.patientIDCard)}
                </div>
                <div className={styles['item']}>
                  <span>手机号：</span>
                  {currentUser.phone}
                </div>
              </div>
              <div className={styles['detail-list']}>
                <BlueBgTitle title={'号源信息'}></BlueBgTitle>
                <div className={styles['item']}>
                  <span>就诊医院：</span>
                  {window.config.HOS_NAME}
                </div>
                <div className={styles['item']}>
                  <span>就诊科室：</span>
                  {selectDept?.deptName}&nbsp;&nbsp;{selectSchedule?.doctorName}
                </div>
                <div className={styles['item']}>
                  <span>号源类型：</span> {selectSchedule?.doctorType === '2' ? '专家' : '普通'}门诊
                  <b className={styles['money']}>{selectSchedule?.registerAmount}元</b>
                </div>
                <div className={styles['item']}>
                  <span>就诊日期：</span>
                  {dayjs(selectOrderNum?.selectDate).format('YYYY年MM月DD日')}
                </div>
                <div className={styles['item']}>
                  <span>&nbsp;</span>
                  {selectOrderNum?.intervalTime}
                  <b className={styles['number']}>{selectOrderNum?.orderNo}号</b>
                </div>
              </div>
            </div>
          </Col>
          <Col span={24}>
            <PayMode
              PAYMENT_TYPE={'reserve'}
              SUCCESS_MODAL_TITLE={'预约成功，请取走凭条'}
              money={0}
            />
          </Col>
        </Row>
      </div>
    </>
  )
}

export default Confirm
