.confirm-page-wrapper {
  width: calc(660px / $pixel);
  margin: calc(40px / $pixel) auto 0;
  overflow-x: hidden;
}

.confirm-info-list-wrapper {
  position: relative;
  width: 100%;
  padding: calc(0px / $pixel) calc(40px / $pixel);
  background-color: #FFF;
  border-radius: calc(22px / $pixel);
  border: 1px solid #99C4FF;

  &::before {
    position: absolute;
    content: '';
    left: calc(-15px / $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &::after {
    position: absolute;
    content: '';
    right: calc(-15px/ $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &.info {

    &::before,
    &::after {
      top: calc(240px/ $pixel);
    }
  }

  .info-box {
    padding: calc(70px / $pixel) 0 calc(30px / $pixel);
    font-size: calc(28px / $pixel);
    line-height: calc(56px / $pixel);
    color: #383838;
    font-weight: medium;
    border-bottom: 1px solid #CFCFCF;
    position: relative;

    .item {
      span {
        display: inline-block;
        min-width: calc(130px / $pixel);
        margin-right: calc(30px / $pixel);
        color: #A6A6A6;
        font-weight: medium;
      }
    }
  }

  .detail-list {
    padding: calc(70px / $pixel) 0 calc(30px / $pixel);
    font-size: calc(28px / $pixel);
    line-height: calc(56px / $pixel);
    color: #383838;
    font-weight: medium;
    position: relative;

    .item {
      span {
        display: inline-block;
        min-width: calc(130px / $pixel);
        margin-right: calc(30px / $pixel);
        color: #A6A6A6;
        font-weight: medium;
      }

      b {
        &.money {
          color: #FF8D1A;
          margin-left: calc(20px / $pixel);
        }

        &.number {
          color: #1677FF;
          margin-left: calc(20px / $pixel);
        }
      }


    }
  }

  .pay-info {
    padding: 0;
    font-size: calc(28px / $pixel);
    line-height: calc(56px / $pixel);
    color: #383838;
    font-weight: 500;

    .item {
      span {
        display: inline-block;
        min-width: calc(150px / $pixel);
        margin-right: calc(30px / $pixel);
        color: #A6A6A6;
      }
    }
  }

  .pay-money {
    margin-top: calc(20px / $pixel);
    text-align: center;
    font-size: calc(35px / $pixel);
    font-weight: 600;
    color: #383838;

    span {
      color: #FF8D1A;
    }
  }
}
