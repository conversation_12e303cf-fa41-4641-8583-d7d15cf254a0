/*
 * @Description: 翻页组件
 */

import React, { useState } from 'react'
import styles from './index.module.scss'

interface PageType {
  current: number
  pageSize: number
  totalPage: number
  onChange: (type: string, number: number) => void
}

const PageButton: React.FC<PageType> = props => {
  const { current, totalPage, onChange } = props

  //下一页
  const setNext = () => {
    if (current < totalPage) {
      onChange('next', current + 1)
    }
  }

  //上一页
  const setUp = () => {
    if (current > 1) {
      onChange('prev', current - 1)
    }
  }

  return (
    <div className={styles['change-page']}>
      <div onClick={setUp} className={styles['up-btn']}>
        <div className={styles[`arrow-up ${current === 1 ? 'gray' : 'blue'}`]}></div>
      </div>
      <div className={styles['page-size']}>
        <span>第</span>
        <span>{current}</span>
        <span>页</span>
      </div>
      <div onClick={setNext} className={styles['next-btn']}>
        <div className={styles[`arrow-down ${current === totalPage ? 'gray' : 'blue'}`]}></div>
      </div>
    </div>
  )
}

export default PageButton
