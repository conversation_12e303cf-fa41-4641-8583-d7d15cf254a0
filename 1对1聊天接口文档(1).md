# 1对1聊天功能接口文档

## 1. 接口概述

### 1.1 功能描述
本接口提供1对1聊天功能，支持文字消息和图片消息的发送与接收，类似微信的聊天体验。

### 1.2 基础信息
- **服务地址**: `http://域名:8081/wyyx`
- **接口前缀**: `/chat`
- **请求方式**: POST/GET
- **数据格式**: JSON/Form-data
- **字符编码**: UTF-8

### 1.3 通用响应格式
```json
{
  "code": "0",           // 状态码：0-成功，1-参数错误，2-业务错误
  "msg": "请求成功！",    // 响应消息
  "data": {}            // 响应数据
}
```

## 2. 数据模型

### 2.1 会话对象 (ChatConversation)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| conversationId | String | 会话唯一标识 |
| user1Id | String | 用户1的设备号 |
| user2Id | String | 用户2的设备号 |
| lastMessageContent | String | 最后一条消息内容 |
| lastMessageType | Integer | 最后一条消息类型：1-文字，2-图片 |
| lastMessageTime | String | 最后一条消息时间 |
| otherUserId | String | 对方用户ID |
| otherUserName | String | 对方用户昵称 |
| otherUserAvatar | String | 对方用户头像URL |
| unreadCount | Integer | 当前用户未读消息数 |

### 2.2 消息对象 (ChatMessage)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| messageId | String | 消息唯一标识 |
| conversationId | String | 会话ID |
| senderId | String | 发送者设备号 |
| receiverId | String | 接收者设备号 |
| messageType | Integer | 消息类型：1-文字，2-图片 |
| content | String | 消息内容 |
| imageUrl | String | 图片URL（图片消息时使用） |
| fileSize | Long | 文件大小（字节） |
| sendStatus | Integer | 发送状态：1-发送中，2-发送成功，3-发送失败 |
| readStatus | Integer | 阅读状态：0-未读，1-已读 |
| createTime | String | 创建时间 |
| senderName | String | 发送者昵称 |
| senderAvatar | String | 发送者头像URL |

## 3. 接口详情

### 3.1 创建或获取会话

#### 接口信息
- **接口地址**: `/chat/conversation/create`
- **请求方式**: POST
- **接口描述**: 创建或获取两个用户之间的会话

#### 请求参数
```json
{
  "user1Id": "device001",    // 用户1设备号，必填
  "user2Id": "device002"     // 用户2设备号，必填
}
```

#### 响应示例
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": {
    "id": 1,
    "conversationId": "conv_device001_device002_1718776200000",
    "user1Id": "device001",
    "user2Id": "device002",
    "user1UnreadCount": 0,
    "user2UnreadCount": 0,
    "status": 1,
    "createTime": "2025-06-19 10:30:00",
    "updateTime": "2025-06-19 10:30:00"
  }
}
```

#### 错误示例
```json
{
  "code": "2",
  "msg": "用户ID不能为空",
  "data": null
}
```

### 3.2 发送文字消息

#### 接口信息
- **接口地址**: `/chat/message/text`
- **请求方式**: POST
- **接口描述**: 发送文字消息

#### 请求参数
```json
{
  "senderId": "device001",      // 发送者设备号，必填
  "receiverId": "device002",    // 接收者设备号，必填
  "content": "你好，这是一条文字消息"  // 消息内容，必填
}
```

#### 响应示例
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": {
    "id": 1,
    "messageId": "msg1234567890abcdef",
    "conversationId": "conv_device001_device002_1718776200000",
    "senderId": "device001",
    "receiverId": "device002",
    "messageType": 1,
    "content": "你好，这是一条文字消息",
    "sendStatus": 2,
    "readStatus": 0,
    "status": 1,
    "createTime": "2025-06-19 10:31:00",
    "updateTime": "2025-06-19 10:31:00"
  }
}
```

### 3.3 发送图片消息（文件上传）

#### 接口信息
- **接口地址**: `/chat/message/image`
- **请求方式**: POST
- **请求格式**: multipart/form-data
- **接口描述**: 发送图片消息（文件上传方式）

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| senderId | String | 是 | 发送者设备号 |
| receiverId | String | 是 | 接收者设备号 |
| imageFile | File | 是 | 图片文件 |

#### 支持的图片格式
- jpg, jpeg, png, gif, bmp, webp
- 最大文件大小：1024MB

#### 响应示例
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": {
    "id": 2,
    "messageId": "msg1234567890abcdef",
    "conversationId": "conv_device001_device002_1718776200000",
    "senderId": "device001",
    "receiverId": "device002",
    "messageType": 2,
    "content": "[图片]",
    "imageUrl": "/uploads/chat/images/abc123def456.jpg",
    "fileSize": 102400,
    "sendStatus": 2,
    "readStatus": 0,
    "status": 1,
    "createTime": "2025-06-19 10:32:00",
    "updateTime": "2025-06-19 10:32:00"
  }
}
```

### 3.4 发送图片消息（base64格式）

#### 接口信息
- **接口地址**: `/chat/message/image-base64`
- **请求方式**: POST
- **请求格式**: JSON
- **接口描述**: 发送图片消息（base64格式）

#### 请求参数
```json
{
  "senderId": "device001",        // 发送者设备号，必填
  "receiverId": "device002",      // 接收者设备号，必填
  "imageBase64": "iVBORw0KGgoAAAANSUhEUgAA...",  // 图片base64数据，必填
  "imageType": "png"              // 图片类型，必填（jpg, jpeg, png, gif, bmp, webp）
}
```

#### 支持的图片格式
- jpg, jpeg, png, gif, bmp, webp

#### 响应示例
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": {
    "id": 3,
    "messageId": "msg1234567890abcdef",
    "conversationId": "conv_device001_device002_1718776200000",
    "senderId": "device001",
    "receiverId": "device002",
    "messageType": 2,
    "content": "[图片]",
    "imageUrl": "/uploads/chat/images/abc123def456.png",
    "fileSize": 98304,
    "sendStatus": 2,
    "readStatus": 0,
    "status": 1,
    "createTime": "2025-06-19 10:33:00",
    "updateTime": "2025-06-19 10:33:00"
  }
}
```

### 3.5 获取聊天记录

#### 接口信息
- **接口地址**: `/chat/message/history`
- **请求方式**: GET
- **接口描述**: 获取指定会话的聊天记录（分页）

#### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| conversationId | String | 是 | - | 会话ID |
| page | Integer | 否 | 1 | 页码 |
| size | Integer | 否 | 20 | 每页大小 |

#### 请求示例
```
GET /chat/message/history?conversationId=conv_device001_device002_1718776200000&page=1&size=20
```

#### 响应示例
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": [
    {
      "id": 2,
      "messageId": "msg1234567890abcdef",
      "conversationId": "conv_device001_device002_1718776200000",
      "senderId": "device001",
      "receiverId": "device002",
      "messageType": 1,
      "content": "你好",
      "sendStatus": 2,
      "readStatus": 1,
      "createTime": "2025-06-19 10:31:00",
      "senderName": "张三",
      "senderAvatar": "http://example.com/avatar1.jpg"
    },
    {
      "id": 1,
      "messageId": "msg0987654321fedcba",
      "conversationId": "conv_device001_device002_1718776200000",
      "senderId": "device002",
      "receiverId": "device001",
      "messageType": 2,
      "content": "[图片]",
      "imageUrl": "/uploads/chat/images/abc123def456.jpg",
      "fileSize": 102400,
      "sendStatus": 2,
      "readStatus": 0,
      "createTime": "2025-06-19 10:30:00",
      "senderName": "李四",
      "senderAvatar": "http://example.com/avatar2.jpg"
    }
  ]
}
```

### 3.6 获取会话列表

#### 接口信息
- **接口地址**: `/chat/conversation/list`
- **请求方式**: GET
- **接口描述**: 获取用户的会话列表（分页）

#### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| userId | String | 是 | - | 用户设备号 |
| page | Integer | 否 | 1 | 页码 |
| size | Integer | 否 | 20 | 每页大小 |

#### 请求示例
```
GET /chat/conversation/list?userId=device001&page=1&size=20
```

#### 响应示例
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": [
    {
      "id": 1,
      "conversationId": "conv_device001_device002_1718776200000",
      "user1Id": "device001",
      "user2Id": "device002",
      "lastMessageContent": "你好",
      "lastMessageType": 1,
      "lastMessageTime": "2025-06-19 10:31:00",
      "otherUserId": "device002",
      "otherUserName": "李四",
      "otherUserAvatar": "http://example.com/avatar2.jpg",
      "unreadCount": 3,
      "createTime": "2025-06-19 10:30:00",
      "updateTime": "2025-06-19 10:31:00"
    }
  ]
}
```

### 3.7 标记消息为已读

#### 接口信息
- **接口地址**: `/chat/message/read`
- **请求方式**: POST
- **接口描述**: 标记指定会话中的消息为已读

#### 请求参数
```json
{
  "conversationId": "conv_device001_device002_1718776200000",  // 会话ID，必填
  "userId": "device001"                                       // 用户设备号，必填
}
```

#### 响应示例
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": "标记已读成功"
}
```

### 3.8 获取未读消息数

#### 接口信息
- **接口地址**: `/chat/message/unread-count`
- **请求方式**: GET
- **接口描述**: 获取指定会话中用户的未读消息数

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| conversationId | String | 是 | 会话ID |
| userId | String | 是 | 用户设备号 |

#### 请求示例
```
GET /chat/message/unread-count?conversationId=conv_device001_device002_1718776200000&userId=device001
```

#### 响应示例
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": 5
}
```

### 3.9 获取消息详情

#### 接口信息
- **接口地址**: `/chat/message/detail`
- **请求方式**: GET
- **接口描述**: 根据消息ID获取消息详情

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| messageId | String | 是 | 消息ID |

#### 请求示例
```
GET /chat/message/detail?messageId=msg1234567890abcdef
```

#### 响应示例
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": {
    "id": 1,
    "messageId": "msg1234567890abcdef",
    "conversationId": "conv_device001_device002_1718776200000",
    "senderId": "device001",
    "receiverId": "device002",
    "messageType": 1,
    "content": "你好",
    "sendStatus": 2,
    "readStatus": 1,
    "createTime": "2025-06-19 10:31:00",
    "senderName": "张三",
    "senderAvatar": "http://example.com/avatar1.jpg"
  }
}
```

### 3.10 获取会话详情

#### 接口信息
- **接口地址**: `/chat/conversation/detail`
- **请求方式**: GET
- **接口描述**: 根据会话ID获取会话详情

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| conversationId | String | 是 | 会话ID |

#### 请求示例
```
GET /chat/conversation/detail?conversationId=conv_device001_device002_1718776200000
```

#### 响应示例
```json
{
  "code": "0",
  "msg": "请求成功！",
  "data": {
    "id": 1,
    "conversationId": "conv_device001_device002_1718776200000",
    "user1Id": "device001",
    "user2Id": "device002",
    "lastMessageContent": "你好",
    "lastMessageType": 1,
    "lastMessageTime": "2025-06-19 10:31:00",
    "user1UnreadCount": 0,
    "user2UnreadCount": 3,
    "status": 1,
    "createTime": "2025-06-19 10:30:00",
    "updateTime": "2025-06-19 10:31:00"
  }
}
```

## 4. 错误码说明

| 错误码 | 说明 | 常见原因 |
|--------|------|----------|
| 0 | 成功 | 请求处理成功 |
| 1 | 参数错误 | 必填参数为空或格式不正确 |
| 2 | 业务错误 | 业务逻辑错误，如用户不存在、文件格式不支持等 |

## 5. 常见错误示例

### 5.1 参数为空
```json
{
  "code": "2",
  "msg": "用户ID不能为空",
  "data": null
}
```

### 5.2 文件格式不支持
```json
{
  "code": "2",
  "msg": "只支持图片文件",
  "data": null
}
```

### 5.3 会话不存在
```json
{
  "code": "2",
  "msg": "会话不存在",
  "data": null
}
```

### 5.4 消息不存在
```json
{
  "code": "2",
  "msg": "消息不存在",
  "data": null
}
```

## 6. 使用说明

### 6.1 基本流程
1. **创建会话**: 调用创建会话接口，获取会话ID
2. **发送消息**: 使用会话中的用户ID发送文字或图片消息
3. **获取消息**: 通过会话ID获取聊天记录
4. **标记已读**: 用户查看消息后标记为已读
5. **会话管理**: 获取用户的所有会话列表

### 6.2 注意事项
1. **用户标识**: 使用设备号(deviceSn)作为用户唯一标识
2. **图片上传**: 图片文件会保存到服务器本地，返回相对路径
3. **分页查询**: 聊天记录和会话列表都支持分页，按时间倒序返回
4. **消息状态**: 消息有发送状态和阅读状态两个维度的状态管理
5. **未读计数**: 系统自动维护每个用户在每个会话中的未读消息数

### 6.3 最佳实践
1. **轮询获取**: 客户端可定期调用获取聊天记录接口获取新消息
2. **本地缓存**: 建议客户端缓存会话列表和聊天记录，减少网络请求
3. **图片预览**: 可以根据imageUrl字段显示图片预览
4. **错误处理**: 根据返回的错误码和错误信息进行相应的错误处理

## 7. 测试用例

### 7.1 Postman测试集合
可以使用以下测试用例验证接口功能：

1. **创建会话测试**
   - 正常创建会话
   - 重复创建同一会话
   - 参数为空的情况

2. **发送消息测试**
   - 发送文字消息
   - 发送图片消息
   - 发送空内容消息

3. **查询测试**
   - 获取聊天记录
   - 获取会话列表
   - 分页查询测试

4. **状态管理测试**
   - 标记消息已读
   - 获取未读消息数

### 7.2 Swagger文档
项目集成了Swagger，可以通过以下地址访问在线API文档：
```
http://域名:8081/wyyx/swagger-ui.html
```

## 8. 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2025-06-19 | 初始版本，支持基础的1对1聊天功能 |

---

**文档维护**: 开发团队
**最后更新**: 2025-06-19
**联系方式**: 如有问题请联系开发团队
```
