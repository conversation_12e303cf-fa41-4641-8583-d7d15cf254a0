.doctor-wrapper {
  height: calc(100vh - 50px);
  // background-image: linear-gradient(#2493F1, #A8D7FF);
  // background-size: 100% calc(260px / $pixel);
  // background-repeat: no-repeat;
  position: relative;
  z-index: 1;

  &::after {
    content: "";
    width: 140%;
    height: calc(260px / $pixel);
    position: absolute;
    left: -20%;
    top: 0;
    border-radius: 0 0 60% 60%;
    background: linear-gradient(#2493F1, #A8D7FF);
    z-index: -1;
  }

  .date-wrapper {
    background-color: #fff;
    width: calc(670px / $pixel);
    margin: calc(30px / $pixel) auto 0;
    height: calc(160px / $pixel);

    :global {
      .adm-jumbo-tabs-tab-description {
        margin-top: calc(25px / $pixel);
      }
    }
  }

  .pt-doctor-wrapper {
    background-color: #fff;
    width: calc(670px / $pixel);
    margin: calc(30px / $pixel) auto 0;
    border-radius: calc(16px / $pixel);
    padding: calc(10px / $pixel) calc(30px / $pixel);

    .title {
      font-size: calc(30px / $pixel);
      color: #383838;
      font-weight: 600;
    }

    .schdule {
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 100%;
      height: calc(60px / $pixel);
      line-height: calc(60px / $pixel);
      border: 1px solid #1677FF;
      border-radius: calc(14px / $pixel);
      margin: calc(15px / $pixel) 0;

      &>.button {
        width: calc(120px / $pixel);
        height: calc(40px / $pixel);
        line-height: calc(40px / $pixel);
        color: #fff;
        background-color: #1677FF;
        border-radius: calc(40px / $pixel);
        text-align: center;
      }

      &.stopped {
        border: none;
        background-color: #EBEBEB;

        &>.button {
          color: #fff;
          background-color: #CCCCCC;
        }
      }

    }
  }

  .zj-doctor-wrapper {
    background-color: #fff;
    width: calc(670px / $pixel);
    margin: calc(30px / $pixel) auto 0;
    border-radius: calc(16px / $pixel);
    padding: calc(10px / $pixel) calc(30px / $pixel);

    .title {
      font-size: calc(30px / $pixel);
      color: #383838;
      font-weight: 600;
    }

    .doctor-schdule {
      margin-top: calc(20px / $pixel);
      border-bottom: 1px solid #E5E5E5;

      &:last-child {
        border-bottom: none;
      }

      .doctor-info {
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0 calc(28px / $pixel);
        margin-bottom: calc(22px / $pixel);
        background-color: #FFFFFF;
        border-radius: calc(14px / $pixel);

        .doctor-image {
          width: calc(85px / $pixel);
          height: calc(105px / $pixel);
          border-radius: 50%;
          margin-right: calc(45px / $pixel);
        }

        .name-box {
          display: flex;
          align-items: center;
          margin-bottom: calc(12px / $pixel);

          .name {
            font-size: calc(38px / $pixel);
            line-height: calc(50px / $pixel);
            color: #333333;
            margin-bottom: 0;
            margin-right: 5px;
          }

          .job {
            height: calc(30px / $pixel);
            padding: 0 calc(16px / $pixel);
            border: 1px solid #1677FF;
            border-radius: calc(10px / $pixel);
            line-height: calc(26px / $pixel);
            font-size: calc(20px / $pixel);
            color: #1677FF;
          }
        }

        .content {
          width: calc(385px / $pixel);
          height: calc(29px / $pixel);
          font-size: calc(20px / $pixel);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }


      .schdule {
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 100%;
        height: calc(60px / $pixel);
        line-height: calc(60px / $pixel);
        border: 1px solid #1677FF;
        border-radius: calc(14px / $pixel);
        margin: calc(15px / $pixel) 0;

        &>.button {
          width: calc(120px / $pixel);
          height: calc(40px / $pixel);
          line-height: calc(40px / $pixel);
          color: #fff;
          background-color: #1677FF;
          border-radius: calc(40px / $pixel);
          text-align: center;
        }

        &.stopped {
          border: none;
          background-color: #EBEBEB;

          &>.button {
            color: #fff;
            background-color: #CCCCCC;
          }
        }

        &:last-child {
          margin-bottom: calc(30px / $pixel);
        }
      }
    }
  }
}
