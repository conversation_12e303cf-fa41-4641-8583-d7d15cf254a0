/*
 * @Author: hjw
 * @Date: 2025-04-17 09:05:17
 * @Description: 用户建档查档相关接口
 */
import request from '@/utils/request'

// 查询病人档案
export async function getUserInfo(params: any, isHideError: boolean) {
  return request({
    url: '/tzjj/BingRenXXCX',
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE
    },
    isReturnErrorRes: true,
    isHideError,
    loadingText: '查询患者信息中，请稍候...'
  })
}

// 查询医保档案
export async function getUserMedInfo(params: any) {
  return request({
    url: '/tzjj/YiBaoRenYuanXX',
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE
    },
    isReturnErrorRes: true,
    loadingText: '查询患者医保信息中，请稍候...'
  })
}

// 建档
export async function createUser(params: any) {
  return request({
    url: '/qzBasic/createUser',
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    }
  })
}

// 查询医生信息
export async function getDoctorInfo(params: any) {
  return request({
    url: '/tzjj/YongHuYZ',
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE
    }
  })
}

// 查询住院记录
export async function getInHospitalList(params: any) {
  return request({
    url: '/tzjj/inHospitalList',
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE
    }
  })
}

