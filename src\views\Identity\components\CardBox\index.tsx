import idCard from '@/assets/images/cards/id-card.png' // 身份证
import socialCard from '@/assets/images/cards/social-card.png' // 社保卡
import elMedCard from '@/assets/images/cards/el-med-card.png' // 医保电子凭证
import elHealthCard from '@/assets/images/cards/el-health-card.png' // 电子健康卡
import input from '@/assets/images/cards/input-patient-id.png' // 手输就诊号
import inhosInput from '@/assets/images/cards/input-hospital-id.png' // 手输住院号
import face from '@/assets/images/cards/face.png' // 刷脸
import faceSelf from '@/assets/images/cards/face-self.png' // 自费刷脸
import foreignerCard from '@/assets/images/cards/foreigner-card.png' // 外国人永居证
import socialCardzh from '@/assets/images/cards/social-card-zh.png' // 社保卡未上线
import elMedCardzh from '@/assets/images/cards/el-med-card-zh.png' // 电子凭证未上线
import jzkCard from '@/assets/images/cards/jzk.png' // 健康卡 （目前是台州的卡面，替换图片即可）
import printTest from '@/assets/images/cards/print-test.png' // 打印测试按钮

import styles from './index.module.scss'

const CARD_IMG = {
  elMedCard,
  idCard,
  socialCard,
  input,
  elHealthCard,
  socialCardzh,
  elMedCardzh,
  inhosInput,
  printTest,
  face,
  faceSelf,
  jzkCard,
  foreignerCard
}

const CardBox = ({ title, type, isRecommend = false, isTest = false }) => {
  return (
    <div className={[styles['identity-item'], type === 'face' && styles['pay']].join(' ')}>
      <img src={CARD_IMG[type]} className={styles['card-img']} alt='' />
      <div className={styles['text']}>
        <span>{title}</span>
      </div>
      <div className={styles['tuijian-box']}>
        {isRecommend && (
          <img src={require('@/assets/images/others/tuijian.png')} alt='推荐使用图标' />
        )}
        {isTest && <img src={require('@/assets/images/others/test.png')} alt='测试图标' />}
      </div>
    </div>
  )
}

export default CardBox
