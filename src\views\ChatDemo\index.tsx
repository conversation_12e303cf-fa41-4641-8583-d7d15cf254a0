import React, { useState } from 'react';
import { Button, Space, Card } from 'antd-mobile';
import { MessageOutline, UserContactOutline } from 'antd-mobile-icons';
import ModuleHeader from '@/components/ModuleHeader';
import Chat from '@/views/Chat';
import styles from './index.module.scss';

const ChatDemo: React.FC = () => {
  const [showFullPageChat, setShowFullPageChat] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState<{id: string, name: string} | null>(null);

  // 模拟医生数据
  const doctors = [
    {
      id: '1',
      name: '张医生',
      specialty: '内科',
      avatar: '/api/placeholder/60/60',
      status: '在线'
    },
    {
      id: '2', 
      name: '李医生',
      specialty: '外科',
      avatar: '/api/placeholder/60/60',
      status: '在线'
    },
    {
      id: '3',
      name: '王医生', 
      specialty: '儿科',
      avatar: '/api/placeholder/60/60',
      status: '忙碌'
    }
  ];

  const handleStartChat = (doctorId: string, doctorName: string) => {
    setSelectedDoctor({ id: doctorId, name: doctorName });
    setShowFullPageChat(true);
  };

  const handleOpenFullPageChat = () => {
    setSelectedDoctor({ id: '1', name: '张医生' });
    setShowFullPageChat(true);
  };

  if (showFullPageChat && selectedDoctor) {
    return (
      <Chat
        doctorId={selectedDoctor.id}
        patientId="device001"
        doctorName={selectedDoctor.name}
      />
    );
  }

  return (
    <div className={styles.chatDemo}>
      <ModuleHeader title="在线咨询演示" />
      
      <div className={styles.content}>
        {/* 功能演示区域 */}
        <Card title="聊天功能演示" className={styles.demoCard}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              color="primary"
              size="large"
              block
              onClick={handleOpenFullPageChat}
            >
              <MessageOutline />
              开始聊天
            </Button>
          </Space>
        </Card>

        {/* 在线医生列表 */}
        <Card title="在线医生" className={styles.doctorCard}>
          <div className={styles.doctorList}>
            {doctors.map((doctor) => (
              <div key={doctor.id} className={styles.doctorItem}>
                <div className={styles.doctorInfo}>
                  <div className={styles.avatar}>
                    <img src={doctor.avatar} alt={doctor.name} />
                  </div>
                  <div className={styles.info}>
                    <div className={styles.name}>{doctor.name}</div>
                    <div className={styles.specialty}>{doctor.specialty}</div>
                    <div className={`${styles.status} ${
                      doctor.status === '在线' ? styles.online : styles.busy
                    }`}>
                      {doctor.status}
                    </div>
                  </div>
                </div>
                <div className={styles.actions}>
                  <Button
                    size="small"
                    color="primary"
                    onClick={() => handleStartChat(doctor.id, doctor.name)}
                    disabled={doctor.status !== '在线'}
                  >
                    咨询
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* 使用说明 */}
        <Card title="使用说明" className={styles.instructionCard}>
          <div className={styles.instructions}>
            <h4>组件特性：</h4>
            <ul>
              <li>支持文本和图片消息发送</li>
              <li>移动端优化的界面设计</li>
              <li>消息状态显示（发送中、已送达、发送失败）</li>
              <li>自动滚动到最新消息</li>
              <li>图片预览和上传功能</li>
              <li>响应式设计，适配不同屏幕尺寸</li>
            </ul>
            
            <h4>使用方式：</h4>
            <ul>
              <li><strong>Chat</strong>：全屏页面形式，完整的聊天体验</li>
              <li>支持轮询获取新消息，保持聊天实时性</li>
              <li>下拉刷新和上拉加载更多历史记录</li>
            </ul>
            
            <h4>接口集成：</h4>
            <ul>
              <li>使用 <code>src/services/chat.ts</code> 中的接口</li>
              <li>基于1对1聊天接口文档实现</li>
              <li>支持发送文本消息和图片消息</li>
              <li>可获取聊天历史记录</li>
              <li>支持创建聊天会话</li>
              <li>轮询机制获取新消息</li>
            </ul>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ChatDemo;
