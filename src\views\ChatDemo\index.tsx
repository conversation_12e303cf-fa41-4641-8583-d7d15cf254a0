import React, { useState } from 'react';
import { Button, Space, Card } from 'antd-mobile';
import { MessageOutline, UserContactOutline } from 'antd-mobile-icons';
import ModuleHeader from '@/components/ModuleHeader';
import Chat from '@/views/Chat';
import styles from './index.module.scss';

const ChatDemo: React.FC = () => {
  const [showFullPageChat, setShowFullPageChat] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState<{id: string, name: string} | null>(null);

  // 模拟医生数据
  const doctors = [
    {
      id: '1',
      name: '张医生',
      specialty: '内科',
      avatar: '/api/placeholder/60/60',
      status: '在线'
    },
    {
      id: '2', 
      name: '李医生',
      specialty: '外科',
      avatar: '/api/placeholder/60/60',
      status: '在线'
    },
    {
      id: '3',
      name: '王医生', 
      specialty: '儿科',
      avatar: '/api/placeholder/60/60',
      status: '忙碌'
    }
  ];

  const handleStartChat = (doctorId: string, doctorName: string) => {
    setSelectedDoctor({ id: doctorId, name: doctorName });
    setShowFullPageChat(true);
  };

  const handleOpenFullPageChat = () => {
    setSelectedDoctor({ id: '1', name: '医生' });
    setShowFullPageChat(true);
  };

  if (showFullPageChat && selectedDoctor) {
    return (
      <Chat
        doctorId={'zjadmin'}
        patientId={'81_2960755_jws001'}
        doctorName={selectedDoctor.name}
      />
    );
  }

  return (
    <div className={styles.chatDemo}>
      <ModuleHeader title="在线咨询演示" />
      
      <div className={styles.content}>
        {/* 功能演示区域 */}
        <Card title="聊天功能演示" className={styles.demoCard}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              color="primary"
              size="large"
              block
              onClick={handleOpenFullPageChat}
            >
              <MessageOutline />
              开始聊天
            </Button>
          </Space>
        </Card>
      </div>
    </div>
  );
};

export default ChatDemo;
