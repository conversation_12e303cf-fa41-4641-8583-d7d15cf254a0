import React, { useState } from 'react';
import { Button, Space, Card } from 'antd-mobile';
import { MessageOutline, UserContactOutline } from 'antd-mobile-icons';
import ModuleHeader from '@/components/ModuleHeader';
import ChatModal from '@/components/ChatModal';
import ChatWindow from '@/components/ChatWindow';
import Chat from '@/views/Chat';
import styles from './index.module.scss';

const ChatDemo: React.FC = () => {
  const [chatModalVisible, setChatModalVisible] = useState(false);
  const [chatWindowVisible, setChatWindowVisible] = useState(false);
  const [showFullPageChat, setShowFullPageChat] = useState(false);

  // 模拟医生数据
  const doctors = [
    {
      id: '1',
      name: '张医生',
      specialty: '内科',
      avatar: '/api/placeholder/60/60',
      status: '在线'
    },
    {
      id: '2', 
      name: '李医生',
      specialty: '外科',
      avatar: '/api/placeholder/60/60',
      status: '在线'
    },
    {
      id: '3',
      name: '王医生', 
      specialty: '儿科',
      avatar: '/api/placeholder/60/60',
      status: '忙碌'
    }
  ];

  const handleOpenChatModal = (doctorId: string, doctorName: string) => {
    setChatModalVisible(true);
  };

  const handleOpenChatWindow = (doctorId: string, doctorName: string) => {
    setChatWindowVisible(true);
  };

  const handleOpenFullPageChat = () => {
    setShowFullPageChat(true);
  };

  if (showFullPageChat) {
    return <Chat doctorId="1" sessionId="session_001" />;
  }

  return (
    <div className={styles.chatDemo}>
      <ModuleHeader title="在线咨询演示" />
      
      <div className={styles.content}>
        {/* 功能演示区域 */}
        <Card title="聊天组件演示" className={styles.demoCard}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              color="primary"
              size="large"
              block
              onClick={() => handleOpenChatModal('1', '张医生')}
            >
              <MessageOutline />
              打开聊天模态框
            </Button>
            
            <Button
              color="success"
              size="large"
              block
              onClick={() => handleOpenChatWindow('1', '张医生')}
            >
              <MessageOutline />
              打开聊天窗口
            </Button>
            
            <Button
              color="warning"
              size="large"
              block
              onClick={handleOpenFullPageChat}
            >
              <MessageOutline />
              打开全屏聊天页面
            </Button>
          </Space>
        </Card>

        {/* 在线医生列表 */}
        <Card title="在线医生" className={styles.doctorCard}>
          <div className={styles.doctorList}>
            {doctors.map((doctor) => (
              <div key={doctor.id} className={styles.doctorItem}>
                <div className={styles.doctorInfo}>
                  <div className={styles.avatar}>
                    <img src={doctor.avatar} alt={doctor.name} />
                  </div>
                  <div className={styles.info}>
                    <div className={styles.name}>{doctor.name}</div>
                    <div className={styles.specialty}>{doctor.specialty}</div>
                    <div className={`${styles.status} ${
                      doctor.status === '在线' ? styles.online : styles.busy
                    }`}>
                      {doctor.status}
                    </div>
                  </div>
                </div>
                <div className={styles.actions}>
                  <Button
                    size="small"
                    color="primary"
                    onClick={() => handleOpenChatModal(doctor.id, doctor.name)}
                    disabled={doctor.status !== '在线'}
                  >
                    咨询
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* 使用说明 */}
        <Card title="使用说明" className={styles.instructionCard}>
          <div className={styles.instructions}>
            <h4>组件特性：</h4>
            <ul>
              <li>支持文本和图片消息发送</li>
              <li>移动端优化的界面设计</li>
              <li>消息状态显示（发送中、已送达、发送失败）</li>
              <li>自动滚动到最新消息</li>
              <li>图片预览和上传功能</li>
              <li>响应式设计，适配不同屏幕尺寸</li>
            </ul>
            
            <h4>三种使用方式：</h4>
            <ul>
              <li><strong>ChatModal</strong>：模态框形式，带倒计时自动关闭</li>
              <li><strong>ChatWindow</strong>：窗口形式，可手动关闭</li>
              <li><strong>Chat</strong>：全屏页面形式，完整的聊天体验</li>
            </ul>
            
            <h4>接口集成：</h4>
            <ul>
              <li>使用 <code>src/services/api/chat.ts</code> 中的接口</li>
              <li>支持发送文本消息和图片消息</li>
              <li>可获取聊天历史记录</li>
              <li>支持创建聊天会话</li>
            </ul>
          </div>
        </Card>
      </div>

      {/* 聊天模态框 */}
      <ChatModal
        visible={chatModalVisible}
        onClose={() => setChatModalVisible(false)}
        doctorId="1"
        doctorName="张医生"
        sessionId="session_001"
        modalCloseNum={30}
      />

      {/* 聊天窗口 */}
      <ChatWindow
        visible={chatWindowVisible}
        onClose={() => setChatWindowVisible(false)}
        doctorId="1"
        doctorName="张医生"
        sessionId="session_001"
      />
    </div>
  );
};

export default ChatDemo;
