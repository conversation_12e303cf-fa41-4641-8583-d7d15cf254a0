import React, {
  useContext,
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import type { GetRef, InputRef } from "antd";
import { Form, Input, Table, Empty, InputNumber } from "antd";
import { Stepper } from "antd-mobile";
import MultipleModal from "@/components/MultipleModal";
import "./index.scss";

type FormInstance<T> = GetRef<typeof Form<T>>;
interface Item {
  index: number;
  key: string;
  name: string;
  age: string;
  address: string;
  usage: string;
  unit?: string;
}
interface EditableRowProps {
  index: number;
}

//单元格组件 EditableCell 参数设置属性
interface EditableCellProps {
  title: any;
  emptyText: string;
  editable: boolean;
  dataIndex: keyof Item;
  record: Item;
  myusages: any;
  isStepper?: any;
  handleSave: (record: Item) => void;
}
type EditableTableProps = Parameters<typeof Table>[0];
interface DataType {
  key: React.Key;
  name: string;
  price: string;
  age?: string;
  address?: string;
  amount: string;
  unit: string;
  myusages: any;
  ypmc?: string, //药品名称
  sccj?: string, //厂家
  ypgg?: string, //规格
  num: any,
}
type ColumnTypes = Exclude<EditableTableProps["columns"], undefined>;

// EditTable参数设置属性
type EditTable = {
  dataSource?: DataType[];
  setDataSource?: (data: DataType[]) => void;
  onSelectAll?: (data: any) => void;
  tableColumns?: (ColumnTypes[number] & {
    editable?: boolean;
    dataIndex: string;
  })[];
  ref?: any,
  usages?: any,
  emptyText?: string,
  type?: any,
  height?: string,
  isStepper?: boolean;
};

const EditableContext = React.createContext<FormInstance<any> | null>(null);

// 单元格组件
const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
  title,
  emptyText,
  editable,
  children,
  dataIndex,
  record,
  myusages,
  isStepper,
  handleSave,
  ...restProps
}) => {
  const [editing, setEditing] = useState(false);
  const [stepperNum, setStepperNum] = useState<any>(1);
  const inputRef = useRef<InputRef>(null);
  const form = useContext(EditableContext)!;
  const [multipleModalVisible, setMultipleModalVisible] = useState(false);
  useEffect(() => {
    if (editing) {
      inputRef.current?.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing); 
    // save
    form.setFieldsValue({ [dataIndex]: record[dataIndex] });
  };

  const save = async () => {
    try {
      const values = await form.validateFields();
      console.log("values", values);
      toggleEdit();
      handleSave({ ...record, ...values });
    } catch (errInfo) {
      console.log("Save failed:", errInfo);
    }
  };

  const _saveTwo = async () => {
    try {
      const values = await form.validateFields();
      const key = Object.keys(values)[0];
      toggleEdit();
      handleSave({ ...record, ...{ [key]: stepperNum } });
    } catch (errInfo) {
      console.log("Save failed:", errInfo);
    }
  };

  const styles = {
    margin: 0,
    padding: "5px",
    width: 80,
  };
  let childNode = children;
  if (editable) {
  childNode = 
  //  editing ? (
      <Form.Item style={{ margin: 0 }} name={dataIndex}>
        {
          isStepper ? <div
            onFocus={() => { console.log('获得焦点') }}
            // onBlur={() => { _saveTwo() }}
            onClick={() => {
              if (title === "用药频次") {
                setMultipleModalVisible(true);
              }
            }}
          >
            <Stepper
              defaultValue={stepperNum}
              max={window.config.STEPPER_NUM}
              min={1}
              // onChange={(val) => setStepperNum(val)}
              onChange={async (val) => {
                const values = await form.validateFields();
                const key = Object.keys(values)[0];
                console.log(key, val)
                // toggleEdit();
                console.log({ ...record, ...{ [key]: val } })
                handleSave({ ...record, ...{ [key]: val } });
              }}
              formatter={value => `${value}${record.unit}`}
            />
          </div> : <Input
            style={styles}
            ref={inputRef}
            readOnly={title === "用药频次"}
            onPressEnter={save} // 按下回车的回调
            onBlur={save} // 失去焦点
            onClick={() => {
              if (title === "用药频次") {
                setMultipleModalVisible(true);
              }
            }}
          />
        }
      </Form.Item>
  //   ) : (
  //     <div className="editable-cell-value-wrap" onClick={toggleEdit}>
  //       {children}
  //     </div>
  //   );
  }

  return (
    <>
      <td {...restProps}>{childNode}</td>
      {/* 选择框 */}
      <MultipleModal
        title="选择用药频次"
        visibleListPopup={multipleModalVisible}
        isMultiple={false} //是否是多选
        vipTypes={myusages}
        onConfirm={(val: any) => {
          setMultipleModalVisible(false);
          form.setFieldsValue({ usage: val[0].name });
          handleSave({ ...record, usage: val[0].name });
        }}
        onCancel={() => setMultipleModalVisible(false)}
      />
    </>
  );
};

const EditableRow: React.FC<EditableRowProps> = ({ ...props }: any) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

const EditTable: React.FC<EditTable> = forwardRef((props: any, ref: any) => {
  const [selected, setSelected] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const handleSave = (row: DataType) => {
    console.log('触发保存', row)
    const newData = [...props?.dataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      ...row,
    });
    props?.setDataSource(newData);
  };
  //清空选中项
  const clearSelection = () => {
    setSelected([]);
    setSelectedRowKeys([]);
  };
  // 暴漏给父组件
  useImperativeHandle(ref, () => ({
    clearSelection,
  }));
  const action = {
    selectedRowKeys, //指定选中项的 key 数组，需要和 onChange 进行配合
    onChange: (newSelectedRowKeys: React.Key[], newSelectedRows: any[]) => {
      if (newSelectedRowKeys.length && newSelectedRows.length) {
        setSelected(newSelectedRows);
        setSelectedRowKeys(newSelectedRowKeys);
      } else {
        setSelected([]);
      }
      props.onSelectAll(newSelectedRows);
    },
    onSelect: (record: any, selected: any, selectedRows: any) => {
      if (!selected && !selectedRows.length) if (!selected) clearSelection();
    },
    onSelectAll: (selected: boolean) => {
      if (!selected) clearSelection();
    },
    //设置是否可选
    // getCheckboxProps: (record: any) => {
    //   return {
    //     name: record.key,
    //     disabled: record?.printCount > window.config.report_print_count, //打印限制次数
    //   };
    // },
  };
  const columns = props.tableColumns.map((val: any) => {
    if (!val.editable) {
      return val;
    }
    return {
      ...val,
      //给单元格 EditableCell 传参数
      onCell: (record: DataType) => {
        return {
          record,
          editable: val.editable,
          dataIndex: val.dataIndex,
          title: val.title,
          myusages: props?.usages,
          isStepper: props?.isStepper,
          handleSave,
        };
      },
    };
  });

  const rowSelection: any = {
    defaultSelectedRowKeys: props?.defaultSelectedRowKeys,//设置默认选中
    type: props?.type, //设置 多选 checkbox 单选radio 
    columnWidth: 30,
    ...action,
  };
  const locale = {
    emptyText: (
      <>
        <Empty
          description={props?.emptyText}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </>
    ),
  };
  return (
    <div>
      <Table
        rowClassName={() => "editable-row"}
        // style={{ maxHeight: props?.height, overflow: "auto" }}
        rowSelection={rowSelection}
        components={{
          body: {
            row: EditableRow,
            cell: EditableCell,
          },
        }}
        bordered={false}
        dataSource={props.dataSource}
        columns={columns as ColumnTypes}
        pagination={false}
        size="small"
        locale={locale}
      />
    </div>
  );
});

export default EditTable;
