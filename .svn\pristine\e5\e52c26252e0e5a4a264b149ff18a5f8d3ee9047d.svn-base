/*
 * @Description:
 */
import { useState, useLayoutEffect, useEffect } from 'react'
import { Outlet, useLocation } from 'react-router-dom'
import { Layout } from 'antd'
import Header from './components/Header'
import Ad from './components/Ad'
import Footer from './components/Footer'
import styles from './index.module.scss'

const { Content } = Layout

const HomePage = () => {
  const [headerType, setHeaderType] = useState('menu')
  const { pathname } = useLocation()

  useLayoutEffect(() => {
    if (pathname.indexOf('menu') > -1) {
      setHeaderType('menu')
    } else if (pathname.indexOf('identity') > -1) {
      setHeaderType('identity')
    } else {
      setHeaderType('others')
    }
  }, [pathname])

  return (
    <Layout className={styles['home-wrapper']}>
      <Header />
      {pathname.indexOf('menu') > -1 && <Ad />}
      <Content>
        <div className={[styles[`module-wrapper`], styles[`${headerType}`]].join(' ')} id='_module'>
          <Outlet />
        </div>
      </Content>
      <Footer />
    </Layout>
  )
}

export default HomePage
