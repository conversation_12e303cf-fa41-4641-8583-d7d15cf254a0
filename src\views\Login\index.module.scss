.container {
  background-color: #f6f6f6;
  border-top-right-radius: 15px;
  border-top-left-radius: 15px;
  overflow: hidden;
  .wrapper {
    margin: 20px 12px 0;
    border-radius: 6px;

    .title {
      color: #666;
      font-size: 19px;
    }

    .way{
      margin: 0px 5px 0 8px;
      color: #4096ff;
      font-size: 20px;
      margin-bottom: 10px;
    }

    .facewrapper{
      width: 100%;
      position: relative;
      background-color: #fff;
      border-radius: 10px;
      border: 1px solid #99c4ff;
      padding: 20px 20px;
      margin-bottom: 20px;
      .face-button{
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .face-image{
        width: 22px;
        height: 22px;
        margin-right: 10px;
      }
      .btn-text{
        display: inline-block;
        font-size: 18px;
        line-height: 24px;
      }
    }

    .confirm {
      width: 100%;
      position: relative;
      background-color: #fff;
      border-radius: 10px;
      border: 1px solid #99c4ff;
      padding: 10px 10px 0;
      .title-wrapper {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 10px;
        font-size: 14px;
        .title {
          width: 80px;
          height: 20px;
          padding-right: 20px;
          text-align: justify;
          color: #979797;
          position: relative;
          & i {
            display: inline-block;
            width: 100%;
          }
          &::after {
            position: absolute;
            top: 1px;
            left: 65px;
            content: ':';
            color: #666;
            display: inline-block;
          }
        }
      }
      .tips {
        padding-top: 10px;
        margin: 5px 5px 0 8px;
        color: #4096ff;
        font-size: 16px;
        border-top: 1px solid #ebebeb;
        .facewrapper{
          padding: 10px 20px;
          margin-bottom: 10px;
        }
      }
    }

    :global {
      .ant-input {
        width: 170px;
        background-color: transparent;
        border-radius: 6px;
        // border: 1px solid #1677ff;
        font-size: 17px;
        ::placeholder {
          color: #999999;
        }
      }
      .adm-form {
        --border-top: none;
        ---border-bottom: none;
      }
      .adm-list-item {
        padding-left: 10px;
      }
      .adm-input-element {
        font-size: 18px;
      }
      .adm-list-item-content-prefix {
        width: 70px;
        font-size: 19px;
      }
    }
  }
}
