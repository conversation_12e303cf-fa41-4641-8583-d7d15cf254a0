/*
 * @Description: 翻页组件
 */
import { useState, forwardRef, useImperativeHandle, useContext } from "react";
import { Button } from 'antd-mobile'
import useDebounce from "@/common/useDebounce"; //防抖封装函数
import myContext from "@/common/context";
import styles from "./index.module.scss";

interface Props {
  max?: any,
  min?: any,
  ref?: any,
  num?: number,
  onPage?: (flag?: any) => void
}

const PageButton: React.FC<Props> = forwardRef(({
  max,
  min,
  num,
  onPage,
}, ref) => {
  const [pagenum, setPagenum] = useState<any>(num);
  const { toNextPage } = useContext<any>(myContext);

  //置为首页
  const clearSelection = () => {
    setPagenum(1)
  }
  // 暴漏给父组件
  useImperativeHandle(ref, () => ({
    clearSelection
  }));

  const handelPage = useDebounce((val: any) => {
    // onPage(val)
    toNextPage(val)
  }, 200);
  
  //下一页
  const setNext = () => {
    if (pagenum < max) {
      setPagenum(pagenum + 1); //页码
      handelPage(pagenum + 1);
    }
  };

  //上一页
  const setUp = () => {
    if (pagenum > min) {
      setPagenum(pagenum - 1); //页码
      handelPage(pagenum - 1);
    }
  };

  return (
    <div className={styles["change-page"]}>
      <Button onClick={setUp} size='small' color='primary' disabled={pagenum == 1}>
        上一页
      </Button>
      <div className={styles["page-size"]}>
        <span>第</span>
        <span>{pagenum}</span>
        <span>页</span>
      </div>
      <Button onClick={setNext} size='small' color='primary' disabled={pagenum >= max}>
        下一页
      </Button>
    </div>
  );
});

export default PageButton;
