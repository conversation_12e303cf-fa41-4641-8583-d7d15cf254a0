import React, { useEffect, useRef } from 'react'
import { HomeFilled } from '@ant-design/icons'
import { Button, ConfigProvider } from 'antd'
import { useNavigate } from 'react-router-dom'
import Logo from '@/assets/images/logo/hos-logo.png'
import DateTime from './components/DateTime.tsx'
import Config from '@/common'
import 'dayjs/locale/zh-cn'
import styles from './index.module.scss'

const Header = () => {
  const navigate = useNavigate()
  return (
    <div className={styles['header-wrapper']}>
      <div className={styles['logo-box']}>
        {/* <img src={Logo} alt='' /> */}
        {window.config.DEVICE_NAME}
      </div>
      <div className={styles['right']}>
        <div>
          <DateTime />
        </div>
        {/* {window.config.isSpeak && (
          <ConfigProvider
            theme={{
              token: {
                colorPrimary: '#32adff'
              },
              components: {
                Button: {
                  paddingInlineSM: 10,
                  paddingBlockSM: 5,
                  defaultBg: '#72c6fe',
                  defaultColor: '#fff',
                  defaultBorderColor: '#72c6fe'
                }
              }
            }}
          >
            <Button type='primary' shape='round' size='small' onClick={() => navigate('/menu')}>
              <HomeFilled />
              首页
            </Button>
          </ConfigProvider>
        )} */}
      </div>
    </div>
  )
}

export default Header
