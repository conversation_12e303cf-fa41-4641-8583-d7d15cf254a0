const menuMzdata = [{
    type: 'register',
    menu_link: '/identity?nextpage=/register/roomlist', // 路由
    name: '挂号预约',
    steps: [], // 用于存步骤条
    title_type: '0', // 首页分类类型
    icon: 'yygh-icon',
    tips: '预约挂号就诊',
    button_text: '去挂号'
  },
  {
    type: 'pay',
    menu_link: '/identity?nextpage=/pay/confirm',
    name: '门诊缴费',
    steps: [],
    title_type: '0',
    icon: 'zzjf-icon',
    tips: '医保刷脸缴费免排队',
    button_text: '去缴费'
  },
  {
    type: 'hospital_out',
    menu_link: '/identity?nextpage=/hospital/discharge',
    name: '出院结算',
    steps: [],
    title_type: '0',
    icon: 'cyjs-icon',
    tips: '办理自助出院结算',
    button_text: '去结算'
  },
  {
    type: 'emergency',
    menu_link: '/identity?nextpage=/emergency/list',
    name: '急救收费',
    steps: [],
    title_type: '1',
    icon: 'cy-icon',
    icon_filled: 'jjzf-icon',
    tips: '危重患者刷脸急救收费',
    button_text: ' 去收费',
  },
  // {
  //   type: 'emergency_query',
  //   menu_link: '/emergency/details',
  //   name: '急救记录',
  //   steps: [],
  //   title_type: '1',
  //   icon: 'rqdcx-icon',
  //   icon_filled: 'jzjl-icon',
  //   tips: '急救患者收费记录查询',
  //   button_text: '去查询'
  // },
  {
    type: 'delivery',
    menu_link: '/identity?nextpage=/delivery/list',
    name: '送药上门',
    steps: [],
    title_type: '1',
    icon: 'yp-icon',
    icon_filled: 'sysm-icon',
    tips: '慢特病药品送上门',
    button_text: '去支付'
  },
  {
    type: 'delivery',
    menu_link: '/identity?nextpage=/docsign',
    name: '家庭医生签约',
    steps: [],
    title_type: '1',
    icon: 'ysqy-icon',
    icon_filled: 'ysqy-icon',
    tips: '家庭医生上门签约',
    button_text: '去签约'
  },
  {
    type: 'delivery',
    menu_link: '/identity?nextpage=/homedischarge',
    name: '家庭病床结算',
    steps: [],
    title_type: '1',
    icon: 'jtbf-icon',
    icon_filled: 'jtbf-icon',
    tips: '家庭病床结算',
    button_text: '去结算'
  },
  {
    type: 'take',
    menu_link: '/identity?nextpage=/takenum/list',
    name: '预约取号',
    steps: [],
    title_type: '2',
    icon: 'yyqh-icon',
    icon_filled: 'yyqh-icon',
    tips: '预约挂号当日可以取号',
    button_text: '去取号'
  },
  {
    type: 'signin',
    menu_link: '/identity?nextpage=/signin/list',
    name: '门诊签到',
    steps: [],
    title_type: '2',
    icon: 'qd-icon',
    icon_filled: 'qd-icon',
    tips: '可使用多种方式进行签到',
    button_text: '去签到'
  },
  {
    type: 'yjsignin',
    menu_link: '/identity?nextpage=/signin/yjlist',
    name: '医技签到',
    steps: [],
    title_type: '2',
    icon: 'qd-icon',
    icon_filled: 'yjqd-icon',
    tips: '可使用多种方式进行医技签到',
    button_text: '去签到'
  },
  {
    type: 'yfsignin',
    menu_link: '/identity?nextpage=/signin/yflist',
    name: '药房签到',
    steps: [],
    title_type: '2',
    icon: 'qd-icon',
    icon_filled: 'yfqd-icon',
    tips: '可使用多种方式进行医技签到',
    button_text: '去签到'
  },
  // {
  //   type: 'ticket',
  //   menu_link: '/ticket/mold',
  //   name: '凭条补打',
  //   steps: [],
  //   title_type: '2',
  //   icon: 'bd-icon',
  //   icon_filled: 'bd-icon',
  //   tips: "补打结算、充值小票",
  //   button_text: '去补打'
  // },
  {
    type: 'hospital_checkin',
    menu_link: '/identity?nextpage=/hospital/checkin',
    name: '入院登记',
    steps: [],
    title_type: '3',
    icon: 'cy-icon',
    icon_filled: 'rydj-icon',
    tips: "入院快捷办理无需排队",
    button_text: '去办理'
  },
  {
    type: 'hospital_account',
    menu_link: '/identity?nextpage=/hospital/account',
    name: '住院充值',
    steps: [],
    title_type: '3',
    icon: 'zycz-icon',
    icon_filled: 'zycz-icon',
    tips: "可选用多种支付方式进行充值",
    button_text: '去充值'
  },
  {
    type: 'hospital_query',
    menu_link: '/identity?nextpage=/hospital/hosquery',
    name: '日清单查询',
    steps: [],
    title_type: '3',
    icon: 'rqdcx-icon',
    icon_filled: 'rqdcx-icon',
    tips: "查询住院每日项目清单",
    button_text: '去查询'
  },
  {
    type: 'hospital_discharge',
    menu_link: '/identity?nextpage=/hospital/discharge',
    name: '出院结算',
    steps: [],
    title_type: '3',
    icon: 'cyjs-icon',
    icon_filled: 'cyjs-icon',
    tips: "查询出院结算",
    button_text: '去结算'
  }
]

const menuZYdata = [
  {
    type: 'hospital_checkin',
    menu_link: '/identity?nextpage=/hospital/checkin',
    name: '入院登记',
    steps: [],
    title_type: '3',
    icon: 'cy-icon',
    icon_filled: 'rydj-icon',
    tips: "居家护理入院办理登记",
    button_text: '去办理'
  },
  {
    type: 'delivery',
    menu_link: '/identity?nextpage=/delivery/list',
    name: '费用录入',
    steps: [],
    title_type: '3',
    icon: 'yp-icon',
    icon_filled: 'sysm-icon',
    tips: '居家护理费用录入',
    button_text: '去支付'
  },
  {
    type: 'hospital_discharge',
    menu_link: '/identity?nextpage=/hospital/outconfirm',
    name: '出院结算',
    steps: [],
    title_type: '3',
    icon: 'cyjs-icon',
    icon_filled: 'cyjs-icon',
    tips: "居家护理出院结算",
    button_text: '去结算'
  },
  {
    type: 'hospital_query',
    menu_link: '/identity?nextpage=/hospital/hosquery',
    name: '费用查询',
    steps: [],
    title_type: '3',
    icon: 'rqdcx-icon',
    icon_filled: 'rqdcx-icon',
    tips: "居家护理录入费用查询",
    button_text: '去查询'
  },
  {
    type: 'ticket',
    menu_link: '/identity?nextpage=/ticket/list',
    name: '凭条补打',
    steps: [],
    title_type: '2',
    icon: 'ptbd-icon',
    icon_filled: 'ptbd-icon',
    tips: "补打出院结算小票",
    button_text: '去补打'
  },
]

const menuMzdata1 = [{
    type: 'emergency',
    menu_link: '/identity?nextpage=/emergency/list',
    name: '急救收费',
    steps: [],
    title_type: '1',
    icon: 'cy-icon',
    icon_filled: 'jjzf-icon',
    tips: '危重患者刷脸急救收费',
    button_text: '去收费',
  },
  {
    type: 'emergency_query',
    menu_link: '/emergency/details',
    name: '收费记录',
    steps: [],
    title_type: '1',
    icon: 'rqdcx-icon',
    icon_filled: 'jzjl-icon',
    tips: '急救患者收费记录查询',
    button_text: '去查询'
  },
]

const IMGS_TYPE = {
  "register": "drgh-icon",
  "reserve": "yygh-icon",
  "pay": "zzjf-icon",
  "signin": "qd-icon",
  "yjsignin": "qd-icon",
  "query": "zzcx-icon",
  "nuclein": "hskd-icon",
  "take": "yyqh-icon",
  "hospital_checkin": "cy-icon",
  "hospital_account": "zycz-icon",
  "hospital_query": "rqdcx-icon",
  "hospital_out": "cyjs-icon",
  "emergency": 'cy-icon',
  "emergency_query": "rqdcx-icon",
  "ticket": "bd-icon"
}

const TIPS_TYPE = {
  "emergency": "危重患者刷脸急救收费",
  "emergency_query": "急救患者收费记录查询",
  "register": "可使用多种方式进行挂号或预约",
  "reserve": "可预约未来一周的号源",
  "pay": "可使用多种支付方式进行缴费",
  "signin": "可使用多种方式进行签到",
  "yjsignin": "可使用多种方式进行医技签到",
  "query": "可查询物价药品诊疗项",
  "nuclein": "可使用多种方式进行开单",
  "take": "预约挂号当日可以取号",
  "hospital_checkin": "入院快捷办理无需排队",
  "hospital_account": "可选用多种支付方式进行充值",
  "hospital_query": "查询住院每日项目清单",
  "hospital_out": "可办理自助出院结算",
  "ticket": "补打结算、充值小票"
}

const BTNS_TYPE = {
  "register": "去挂号",
  "reserve": "去预约",
  "pay": "去缴费",
  "signin": "去签到",
  "yjsignin": "去签到",
  "query": "去查询",
  "nuclein": "去开单",
  "take": "去取号",
  "hospital_account": "去充值",
  "hospital_query": "去查询",
  "hospital_out": "去办理",
  "emergency": '去挂号',
}

export default {
  menuMzdata: menuZYdata,
  IMGS_TYPE,
  TIPS_TYPE,
  BTNS_TYPE
}
