.warm-tips-wrapper {
  text-align: center;

  .warm-img-title-box {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: calc(38px / $pixel);
    margin-bottom: calc(9px / $pixel);

    >img {
      width: calc(16px / $pixel);
      height: calc(10px / $pixel);
    }

    span {
      margin: 0 calc(14px / $pixel);
      color: #FF3141;
      font-size: calc(35px / $pixel);
      line-height: calc(40px / $pixel);
    }
  }

  >p {
    margin: calc(20px / $pixel) 0 calc(5px / $pixel);
    padding-left: calc(64px / $pixel);
    font-size: calc(32px / $pixel);
    line-height: calc(30px / $pixel);
    color: #333333;
    letter-spacing: 1.5px;
    transform: scale(0.9);
    transform-origin: 0 0;
  }
}
