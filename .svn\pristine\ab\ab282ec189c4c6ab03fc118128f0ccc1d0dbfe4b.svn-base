import { useEffect } from 'react'
import { But<PERSON>, SearchBar, CheckList } from 'antd-mobile'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { getSpeak } from '@/services/hardware'
import ModuleHeader from '@/components/ModuleHeader'
import styles from './index.module.scss'

const data = [
  {
    key: '4',
    name: '高血压'
  },
  {
    key: '7',
    name: '糖尿病'
  },
  {
    key: '10',
    name: '中度瘫痪'
  },
  {
    key: '12',
    name: '腿部骨折'
  },
  {
    key: '14',
    name: '脑部开放性创伤'
  },
  {
    key: '17',
    name: '手部骨折'
  },
  {
    key: '110',
    name: '癫痫'
  },
  {
    key: '140',
    name: '严重烫伤'
  }
]

const QueryList = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const handleReturn = () => {
    navigate(-1)
  }

  useEffect(() => {
    window.config.isSpeak && getSpeak({ content: '请选择诊断名称' })
  }, [])

  return (
    <>
      <ModuleHeader title={'诊断选择'} />
      <div className={styles['emergency-list-wrapper']}>
        <div className={styles['search-wrapper']}>
          <SearchBar
            placeholder='请输入诊断信息进行搜索'
            showCancelButton
            style={{
              '--border-radius': '100px',
              '--background': '#ffffff',
              '--height': '32px',
              '--padding-left': '12px'
            }}
          />
        </div>
        <div className={[styles['item-wrapper'], styles['scroll-style']].join(' ')}>
          <CheckList
            onChange={val => {
              let diag = {}
              if (val.length > 0) {
                diag = data.find(item => item.key === val[0])
              }
              console.log(diag)
              dispatch({
                type: 'list/setOtherData',
                payload: { ...diag }
              })
            }}
            style={{
              '--font-size': '15px'
            }}
          >
            {data.map(item => (
              <CheckList.Item key={item.key} value={item.key}>
                {item.name}
              </CheckList.Item>
            ))}
          </CheckList>
        </div>
        <div className={styles['button-wrapper']}>
          <Button color='primary' size='middle' block={true} onClick={handleReturn}>
            确认
          </Button>
        </div>
      </div>
    </>
  )
}

export default QueryList
