.container {
  // height: calc(100vh - 60px);
  background-color: #f6f6f6;
  border-top-right-radius: 15px;
  border-top-left-radius: 15px;
  overflow: hidden;
  .wrapper {
    height: calc(100vh - 135px);
    margin: 0 12px;
    border-radius: 6px;
    overflow-x: hidden;
    overflow-y: scroll;
    // border: 1px solid red;

    .confirm {
      width: 100%;
      position: relative;
      background-color: #fff;
      border-radius: 10px;
      border: 1px solid #99c4ff;
      padding: 30px 10px 0;
      margin-bottom: 10px;
      .line-wrapper {
        width: 100%;
        position: absolute;
        top: 31vh;
        left: 50%;
        transform: translateX(-50%);
        align-items: center;
        z-index: 10;
        .line {
          width: 88%;
          height: 1px;
          flex: 1;
          margin: 0 auto;
          border-bottom: 1px solid #cfcfcf;
        }
        .circle-box {
          width: 15px;
          height: 15px;
          background-color: #f6f6f6;
          border: 1px solid #99c4ff;
          border-radius: 50%;
          &.left {
            position: absolute;
            bottom: -8px;
            left: -8px;
            clip: rect(0 15px 17px 7px); //图形裁剪
          }
          &.right {
            position: absolute;
            bottom: -8px;
            right: -8px;
            clip: rect(0 8px 15px 0); //图形裁剪
          }
        }
      }
      .title-wrapper {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 10px;
        font-size: 15px;
        .title {
          width: 80px;
          height: 20px;
          padding-right: 20px;
          text-align: justify;
          color: #a6a6a6;
          position: relative;
          & i {
            display: inline-block;
            width: 100%;
          }
          &::after {
            position: absolute;
            top: 1px;
            left: 65px;
            content: ":";
            color: #666;
            display: inline-block;
          }
        }
      }

      .label {
        display: flex;
        align-items: center;

        > img {
          width: 15px;
          height: calc(38px / 2);
        }
        > span {
          line-height: calc(45px / 2);
          font-size: 16px;
          font-weight: 500;
          margin-left: 5px;
        }
      }

      .total {
        display: flex;
        justify-content: center;
        margin-top: 5px;
        align-items: center;
        font-size: 17px;
        .red-text {
          display: inline-block;
          font-size: 16px;
          color: #ff8d1a;
          margin-left: 7px;
        }
      }
      .black-txt {
        font-size: 13px;
        color: #333333;
      }
      .blue-text {
        display: inline-block;
        margin-left: 10px;
        color: #1677ff;
      }
      .red-text {
        display: inline-block;
        font-size: 13px;
        color: #ff8d1a;
        margin-left: 15px;
      }
      .info-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 280px;
        height: 100px;
        margin: 48px auto 0;
        text-align: center;
        color: #ffffff;
        border-radius: 50px;
        background-color: #1677ff;
        font-size: 42px;
      }
      .grid-demo-item-block {
        text-align: center;
      }
    }
    .input-box {
      display: flex;
      align-items: center;
    }
    .phone {
      .left {
        color: #333333;
        min-width: max-content;
        font-size: 18px;
      }
    }
    :global {
      .ant-input {
        width: 170px;
        background-color: transparent;
        border-radius: 6px;
        // border: 1px solid #1677ff;
        font-size: 14px;

        ::placeholder {
          color: #999999;
        }
      }
      .adm-text-area-element {
        padding: 5px;
        border: 1px solid #e8e8e8;
      }
      .adm-form {
        --border-top: none;
        ---border-bottom: none;
      }
      .adm-list-item-content-prefix {
        width: var(--prefix);
        flex: none;
        padding-right: var(--prefix-padding-right);
      }
      .adm-list-header {
        color: #1677ff;
        font-size: 16px;
        font-weight: bold;
        padding: 8px var(--padding-right) 8px 5px;
      }
      .adm-list-item {
        padding-left: 10px;
      }
    }
    .btn {
      width: 100%;
      margin: 34vh auto 0;
    }
  }
}
