import { useState, useEffect } from 'react'
import dayjs from 'dayjs'
import styles from '../index.module.scss'

export default function DateTimeText() {
  const [date, setDate] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setDate(new Date())
    }, 1000)
    return () => {
      clearInterval(timer)
    }
  }, [date])

  return (
    <>
      <p className={styles['time']}>{dayjs(date).format('HH:mm')}</p>
      <p className={styles['date']}>{dayjs(date).locale('zh-cn').format('YYYY-MM-DD')}</p>
    </>
  )
}
