.e-table-wrapper {
  width: 100%;
  padding: 90px;

  .table-wrapper {
    overflow: hidden;

    .table-content {
      position: relative;
      height: 826px;
      background-color: #F0F6FF;
      border-radius: 22px;
      border: 1px solid #99C4FF;
      padding: 84px 30px;
      z-index: 1;

      // overflow: hidden;
      .main-table {
        :global(.ant-table-thead) {
          >tr {
            >th {
              border-bottom: 2px solid #84B5FF !important;
              font-size: 30px;
            }
          }
        }
      }

      &::before {
        position: absolute;
        content: '';
        top: 140px;
        left: -15px;
        width: 30px;
        height: 30px;
        background-color: #FFFFFF;
        border: 1px solid #84B5FF;
        border-radius: 30px;
        z-index: 100;
        overflow: hidden;
      }

      &::after {
        position: absolute;
        content: '';
        top: 140px;
        right: -15px;
        width: 30px;
        height: 30px;
        background-color: #FFFFFF;
        border: 1px solid #84B5FF;
        border-radius: 30px;
        z-index: 100;
        overflow: hidden;
      }
    }
  }
}
