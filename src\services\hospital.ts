import request from '../utils/request'

// 住院患者信息查询
export async function getInHospitalInfo(params) {
  return request({
    url: `/jyryInHospital/getInHospitalInfo`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION },
    isHideError: true,
    isReturnErrorRes: true
  })
}

// 住院内账户充值结算
export async function rechargeSettlement(params) {
  return request({
    url: `/jyryInHospital/inHospitalRecharge`,
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE,
      apiVersion: window.config.API_VERSION
    },
    loadingText: '正在充值中请稍候...'
  })
}

// 入院登记
export async function checkInHospital(params) {
  return request({
    url: `/tzjj/RuYuanDJ`,
    method: 'POST',
    data: { ...params, deviceCode: window.config.DEVICE_CODE },
    loadingText: '入院登记办理中，请稍候...'
  })
}

// 出院结算查询
export async function getDischargeInfo(params) {
  return request({
    url: `/jyryInHospital/getDischargeInfo`,
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE,
      apiVersion: window.config.API_VERSION
    },
    isHideError: true,
    isReturnErrorRes: true
  })
}

// 预出院
export async function queryPreOutHospital(params) {
  return request({
    url: `/tzjj/YuChuYuan`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION },
    loadingText: '正在预出院，请稍候...'
  })
}

// 医保费用确认 type 0 删除费用 1 上传费用
export async function queryConfirmFee(params, isLoading) {
  return request({
    url: `/tzjj/FeiYongQR`,
    method: 'POST',
    data: { ...params, apiVersion: window.config.API_VERSION },
    isHideLoading: isLoading,
    loadingText: '医保费用上传中，请稍候...'
  })
}


// 出院预结算
export async function outHospitalPreSettlement(params) {
  return request({
    url: `/tzjj/YuJieSuan`,
    method: 'POST',
    data: { ...params, deviceCode: window.config.DEVICE_CODE },
    loadingText: '正在预结算，请稍候...'
  })
}

// 出院结算
export async function outHospitalSettlement(params) {
  return request({
    url: `/tzjj/ChuYuanJS`,
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE,
    },
    loadingText: '正在结算中，请稍候...'
  })
}

// 住院患者费用清单查询
export async function queryCostDetail(params, isLoading = false) {
  return request({
    url: `/tzjj/FeiYongMX`,
    method: 'POST',
    data: {
      ...params,
      deviceCode: window.config.DEVICE_CODE
    },
    loadingText: '费用明细查询中，请稍候...',
    isHideLoading: isLoading,
  })
}

// 入院记录查询
export async function getInHospitalRecording(params) {
  return request({
    url: `/jyryInHospital/getInHospitalRecording`,
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    },
    isHideError: true,
    isReturnErrorRes: true
  })
}

// 查询发票信息
export async function getInvoice(params) {
  return request({
    url: `/jyryInHospital/getInvoice`,
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    },
    isHideError: true
  })
}

// 查询发票详情信息
export async function getInvoiceDetail(params) {
  return request({
    url: `/jyryInHospital/getInvoiceDetail`,
    method: 'POST',
    data: {
      ...params,
      apiVersion: window.config.API_VERSION,
      deviceCode: window.config.DEVICE_CODE
    },
    isHideError: true
  })
}


// 获取诊断
export async function getDiagnosis(params: any) {
  return request({
    url: '/tzjj/selectDiagnosis',
    method: 'POST',
    data: { ...params, deviceCode: window.config.DEVICE_CODE }
  })
}

// 获取诊疗项
export async function getPrescription(params: any) {
  return request({
    url: '/tzjj/selectDrugs',
    method: 'POST',
    data: { ...params, deviceCode: window.config.DEVICE_CODE }
  })
}

// 费用录入
export async function inputCharge(params: any) {
  return request({
    url: '/tzjj/FeiYongLR',
    method: 'POST',
    data: { ...params, deviceCode: window.config.DEVICE_CODE },
    loadingText: '费用信息提交中，请稍候...',
    isReturnErrorRes: true
  })
}

// 费用删除
export async function delateCharge(params: any) {
  return request({
    url: '/tzjj/FeiYongTF',
    method: 'POST',
    data: { ...params, deviceCode: window.config.DEVICE_CODE },
    loadingText: '费用信息删除中，请稍候...',
  })
}