import React, { useState } from 'react';
import { Button, Card, Space, Toast } from 'antd-mobile';
import { MessageOutline, UserContactOutline, PhoneOutline } from 'antd-mobile-icons';
import ModuleHeader from '@/components/ModuleHeader';
import ChatModal from '@/components/ChatModal';
import ChatWindow from '@/components/ChatWindow';
import styles from './index.module.scss';

// 模拟医生数据
const mockDoctor = {
  id: '1',
  name: '张医生',
  specialty: '内科主任医师',
  avatar: '/api/placeholder/60/60',
  status: '在线',
  introduction: '从事内科临床工作20年，擅长心血管疾病、糖尿病等慢性病的诊治。'
};

const ChatExample: React.FC = () => {
  const [chatModalVisible, setChatModalVisible] = useState(false);
  const [chatWindowVisible, setChatWindowVisible] = useState(false);

  // 开始咨询
  const handleStartConsultation = () => {
    setChatModalVisible(true);
    Toast.show('正在连接医生...');
  };

  // 打开聊天窗口
  const handleOpenChatWindow = () => {
    setChatWindowVisible(true);
  };

  // 拨打电话（模拟）
  const handleCallDoctor = () => {
    Toast.show('电话咨询功能暂未开放');
  };

  return (
    <div className={styles.chatExample}>
      <ModuleHeader title="在线咨询" />
      
      <div className={styles.content}>
        {/* 医生信息卡片 */}
        <Card className={styles.doctorCard}>
          <div className={styles.doctorInfo}>
            <div className={styles.avatar}>
              <img src={mockDoctor.avatar} alt={mockDoctor.name} />
              <div className={styles.statusBadge}>
                {mockDoctor.status}
              </div>
            </div>
            <div className={styles.info}>
              <div className={styles.name}>{mockDoctor.name}</div>
              <div className={styles.specialty}>{mockDoctor.specialty}</div>
              <div className={styles.introduction}>
                {mockDoctor.introduction}
              </div>
            </div>
          </div>
        </Card>

        {/* 咨询方式选择 */}
        <Card title="选择咨询方式" className={styles.consultationCard}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              color="primary"
              size="large"
              block
              onClick={handleStartConsultation}
              className={styles.consultationButton}
            >
              <MessageOutline />
              <span>图文咨询</span>
              <span className={styles.price}>免费</span>
            </Button>
            
            <Button
              color="success"
              size="large"
              block
              onClick={handleOpenChatWindow}
              className={styles.consultationButton}
            >
              <UserContactOutline />
              <span>专家咨询</span>
              <span className={styles.price}>¥50</span>
            </Button>
            
            <Button
              color="warning"
              size="large"
              block
              onClick={handleCallDoctor}
              className={styles.consultationButton}
            >
              <PhoneOutline />
              <span>电话咨询</span>
              <span className={styles.price}>¥100</span>
            </Button>
          </Space>
        </Card>

        {/* 温馨提示 */}
        <Card title="温馨提示" className={styles.tipsCard}>
          <div className={styles.tips}>
            <div className={styles.tipItem}>
              <span className={styles.tipIcon}>💡</span>
              <span>请详细描述您的症状，以便医生更好地为您诊断</span>
            </div>
            <div className={styles.tipItem}>
              <span className={styles.tipIcon}>📷</span>
              <span>可以上传相关检查报告或症状图片</span>
            </div>
            <div className={styles.tipItem}>
              <span className={styles.tipIcon}>⏰</span>
              <span>医生通常在5分钟内回复，请耐心等待</span>
            </div>
            <div className={styles.tipItem}>
              <span className={styles.tipIcon}>🔒</span>
              <span>您的隐私信息将得到严格保护</span>
            </div>
          </div>
        </Card>

        {/* 常见问题 */}
        <Card title="常见问题" className={styles.faqCard}>
          <div className={styles.faqList}>
            <div className={styles.faqItem}>
              <div className={styles.question}>Q: 咨询费用如何收取？</div>
              <div className={styles.answer}>A: 图文咨询免费，专家咨询和电话咨询按次收费。</div>
            </div>
            <div className={styles.faqItem}>
              <div className={styles.question}>Q: 医生多久会回复？</div>
              <div className={styles.answer}>A: 在线医生通常在5分钟内回复，忙碌时可能稍有延迟。</div>
            </div>
            <div className={styles.faqItem}>
              <div className={styles.question}>Q: 可以上传图片吗？</div>
              <div className={styles.answer}>A: 可以，支持上传检查报告、症状图片等，最大5MB。</div>
            </div>
          </div>
        </Card>
      </div>

      {/* 聊天模态框 */}
      <ChatModal
        visible={chatModalVisible}
        onClose={() => setChatModalVisible(false)}
        doctorId={mockDoctor.id}
        doctorName={mockDoctor.name}
        sessionId={`session_${Date.now()}`}
        modalCloseNum={60} // 60秒后自动关闭
      />

      {/* 聊天窗口 */}
      <ChatWindow
        visible={chatWindowVisible}
        onClose={() => setChatWindowVisible(false)}
        doctorId={mockDoctor.id}
        doctorName={mockDoctor.name}
        sessionId={`session_${Date.now()}`}
      />
    </div>
  );
};

export default ChatExample;
