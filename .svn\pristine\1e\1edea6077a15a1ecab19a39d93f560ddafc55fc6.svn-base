import { NavLink } from 'react-router-dom'
import Config from '@/common'
import styles from '../index.module.scss'

const BigButtonItem = ({ item, findUrl, class_name }) => {
  return (
    <NavLink to={findUrl(item.name)}>
      <div className={[styles['big-button-wrapper'], styles[`${class_name}`]].join(' ')}>
        <div className={styles['top']}>
          <div className={styles['icon-box-1']}>
            {item.icon ? (
              <img
                className={styles['menu-icon']}
                src={require(`../../../assets/images/icons/${item.icon}.png`)}
                alt='图标'
              />
            ) : (
              <img className={styles['menu-icon']} src='' alt='图标' />
            )}
          </div>
          <div className={styles['title-box']}>
            <h2 className={styles['title']}>{item.name}</h2>
            {/* <p className="tips">{Config.TIPS_TYPE[item.type]}</p> */}
          </div>
          <div className={styles['right-btn']}>
            <span>{item.button_text}</span>
            <img alt='向右箭头小图标' src={require('@/assets/images/btns/right-arrow.png')} />
          </div>
        </div>
        <p className={styles['attention']}>{item.tips}</p>
      </div>
    </NavLink>
  )
}

export default BigButtonItem
