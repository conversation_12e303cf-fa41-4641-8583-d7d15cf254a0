import { Row, Col } from 'antd'
import BigButtonItem from './BigButtonItem'
import MidButtonItem from './MidButtonItem'
import SmButtonItem from './SmButtonItem'
import styles from '../index.module.scss'

const MenuFour = ({ data, findUrl }) => {
  const data1 = data?.[0] ?? {}
  const data2 = data?.[1] ?? {}
  const data3 = data?.[2] ?? {}
  const data4 = data?.[3] ?? {}
  return (
    <div className={styles['menu4']}>
      <div className={styles['menu4-mid-wrapper']}>
        <Row justify='space-between' gutter={[7, 12]}>
          {data.map((item, index) => {
            return (
              <Col span={12} key={index}>
                <MidButtonItem item={item} class_name={'bg' + index} findUrl={findUrl} />
              </Col>
            )
          })}
        </Row>
      </div>
    </div>
  )
}

export default MenuFour
