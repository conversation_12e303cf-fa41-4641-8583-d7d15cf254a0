import { useState, useEffect } from 'react'
import { Button } from 'antd-mobile'
import dayjs from 'dayjs'
import styles from './index.module.scss'
import { NavBar, JumboTabs } from 'antd-mobile'
import { useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { IdNoPrivate } from '@/utils/utils'
import { RootState } from '@/store'
import { getSpeak } from '@/services/hardware'
import BlueBgTitle from '@/components/BlueBgTitle'
import ETipModal from '@/components/ETipModal'
import gotoMenu from '@/utils/gotoMenu'

const DoctorList = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { currentUser, readCardData } = useSelector((state: RootState) => state.userInfo)

  useEffect(() => {
    window.config.isSpeak && getSpeak({ content: '请选择要取号的预约记录' })
  }, [])

  const handleSignIn = () => {
    navigate('/takenum/confirm')
  }

  return (
    <div className={styles['doctor-wrapper']}>
      <div className={styles['header']}>
        <NavBar back='返回' onBack={() => navigate(-1)} style={{ color: '#fff' }}>
          {'预约取号'}
        </NavBar>
      </div>
      <div className={styles['info-wrapper']}>
        <BlueBgTitle title={'就诊人信息'}></BlueBgTitle>
        <div className={styles['info-box']}>
          <div className={styles['item']}>
            <span>姓名：</span>
            {currentUser.patientName}
          </div>
          <div className={styles['item']}>
            <span>证件号：</span>
            {IdNoPrivate(currentUser.patientIDCard)}
          </div>
          <div className={styles['item']}>
            <span>手机号：</span>
            {currentUser.phone}
          </div>
        </div>
      </div>
      <div className={styles['sign-list-wrapper']}>
        <div className={styles['doctor-schdule']} onClick={() => handleSignIn()}>
          <div className={styles['doctor-info']}>
            <img
              src={require('@/assets/images/others/default-doctor.png')}
              alt='医生照片'
              className={styles['doctor-image']}
            />
            <div className={styles['right']}>
              <div className={styles['name-box']}>
                <span className={styles['name']}>杜鹃</span>
                <span className={styles['job']}>主任医师</span>
              </div>
              <div className={styles['content']}>
                <span className=''>消化内科</span>
              </div>
              <div className={styles['content']}>
                <span className=''>{`${dayjs().format('YYYY-MM-DD')} 下午`}</span>
              </div>
            </div>
          </div>
          <div className={styles['button-wrapper']}>
            <Button color='primary' shape='rounded' block size='small'>
              取号
            </Button>
          </div>
        </div>
        <div className={styles['doctor-schdule']} onClick={() => handleSignIn()}>
          <div className={styles['doctor-info']}>
            <img
              src={require('@/assets/images/others/default-doctor.png')}
              alt='医生照片'
              className={styles['doctor-image']}
            />
            <div className={styles['right']}>
              <div className={styles['name-box']}>
                <span className={styles['name']}>沈泽</span>
                <span className={styles['job']}>副主任医师</span>
              </div>
              <div className={styles['content']}>
                <span className=''>中医科</span>
              </div>
              <div className={styles['content']}>
                <span className=''>{`${dayjs().add(1, 'days').format('YYYY-MM-DD')} 上午`}</span>
              </div>
            </div>
          </div>
          <div className={styles['button-wrapper']}>
            <Button color='primary' shape='rounded' block size='small' disabled>
              取号
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DoctorList
