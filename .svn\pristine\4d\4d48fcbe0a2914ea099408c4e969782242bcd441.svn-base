@import '@/assets/scss/base.scss';

.wrapper{
  height: calc(100vh - 130px);
}

.confirm-page-wrapper {
  width: calc(680px / $pixel);
  margin: 0 auto;
  overflow-x: hidden;
  .label {
    display: flex;
    align-items: center;
    
    img {
      width: 17px;
      margin-right: 3px;
    }
    span {
      line-height: calc(45px / 2);
      font-size: 18px;
      font-weight: 500;
    }
  }
}

.confirm-info-list-wrapper {
  position: relative;
  width: 100%;
  padding: calc(0px / $pixel) calc(20px / $pixel);
  background-color: #FFF;
  border-radius: calc(22px / $pixel);
  border: 1px solid #99C4FF;

  &::before {
    position: absolute;
    content: '';
    left: calc(-15px / $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &::after {
    position: absolute;
    content: '';
    right: calc(-15px/ $pixel);
    width: calc(27px/ $pixel);
    height: calc(27px/ $pixel);
    background-color: #F6F6F6;
    border: 1px solid #84B5FF;
    border-radius: calc(27px/ $pixel);
    z-index: 100;
    overflow: hidden;
  }

  &.info {

    &::before,
    &::after {
      top: calc(40px/ $pixel);
    }
  }

  .info-box {
    padding: calc(70px / $pixel) 0 calc(30px / $pixel);
    font-size: calc(36px / $pixel);
    line-height: calc(60px / $pixel);
    color: #383838;
    font-weight: medium;
    border-bottom: 1px solid #CFCFCF;
    position: relative;

    .item {
      span {
        display: inline-block;
        min-width: calc(130px / $pixel);
        margin-right: calc(30px / $pixel);
        color: #000;
        font-weight: medium;

        &.number {
          color: #383838;
          font-weight: 600;
          font-size: calc(36px / $pixel);
        }
      }
    }
  }

  .clear-btn{
    text-align: right;
    color: #1677ff;
    font-size:16px;
    padding: 0 10px 10px;
  }
}

:global {
  .ant-input {
    width: 170px;
    background-color: transparent;
    border-radius: 6px;
    // border: 1px solid #1677ff;
    font-size: 14px;
    ::placeholder {
      color: #999999;
    }
  }
  .adm-text-area-element {
    padding: 5px;
    border: 1px solid #e8e8e8;
  }
  .adm-form {
    --border-top: none;
    ---border-bottom: none;
  }
  .adm-list-item-content-prefix {
    width: var(--prefix);
    flex: none;
    padding-right: var(--prefix-padding-right);
  }
  .adm-list-header {
    color: #1677ff;
    font-size: 18px;
    font-weight: bold;
    padding: 10px 0;
  }
  .adm-list-item {
    padding-left: 10px;
  }
  .adm-form-item.adm-form-item-vertical .adm-form-item-label {
    font-size: 18px;
    margin-bottom: 4px;
    font-weight: 600;
  }
}