import { IdNoPrivate } from '@/utils/utils'
// 打印小票的方法
export default function getDischargePrintData (currentUser, outHopitalInfo) {

    // 打印参数字段
    const data = [
        {
            "text": window.config.HOS_NAME,
            "size": 30,
            "isBold":true,
            "isUnderLine":false,
            "align": 1,
        },
        {
            "text": '居家护理出院结算凭证',
            "size": 18,
            "isBold":true,
            "isUnderLine":false,
            "align": 1,
        },
        "-----------------------",
        `患者姓名: ${currentUser?.patientName ?? ""}`,
        `证件号: ${IdNoPrivate(currentUser?.idCard)}`,
        `病案号: ${currentUser.patientID}`,
        "-----------------------",
        `住院费用: ${outHopitalInfo?.FeiYongZE}`,
        `医保支付: ${outHopitalInfo?.YiBaoBXJE}`,
        `优惠金额: ${outHopitalInfo?.YouHuiJE}`,
        `自费支付: ${outHopitalInfo?.YingFuJE}`,
        "-----------------------",
    ]

    return data;
}