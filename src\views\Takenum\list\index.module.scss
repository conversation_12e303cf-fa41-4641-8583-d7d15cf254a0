.doctor-wrapper {
  height: calc(100vh - 50px);
  // background-image: linear-gradient(#2493F1, #A8D7FF);
  // background-size: 100% calc(260px / $pixel);
  // background-repeat: no-repeat;
  position: relative;
  z-index: 1;

  &::after {
    content: "";
    width: 140%;
    height: calc(260px / $pixel);
    position: absolute;
    left: -20%;
    top: 0;
    border-radius: 0 0 60% 60%;
    background: linear-gradient(#2493F1, #A8D7FF);
    z-index: -1;
  }

  .info-wrapper {
    position: relative;
    background-color: #fff;
    width: calc(670px / $pixel);
    margin: calc(30px / $pixel) auto 0;
    border-radius: calc(16px / $pixel);
    padding: calc(20px / $pixel) calc(20px / $pixel) 0;

    .info-box {
      padding: calc(20px / $pixel) 0;
      font-size: calc(28px / $pixel);
      line-height: calc(56px / $pixel);
      color: #383838;
      font-weight: 500;

      .item {
        span {
          display: inline-block;
          min-width: calc(100px / $pixel);
          margin-right: calc(30px / $pixel);
          color: #A6A6A6;
        }
      }
    }
  }

  .sign-list-wrapper {
    width: calc(670px / $pixel);
    margin: calc(20px / $pixel) auto 0;

    .doctor-schdule {
      border-radius: calc(16px / $pixel);
      margin-top: calc(20px / $pixel);
      padding: calc(30px / $pixel) calc(10px / $pixel);
      background-color: #FFFFFF;
      display: flex;
      align-items: center;

      .doctor-info {
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0 calc(28px / $pixel);
        border-radius: calc(14px / $pixel);
        flex: 1;

        .doctor-image {
          width: calc(85px / $pixel);
          height: calc(105px / $pixel);
          border-radius: 50%;
          margin-right: calc(45px / $pixel);
        }

        .name-box {
          display: flex;
          align-items: center;
          margin-bottom: calc(12px / $pixel);

          .name {
            font-size: calc(34px / $pixel);
            line-height: calc(40px / $pixel);
            color: #333333;
            margin-bottom: 0;
            margin-right: 5px;
          }

          .job {
            height: calc(30px / $pixel);
            padding: 0 calc(16px / $pixel);
            border: 1px solid #1677FF;
            border-radius: calc(10px / $pixel);
            line-height: calc(26px / $pixel);
            font-size: calc(20px / $pixel);
            color: #1677FF;
          }
        }

        .content {
          font-size: calc(30px / $pixel);
        }
      }

      .button-wrapper {
        width: calc(150px / $pixel);
        font-size: calc(35px / $pixel);
        padding-right: calc(20px / $pixel);
      }
    }
  }
}
