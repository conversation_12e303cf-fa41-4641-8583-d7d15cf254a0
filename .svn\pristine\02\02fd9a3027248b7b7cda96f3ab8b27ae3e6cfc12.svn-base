/*
 * @Author: lhy
 * @Date: 2022-07-18 09:05:17
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-09-06 15:07:52
 * @FilePath: \源码4网络打印\src\utils\getSerialNo.js
 * @Description:
 */
import { getDeviceDetail } from '../services/device'

/**
 * @description: 根据SN号查询设备信息和设备类型
 * @param {*} fields
 * @return {*}
 */

export const handleGetDeviceDetail = async fields => {
  try {
    const response = await getDeviceDetail({
      ...fields
    })
    if (response.code === '0') {
      return response.data
    }
    return false
  } catch (error) {
    return false
  }
}

// 获取设备 SN 号
export const getSerialNo = () => {
  console.log('获取设备SN号')
  if (window.config.isOpenRealReadId) {
    window.YW.getSerialNumber({
      success: res => {
        console.log('sn号:' + JSON.stringify(res))
        window.config.SERIAL_NO = res?.serialNumber
        // 查询设备信息
        const result = handleGetDeviceDetail({ deviceCode: res.serialNumber })
        result.then(res => {
          if (!res) return
          const { deviceInfo } = res
          window.config.HIS_DEVICE_CODE = deviceInfo?.hisDeviceCode ?? ''
          window.config.DEVICE_CODE = deviceInfo?.deviceCode ?? ''
          window.config.DEVICE_TYPE = deviceInfo?.deviceType ?? ''
          window.config.SPBILL_CREATE_IP = deviceInfo?.deviceIP ?? ''
          window.config.SAID = deviceInfo?.saID ?? ''
          window.config.HOS_NAME = deviceInfo?.hospitalName ?? ''
        })
      },
      fail: err => {
        console.log('fail', JSON.stringify(err))
      }
    })
  }else{
    // 查询设备信息
    const result = handleGetDeviceDetail({ deviceCode: 'PH28246E90052' })
    result.then(res => {
      if (!res) return
      const { deviceInfo } = res
      window.config.HIS_DEVICE_CODE = deviceInfo?.hisDeviceCode ?? ''
      window.config.DEVICE_CODE = deviceInfo?.deviceCode ?? ''
      window.config.DEVICE_TYPE = deviceInfo?.deviceType ?? ''
      window.config.SPBILL_CREATE_IP = deviceInfo?.deviceIP ?? ''
      window.config.SAID = deviceInfo?.saID ?? ''
    })
  }
}
