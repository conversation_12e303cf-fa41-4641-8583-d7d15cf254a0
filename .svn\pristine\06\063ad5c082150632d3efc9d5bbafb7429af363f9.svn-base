@import '../../../assets/scss/base.scss';

.emergency-list-wrapper {
  height: calc(100vh - 280px / $pixel);
}

.date-box {
  margin: calc(20px / $pixel);
  background-color: #fff;
  padding: calc(20px / $pixel) calc(20px / $pixel) calc(20px / $pixel) calc(20px / $pixel);
  border-radius: calc(16px / $pixel);
  color: #383838;

  .label {
    display: flex;
    align-items: center;

    >img {
      width: calc(30px / $pixel);
      height: calc(35px / $pixel);
    }

    >span {
      line-height: calc(45px / $pixel);
      font-size: calc(30px / $pixel);
      font-weight: 500;
      margin-left: calc(20px / $pixel);
    }
  }

  .content {
    text-align: center;
    margin-top: calc(10px / $pixel);

    >span {
      line-height: calc(45px / $pixel);
      font-size: calc(30px / $pixel);
      font-weight: 500;
      margin: 0 calc(20px / $pixel);
    }
  }
}

.patient-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: calc(20px / $pixel);
  background-color: #fff;
  padding: calc(20px / $pixel) calc(20px / $pixel) calc(20px / $pixel) calc(20px / $pixel);
  border-radius: calc(16px / $pixel);
  color: #383838;

  .label {
    display: flex;
    align-items: center;

    >img {
      width: calc(30px / $pixel);
      height: calc(35px / $pixel);
    }

    >span {
      line-height: calc(45px / $pixel);
      font-size: calc(30px / $pixel);
      font-weight: 500;
      margin-left: calc(20px / $pixel);
    }
  }

  .content {
    text-align: center;

    >span {
      line-height: calc(45px / $pixel);
      font-size: calc(30px / $pixel);
      font-weight: 500;
      margin-left: calc(20px / $pixel);
    }
  }
}

.info-list {
  height: calc(100vh - 550px / $pixel);
  padding: 0 calc(25px / $pixel);

  .info-box {
    font-size: calc(28px / $pixel);
    line-height: calc(56px / $pixel);
    color: #383838;
    font-weight: 500;
    padding-bottom: calc(20px / $pixel);

    .item {
      span {
        display: inline-block;
        min-width: calc(100px / $pixel);
        margin-right: calc(30px / $pixel);
        color: #A6A6A6;
      }
    }
  }

  .status-icon {
    position: absolute;
    top: calc(20px / $pixel);
    right: calc(20px / $pixel);
    width: calc(160px / $pixel);
    font-size: calc(25px / $pixel);
  }

  :global(.adm-list-body) {
    border-radius: calc(16px / $pixel);
  }

  :global(.adm-list-item-content-main) {
    padding: 5px 0;
  }

  :global(.adm-list-body-inner) {
    margin-top: calc(-20px / $pixel);
    background-color: #F6F6F6;
  }

  :global(.adm-list-item) {
    margin-top: calc(20px / $pixel);
    border-radius: calc(16px / $pixel);
    position: relative;
  }
}
