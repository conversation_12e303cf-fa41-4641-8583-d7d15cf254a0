.identity-item {
  position: relative;
  text-align: center;
  margin-bottom: calc(45px / $pixel);

  &.pay {
    -webkit-animation: free_download 0.8s linear alternate infinite;
    animation: free_download 0.8s linear alternate infinite;
  }

  .card-img {
    width: calc(312px / $pixel);
    height: calc(183px / $pixel);
    margin-bottom: calc(33px / $pixel);
  }

  .text {
    text-align: center;

    span {
      font-size: calc(40px / $pixel);
      line-height: calc(34px / $pixel);
      color: #1C1C1C;
      font-weight: bold;
      letter-spacing: 1px;
    }
  }

  .tuijian-box {
    position: absolute;
    right: calc(-8px / $pixel);
    top: calc(-8px / $pixel);

    >img {
      width: calc(100px / $pixel);
      height: calc(100px / $pixel);
    }
  }
}

@-webkit-keyframes free_download {
  0% {
    -webkit-transform: scale(0.93);
  }

  100% {
    -webkit-transform: scale(1.05);
  }
}

@keyframes free_download {
  0% {
    transform: scale(0.93);
  }

  100% {
    transform: scale(1.05);
  }
}
