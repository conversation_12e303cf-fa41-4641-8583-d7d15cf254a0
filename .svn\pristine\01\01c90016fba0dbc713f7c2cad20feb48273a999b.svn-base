import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import { Provider } from 'react-redux'
import store from './store/index'
import './utils/vconsole'
import { getSerialNo } from './utils/getSerialNo.ts'

if (window.config.isOpenRealReadId) {
  // 初始化IOT组件
  window.YW.init({
    appId: '9105351747773885834584750',
    isvId: '91053440677',
    serverPublicKey:
      '0425E532B0333F40C2E8B4905D2AE8A31E690CDBA12E0EE20B85888DC73FA6D7EDEA86450A1459B94705CE77BF305D91666F5B8FF85CB8315F8D1CD4744F664955',
    isvPrivateKey: '00DBFEEAB871B0F8A0A218AE9E0A73AEC0B8C016772B9F42D8A6BF70FFB0DE0501',
    isvSecretKey: '61233330393131383236363539393539',
    success: res => {
      console.log('success', res)
      // 设置无操作返回时间
      window.YW.setTouchTimeout({
        noTouchTimeOut: window.config.NO_TOUCH_TIME_OUT,
        secondCheckTimeout: 30
      })
      // 初始化成功后获取设备号
      getSerialNo()
    },
    fail: err => {
      console.log('fail', err)
    }
  })
} else {
  getSerialNo()
}

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  // <React.StrictMode>
  <Provider store={store}>
    <App />
  </Provider>
  // </React.StrictMode>
)
