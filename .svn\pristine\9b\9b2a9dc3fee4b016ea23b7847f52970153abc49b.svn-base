.identity-wrapper {

  .title {
    margin-top: calc(44px / $pixel);
    margin-bottom: calc(115px / $pixel);
    color: #333333;
    font-size: calc(48px / $pixel);
    line-height: calc(70px / $pixel);
    text-align: center;
  }

  .content-center {
    width: $width660;
    margin: 0 auto;
  }

  .brush-face-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: calc(660px / $pixel);
    height: calc(180px / $pixel);
    background: url("@/assets/images/others/identity-brush-face-btn-bg.png") no-repeat center;
    background-size: 100% 100%;
    box-shadow: 0 6px 30px 0 #A3CBF4;
    margin: 0 auto calc(60px / $pixel);
    position: relative;
    border-radius: calc(24px / $pixel);

    >img {
      width: calc(112px / $pixel);
      height: calc(112px / $pixel);
      margin-right: calc(40px / $pixel);
    }

    .text {
      font-size: calc(70px / $pixel);
      line-height: calc(108px / $pixel);
      font-weight: 500;
      letter-spacing: calc(8px / $pixel);
      color: #FFFFFF;
    }

    .tuijian-box {
      position: absolute;
      right: calc(-14px / $pixel);
      top: calc(-14px / $pixel);

      >img {
        width: calc(130px / $pixel);
        height: calc(130px / $pixel);
      }
    }
  }

  // 扫码或手输门诊号
  .sm-ss-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: calc(660px / $pixel);
    height: calc(180px / $pixel);
    background: url("@/assets/images/others/green-btn-bg.png") no-repeat center;
    // background: linear-gradient(to right, #);
    background-size: 100% 100%;
    box-shadow: 0 6px 25px 0 #A3CBF4;
    margin: 0 auto calc(170px / $pixel);

    .sm-icon {
      width: calc(74px / $pixel);
      height: calc(72px / $pixel);
      margin-right: calc(14px / $pixel);
    }

    .ss-icon {
      width: calc(62px / $pixel);
      height: calc(74px / $pixel);
      margin-right: calc(30px / $pixel);
    }

    .text {
      font-size: calc(50px / $pixel);
      line-height: calc(72px / $pixel);
      font-weight: 600;
      color: #FFFFFF;
    }
  }

  .more-title-img {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: calc(660px / $pixel);
    height: calc(36px / $pixel);
    margin: 0 auto calc(50px / $pixel);

    >img {
      width: calc(200px / $pixel);
      height: 1px;
    }

    >span {
      font-size: calc(34px / $pixel);
      line-height: calc(36px / $pixel);
      color: #999999;
      // margin: 0 calc(20px / $pixel);
    }
  }


}
