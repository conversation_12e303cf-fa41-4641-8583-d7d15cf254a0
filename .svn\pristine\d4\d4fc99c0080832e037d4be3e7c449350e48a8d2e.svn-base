/*
 * @Description: 两个按钮功能
 */
import BigButtonItem from './BigButtonItem'
import styles from '../index.module.scss'

const styleName = ['bgblue', 'bggreen', 'bgred']
const MenuTwo = ({ data, findUrl }) => {
  return (
    <div className={styles['menu2']}>
      {data.map((item, index) => {
        return (
          <BigButtonItem key={index} item={item} class_name={styleName[index]} findUrl={findUrl} />
        )
      })}
    </div>
  )
}

export default MenuTwo
