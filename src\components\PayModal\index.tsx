import { Modal, QRCode, ConfigProvider } from 'antd'
import ModalCloseItem from '../ModalCloseItem'
import { useTheme } from 'antd-style'
import { PositionType } from 'antd/es/image/style'
import styles from './index.module.scss'

const codeIcons = [
  'https://pic.imgdb.cn/item/631adf4a16f2c2beb1748d7d.png',
  'https://pic.imgdb.cn/item/631954d316f2c2beb1c94187.png',
  'https://pic.imgdb.cn/item/631a9e4316f2c2beb124add5.png',
  'https://pic.imgdb.cn/item/63842be216f2c2beb1162d4a.png'
]

type ModelProps = {
  modalVisible: boolean
  onCancel: () => void
  modalCloseNum?: number
  payParams: {
    patientName: string
    codeUrl: string
    money: string | number
  }
  modalType: number
  payModeType: '1' | '2'
}

const typeTextArr = ['支付宝或微信', '微信', '支付宝', '数字人民币']
const imageArr = ['pay-wx-alipay', 'pay-wx', 'pay-alipay', '']

const PayModal: React.FC<ModelProps> = ({
  modalVisible,
  onCancel,
  payParams,
  modalType,
  payModeType
}) => {
  const token = useTheme()
  const modalStyles = {
    header: {},
    body: {
      paddingTop: '20px',
      minHeight: '250px'
    },
    mask: {
      position: 'absolute' as PositionType
    },
    wrapper: {
      position: 'absolute' as PositionType
    },
    footer: {
      display: 'flex',
      justifyContent: 'space-around',
      paddingBottom: '20px'
    },
    content: {
      borderRadius: 16,
      minHeight: '280px',
      padding: 8
    }
  }

  return (
    <Modal
      destroyOnClose
      open={modalVisible}
      title={null}
      footer={null}
      maskClosable={false}
      closable={false}
      centered
      onCancel={() => onCancel()}
      width={`${660 / window.config.minPixelValue}px`}
      getContainer={document.getElementById('_module') as HTMLElement}
      styles={modalStyles}
    >
      {/* <ModalCloseItem num={window.config.PAY_OVER_TIME} onCancel={onCancel} /> */}
      <h2 className={styles['patient-text']}>
        患者姓名：<span>{payParams.patientName}</span>&nbsp;&nbsp;|&nbsp;&nbsp;支付金额：
        <span className='money'>{payParams.money}元</span>
      </h2>
      <div className={styles['pay-code-wrapper']}>
        {payModeType === '2' && (
          <img
            src={require(`../../assets/images/gifs/${imageArr[modalType]}.gif`)}
            alt='付钱动图'
          />
        )}
        {payModeType === '1' && (
          <QRCode
            size={120}
            value={payParams.codeUrl}
            icon={require('../../assets/images/pay/jh-pay.png')}
          />
        )}
      </div>
      <p className={styles['pay-tips-text']}>
        请打开{typeTextArr[modalType]}APP
        <br />
        {payModeType === '1' ? '扫描' : '展示'}二维码支付
      </p>
    </Modal>
  )
}

export default PayModal
