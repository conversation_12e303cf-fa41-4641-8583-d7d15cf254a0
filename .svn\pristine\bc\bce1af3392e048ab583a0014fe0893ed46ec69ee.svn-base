import request from './request'

// 多接口联合请求
export default async function multiRequest (options, dataList) {
	console.log('多接口联合请求', options, dataList)
	const data = dataList.map((item: any) => {
		if(options.method === 'POST'){
			Object.assign(options.data, item)
		}else if (options.method === 'GET'){
			Object.assign(options.params, item)
		}
		return request(options).then(res => { return res.data })
	})
	const list = await Promise.all(data)
	return list
}
